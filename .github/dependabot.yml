# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://help.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: pip  # See documentation for possible values
    directories: [/, /services, /services/serverless/apps, /infrastructure/aws]
    schedule:
      interval: daily
    rebase-strategy: disabled
    groups:
      all:
        patterns: ['*']
    ignore:
      - dependency-name: boto3*
      - dependency-name: pytest-asyncio*
      - dependency-name: aws-cdk-lib*
      - dependency-name: azure-monitor-opentelemetry*
  - package-ecosystem: gomod
    directory: /golang/wxcache
    rebase-strategy: disabled
    schedule:
      interval: daily
    groups:
      all:
        patterns: ['*']
    ignore:
      - dependency-name: '*'
        update-types: [version-update:semver-patch]
