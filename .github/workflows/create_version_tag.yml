name: Create version tag
on:
  push:
    branches: [develop]
  workflow_dispatch:

jobs:
  Bump-version:
    name: Create version tag
    runs-on: ubuntu-latest
    steps:
      - name: Echo ref
        run: |
          echo ${{ github.ref }}
          echo ${{ github.event_name }}

      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: 3.13
      - name: Setup git config
        run: |
          git config --global user.name "GitHub Actions Bot"
          git config --global user.email "<>"
      - name: Add tag with current version
        run: |-
          git remote set-url origin https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/$GITHUB_REPOSITORY
          git checkout $GITHUB_HEAD_REF
          git fetch --all --tags
          previous_version=$(git describe --tags --abbrev=0 --always)
          new_version=$(python bump_version.py $previous_version)
          git tag $new_version
          git push origin $new_version
