name: Sync OpenAPI to ReadMe
on:
  push:
    branches: [develop]
  workflow_dispatch:

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Sync OpenAPI definition to ReadMe
        uses: readmeio/rdme@v9
        with:
          rdme: openapi ./services/data_service/openapi-stable.json --key=${{ secrets.MY_LLIF_README_KEY }} --id=64d608b3473525005f6cec73

  slack-workflow-status:
    name: Post Workflow Status To Slack
    if: failure()
    needs: sync
    runs-on: ubuntu-latest
    steps:
      - name: Slack Workflow Notification
        uses: Gamesight/slack-workflow-status@master
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          slack_webhook_url: ${{ secrets.SLACK_WEBHOOK_FOUNDATION_TESTING }}
          name: Github Bot
          include_jobs: on-failure
          include_commit_message: true
