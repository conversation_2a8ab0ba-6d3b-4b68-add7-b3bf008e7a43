package convertor

import (
	"math"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestNaNToNilPtrShouldPass(t *testing.T) {
	tests := []struct {
		input          float64
		expectedOutput *float64
	}{
		{
			input:          10.0,
			expectedOutput: NumberToPtr(10.0),
		},
		{
			input:          0.0,
			expectedOutput: NumberToPtr(0.0),
		},
		{
			input:          math.NaN(),
			expectedOutput: nil,
		},
	}

	for _, tt := range tests {
		output := NanToNilPtr(tt.input)
		require.Equal(t, tt.expectedOutput, output)
	}
}

func TestZeroToNilShouldPass(t *testing.T) {
	tests := []struct {
		input          float64
		expectedOutput *float64
	}{
		{
			input:          0.0,
			expectedOutput: nil,
		},
		{
			input:          10.0,
			expectedOutput: NumberToPtr(10.0),
		},
		{
			input:          -5.0,
			expectedOutput: NumberToPtr(-5.0),
		},
	}

	for _, tt := range tests {
		output := ZeroToNil(tt.input)
		require.Equal(t, tt.expectedOutput, output)
	}
}

func TestFloatNilPtrToZeroShouldPass(t *testing.T) {
	tests := []struct {
		input          *float64
		expectedOutput float64
	}{
		{
			input:          nil,
			expectedOutput: 0.0,
		},
		{
			input:          NumberToPtr(1.0),
			expectedOutput: 1.0,
		},
		{
			input:          NumberToPtr(-1.0),
			expectedOutput: -1.0,
		},
	}

	for _, tt := range tests {
		output := FloatNilPtrToZero(tt.input)
		require.Equal(t, tt.expectedOutput, output)
	}
}

func TestConvertIntNilPtrToZeroShouldPass(t *testing.T) {
	tests := []struct {
		input          *int
		expectedOutput int
	}{
		{
			input:          nil,
			expectedOutput: 0,
		},
		{
			input:          NumberToPtr(10),
			expectedOutput: 10,
		},
	}

	for _, tt := range tests {
		require.Equal(t, tt.expectedOutput, IntNilPtrToZero(tt.input))
	}
}

func TestFloatPtrToInt(t *testing.T) {
	tests := []struct {
		input  *float64
		output int
	}{
		{
			input:  NumberToPtr(10.0),
			output: 10,
		},
		{
			input:  nil,
			output: 0,
		},
	}

	for _, tt := range tests {
		result := FloatPtrToInt(tt.input)
		require.Equal(t, tt.output, result)
	}
}
