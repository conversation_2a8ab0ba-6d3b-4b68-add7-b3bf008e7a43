package convertor

import "math"

// RoundFloat takes in a float64 and a precision integer to cut off fractional digits
//
// Ex: f = 10.1234, prec = 2, RoundFloat(f, prec) == 10.12
func RoundFloat(f float64, prec int) float64 {
	mul := math.Pow(10, float64(prec))
	return math.Trunc(f*mul) / mul
}

// RoundFloatPtr works like Truncate, but allows a float pointer as input and returns nil if nil pointer is received
func RoundFloatPtr(f *float64, prec int) *float64 {
	if f == nil {
		return nil
	}
	value := RoundFloat(*f, prec)
	return &value
}

// NumberToPtr converts a number (either an int or a float64) to a pointer
func NumberToPtr[T int | float64](n T) *T {
	return &n
}
