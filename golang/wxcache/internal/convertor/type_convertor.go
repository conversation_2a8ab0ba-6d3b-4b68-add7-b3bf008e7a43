package convertor

import "math"

// nanToNilPtr takes in a float which can either be a valid value or math.NaN().
//
// If the input is math.NaN() then it returns a nil pointer instead, otherwise it returns a pointer to the original value.
func NanToNilPtr(f float64) *float64 {
	if math.IsNaN(f) {
		return nil
	}
	return &f
}

// ZeroToNil takes in a float64 or an integer to assert if the value is 0.
// If the value is zero, then the function returns a nil pointer instead.
// If the value is NOT zero, it returns the initial value as a pointer.
func ZeroToNil[T float64 | int](v T) *T {
	if v == 0 {
		return nil
	}
	return &v
}

// IntNilPtrToZero takes in an integer pointer and asserts it is not nil
// If the pointer is nil, it returns 0.0 instead, otherwise it returns the passed in value.
func IntNilPtrToZero(i *int) int {
	if i == nil {
		return 0
	}
	return *i
}

// FloatNilPtrToZero takes in a float pointer and asserts it is not nil
// If the pointer is nil, it returns 0.0 instead, otherwise it returns the passed in value.
func FloatNilPtrToZero(f *float64) float64 {
	if f == nil {
		return 0.0
	}
	return *f
}

// FloatPtrToInt takes in a float pointer and converts it into an int.
// If the pointer is nil, the returned value is 0.
func FloatPtrToInt(f *float64) int {
	if f == nil {
		return 0
	}
	return int(*f)
}

// StrToStrPtr takes in a string and returns a pointer to it.
func StrToStrPtr(s string) *string {
	return &s
}

// StrPtrToStr takes in a string pointer and returns the string value.
// If the pointer is nil, the returned value is an empty string.
func StrPtrToStr(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}
