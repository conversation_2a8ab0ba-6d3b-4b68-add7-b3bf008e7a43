package handler_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/api/handler"
	"llif.org/wxcache/internal/api/request"
	"llif.org/wxcache/internal/service"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/testutil/testsetup"
	"llif.org/wxcache/pkg/wxtypes"
)

var (
	apiPrefix = "/api/v2"
	aqr       = apiPrefix + "/airquality"
	wr        = apiPrefix + "/weather"
	pr        = apiPrefix + "/pollen"
)

func TestPostEnvironmentDataIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	var (
		environmentInput = request.PostEnvironment{
			SpaceTime: []wxtypes.SpaceTime{{
				Lat:      location.London.Lat,
				Long:     location.London.Long,
				TimeFrom: time.Now().UTC().Add(-24 * time.Hour),
				TimeTo:   time.Now().UTC(),
			}},
		}

		e = echo.New()
	)

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		svc     = service.NewEnvironmentService(c)
		handler = handler.NewEnvironmentHandler(c.Logger, svc)
	)

	assertSuccess := func(t *testing.T, route string, f echo.HandlerFunc) {
		var rb bytes.Buffer
		err := json.NewEncoder(&rb).Encode(environmentInput)
		require.NoError(t, err)

		request := httptest.NewRequest(http.MethodPost, route, &rb)
		request.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		recorder := httptest.NewRecorder()
		ctx := e.NewContext(request, recorder)

		err = f(ctx)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, recorder.Code)
		require.NotEmpty(t, recorder.Body.String())
	}

	t.Run("Air Quality", func(t *testing.T) {
		assertSuccess(t, aqr, handler.PostAirQuality)
	})

	t.Run("Weather", func(t *testing.T) {
		assertSuccess(t, wr, handler.PostWeather)
	})

	t.Run("Pollen", func(t *testing.T) {
		assertSuccess(t, pr, handler.PostPollen)
	})
}

func TestPostEnvironmentInvalidRequest(t *testing.T) {
	var (
		environmentInput = wxtypes.SpaceTime{
			TimeFrom: time.Now().UTC(),
			TimeTo:   time.Now().UTC().Add(24 * time.Hour),
			Lat:      location.London.Lat,
			Long:     location.London.Long,
		}

		e = echo.New()
	)

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		svc     = service.NewEnvironmentService(c)
		handler = handler.NewEnvironmentHandler(c.Logger, svc)
	)

	assertBadRequest := func(t *testing.T, route string, f echo.HandlerFunc) {
		var rb bytes.Buffer
		err := json.NewEncoder(&rb).Encode(environmentInput)
		require.NoError(t, err)

		request := httptest.NewRequest(http.MethodPost, route, &rb)
		request.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		recorder := httptest.NewRecorder()
		ctx := e.NewContext(request, recorder)

		err = f(ctx)
		// The handler SHOULD NOT return an error itself unless it's 500 internal server error.
		// Therefore even for bad input it returns nil, but the response is set to the appropriate code
		require.NoError(t, err)
		require.Equal(t, http.StatusBadRequest, recorder.Code)
	}

	t.Run("Air Quality", func(t *testing.T) {
		assertBadRequest(t, aqr, handler.PostAirQuality)
	})

	t.Run("Weather", func(t *testing.T) {
		assertBadRequest(t, wr, handler.PostWeather)
	})

	t.Run("Pollen", func(t *testing.T) {
		assertBadRequest(t, pr, handler.PostPollen)
	})
}

func TestPostEnvironmentEmptyResponse(t *testing.T) {
	var (
		timeIn10Years    = time.Now().UTC().Add(365 * (24 * time.Hour))
		environmentInput = request.PostEnvironment{
			SpaceTime: []wxtypes.SpaceTime{{
				Lat:      location.London.Lat,
				Long:     location.London.Long,
				TimeFrom: timeIn10Years,
				TimeTo:   timeIn10Years,
			}},
		}

		e = echo.New()
	)

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		svc     = service.NewEnvironmentService(c)
		handler = handler.NewEnvironmentHandler(c.Logger, svc)
	)

	assertNoContent := func(t *testing.T, route string, f echo.HandlerFunc) {
		var rb bytes.Buffer
		err := json.NewEncoder(&rb).Encode(environmentInput)
		require.NoError(t, err)

		request := httptest.NewRequest(http.MethodPost, route, &rb)
		request.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		recorder := httptest.NewRecorder()
		ctx := e.NewContext(request, recorder)

		err = f(ctx)
		// The handler SHOULD NOT return an error itself unless it's 500 internal server error.
		// Therefore even for bad input it returns nil, but the response is set to the appropriate code
		require.NoError(t, err)
		require.Equal(t, http.StatusNoContent, recorder.Code)
	}

	t.Run("Air Quality", func(t *testing.T) {
		assertNoContent(t, aqr, handler.PostAirQuality)
	})

	t.Run("Weather", func(t *testing.T) {
		assertNoContent(t, wr, handler.PostWeather)
	})

	t.Run("Pollen", func(t *testing.T) {
		assertNoContent(t, pr, handler.PostPollen)
	})
}

func TestPostEnvironmentInvalidSpaceTimeOrder(t *testing.T) {
	var (
		now              = time.Now().UTC()
		environmentInput = request.PostEnvironment{
			SpaceTime: []wxtypes.SpaceTime{{
				Lat:      location.London.Lat,
				Long:     location.London.Long,
				TimeFrom: now,                 // TimeFrom is after TimeTo - this should fail validation
				TimeTo:   now.Add(-time.Hour), // TimeTo is 1 hour before TimeFrom
			}},
		}

		e = echo.New()
	)

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		svc     = service.NewEnvironmentService(c)
		handler = handler.NewEnvironmentHandler(c.Logger, svc)
	)

	assertSpaceTimeValidationError := func(t *testing.T, route string, f echo.HandlerFunc) {
		var rb bytes.Buffer
		err := json.NewEncoder(&rb).Encode(environmentInput)
		require.NoError(t, err)

		request := httptest.NewRequest(http.MethodPost, route, &rb)
		request.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		recorder := httptest.NewRecorder()
		ctx := e.NewContext(request, recorder)

		err = f(ctx)
		// The handler should return no error (it handles the validation internally)
		require.NoError(t, err)
		require.Equal(t, http.StatusUnprocessableEntity, recorder.Code)

		// Verify the error message mentions invalid time range
		require.Contains(t, recorder.Body.String(), "invalid time range")
	}

	t.Run("Air Quality", func(t *testing.T) {
		assertSpaceTimeValidationError(t, aqr, handler.PostAirQuality)
	})

	t.Run("Weather", func(t *testing.T) {
		assertSpaceTimeValidationError(t, wr, handler.PostWeather)
	})

	t.Run("Pollen", func(t *testing.T) {
		assertSpaceTimeValidationError(t, pr, handler.PostPollen)
	})
}
