package database

import (
	"context"
	"log/slog"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/testutil"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/wxtypes"
)

func TestGetTypeFromResponseBodyWithEmptyHits(t *testing.T) {
	var (
		input = OpenSearchSearchBody{
			Hits: struct {
				Total struct {
					Value    int    "json:\"value\""
					Relation string "json:\"relation\""
				} "json:\"total\""
				MaxScore float64 "json:\"max_score\""
				Hits     []struct {
					Index  string  "json:\"_index\""
					Id     string  "json:\"_id\""
					Score  float64 "json:\"_score\""
					Source any     "json:\"_source\""
				} "json:\"hits\""
			}{
				Hits: []struct {
					Index  string  "json:\"_index\""
					Id     string  "json:\"_id\""
					Score  float64 "json:\"_score\""
					Source any     "json:\"_source\""
				}{},
			},
		}
		data = GetTypeFromResponseBody[wxtypes.AirQualityV2](context.Background(), slog.Default(), &input)
	)
	require.Empty(t, data)
}

func TestGetTypeFromResponseBodyWithIncorrectType(t *testing.T) {
	var (
		input = OpenSearchSearchBody{
			Hits: struct {
				Total struct {
					Value    int    "json:\"value\""
					Relation string "json:\"relation\""
				} "json:\"total\""
				MaxScore float64 "json:\"max_score\""
				Hits     []struct {
					Index  string  "json:\"_index\""
					Id     string  "json:\"_id\""
					Score  float64 "json:\"_score\""
					Source any     "json:\"_source\""
				} "json:\"hits\""
			}{
				Hits: []struct {
					Index  string  "json:\"_index\""
					Id     string  "json:\"_id\""
					Score  float64 "json:\"_score\""
					Source any     "json:\"_source\""
				}{
					{
						Source: wxtypes.AirQualityV2{},
					},
				},
			},
		}
		data = GetTypeFromResponseBody[wxtypes.PollenV2](context.Background(), slog.Default(), &input)
	)
	require.Empty(t, data)
}

func TestGetTypeFromResponseBody(t *testing.T) {
	var (
		aq = wxtypes.AirQualityV2{
			Timestamp: time.Now().UTC(),
			Pollutants: wxtypes.AirQualityPollutants{
				PM10: convertor.NumberToPtr(10.0),
			},
			AQI: wxtypes.AirQualityIndex{
				EU: convertor.NumberToPtr(10),
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  location.Capetown.Lat,
				Longitude: location.Capetown.Long,
			},
			Metadata: wxtypes.Metadata{Provider: "test"},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
			},
		}
		input = OpenSearchSearchBody{
			Hits: struct {
				Total struct {
					Value    int    "json:\"value\""
					Relation string "json:\"relation\""
				} "json:\"total\""
				MaxScore float64 "json:\"max_score\""
				Hits     []struct {
					Index  string  "json:\"_index\""
					Id     string  "json:\"_id\""
					Score  float64 "json:\"_score\""
					Source any     "json:\"_source\""
				} "json:\"hits\""
			}{
				Hits: []struct {
					Index  string  "json:\"_index\""
					Id     string  "json:\"_id\""
					Score  float64 "json:\"_score\""
					Source any     "json:\"_source\""
				}{
					{
						Source: aq,
					},
				},
			},
		}
		data = GetTypeFromResponseBody[wxtypes.AirQualityV2](context.Background(), slog.Default(), &input)
	)
	require.NotEmpty(t, data)
	require.Len(t, data, 1)
	require.Equal(t, aq, data[0])
}

func TestGetTypeFromResponseBodyReturnsEmptySliceIfNilBodyProvided(t *testing.T) {
	require.Empty(t, GetTypeFromResponseBody[wxtypes.WeatherV2](context.Background(), slog.Default(), nil))
}

func TestGetEmptyBucketsFromAggregation(t *testing.T) {
	lastMinuteOfAnHour := func(dt time.Time) time.Time {
		return time.Date(dt.Year(), dt.Month(), dt.Day(), dt.Hour(), 59, 59, 0, dt.Location())
	}

	var (
		lat, lon = location.Seoul.Lat, location.Seoul.Long

		input = []OpenSearchAggregationBucket{
			{
				Key:         1,
				KeyAsString: "2023-01-01T00:00:00Z",
				DocCount:    0,
			},
			{
				Key:         1,
				KeyAsString: "2023-01-01T01:00:00Z",
				DocCount:    1,
			},
			{
				Key:         1,
				KeyAsString: "2023-01-01T02:00:00Z",
				DocCount:    3,
			},
			{
				Key:         1,
				KeyAsString: "2023-01-01T03:00:00Z",
				DocCount:    0,
			},
		}
		expectedResult = []wxtypes.SpaceTime{
			{
				Lat:      lat,
				Long:     lon,
				TimeFrom: testutil.GetISOTimeFromString(input[0].KeyAsString),
				TimeTo:   lastMinuteOfAnHour(testutil.GetISOTimeFromString(input[0].KeyAsString)),
			},
			{
				Lat:      lat,
				Long:     lon,
				TimeFrom: testutil.GetISOTimeFromString(input[3].KeyAsString),
				TimeTo:   lastMinuteOfAnHour(testutil.GetISOTimeFromString(input[3].KeyAsString)),
			},
		}

		result = GetEmptyBucketsFromAggregation(context.Background(), slog.Default(), lat, lon, input)
	)
	require.NotEmpty(t, result)
	require.EqualValues(t, expectedResult, result)
}
