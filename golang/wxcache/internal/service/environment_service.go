package service

import (
	"context"
	"log/slog"

	"llif.org/wxcache/internal/config"
	"llif.org/wxcache/internal/spacetime"
	"llif.org/wxcache/pkg/provider"
	"llif.org/wxcache/pkg/provider/ambee"
	"llif.org/wxcache/pkg/provider/openmeteo"
	"llif.org/wxcache/pkg/provider/visualcrossing"
	"llif.org/wxcache/pkg/provider/weatherapi"
	"llif.org/wxcache/pkg/wxtypes"
)

type EnvironmentService interface {
	GetAirQuality(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.AirQualityV2, error)
	GetWeather(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.WeatherV2, error)
	GetPollen(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.PollenV2, error)
	GetAirQualityForecast(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.AirQualityV2, error)
	GetWeatherForecast(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.WeatherV2, error)
	GetPollenForecast(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.PollenV2, error)
}

// environmentService holds the providers used to resolve space time points into the appropriate data types.
//
// Never create this struct directly, rather use the NewEnvironmentService() call.
type environmentService struct {
	*slog.Logger

	config *config.Config

	// A slice of providers to use when resolving air quality
	aq []provider.Provider

	// A slice of providers to use when resolving weather
	weather []provider.Provider

	// A slice of providers to use when resolving pollen
	pollen []provider.Provider

	// A slice of providers to use when resolving forecasted air quality
	aqForecast []provider.ForecastProvider

	// A slice of providers to use when resolving forecasted weather
	weatherForecast []provider.ForecastProvider

	// A slice of providers to use when resolving forecasted pollen
	pollenForecast []provider.ForecastProvider
}

var _ EnvironmentService = (*environmentService)(nil)

// NewEnvironmentService creates a newly initialised environment service with all used providers. It handles the priority of the providers for given data types.
//
// @TODO: Find a way to allow overriding the initialised providers for individual data types from the outside.
func NewEnvironmentService(c *config.Config) *environmentService {
	var (
		ambee          = ambee.Init(c)
		openmeteo      = openmeteo.Init(c)
		wapi           = weatherapi.Init(c)
		visualcrossing = visualcrossing.Init(c)
	)
	svc := &environmentService{
		config:          c,
		Logger:          c.Logger,
		aq:              []provider.Provider{openmeteo, wapi},
		weather:         []provider.Provider{visualcrossing, wapi, openmeteo},
		pollen:          []provider.Provider{openmeteo, ambee},
		aqForecast:      []provider.ForecastProvider{openmeteo, wapi},
		weatherForecast: []provider.ForecastProvider{openmeteo},
		pollenForecast:  []provider.ForecastProvider{openmeteo, ambee},
	}
	c.Logger.Info("initialised environment service providers",
		"aq", len(svc.aq),
		"weather", len(svc.weather),
		"pollen", len(svc.pollen),
		"aq_forecast", len(svc.aqForecast),
		"weather_forecast", len(svc.weatherForecast),
		"pollen_forecast", len(svc.pollenForecast),
	)
	return svc
}

// GetAirQuality resolves the given space time into air quality data points and then saves them to the database before returning them to the caller.
// If the data is already in the database, the save step is a no-op.
func (svc *environmentService) GetAirQuality(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.AirQualityV2, error) {
	var (
		results            = make([]wxtypes.AirQualityV2, 0)
		spaceTimeToResolve = make([]wxtypes.SpaceTime, 0)
	)

	for _, bucket := range st {
		data, emptyBuckets, err := svc.config.Repository.AirQuality.FetchWithEmptyBuckets(ctx, bucket)
		if err != nil {
			svc.ErrorContext(ctx, "could not fetch data from the database", "data_type", "air_quality", "space_time", bucket, "err", err)
			spaceTimeToResolve = append(spaceTimeToResolve, bucket)
			continue
		}
		if len(emptyBuckets) > 0 {
			spaceTimeToResolve = append(spaceTimeToResolve, emptyBuckets...)
		}
		if len(data) > 0 {
			results = append(results, data...)
		}
	}

	if len(spaceTimeToResolve) == 0 {
		svc.InfoContext(ctx, "data fully loaded from cache", "len", len(results))
		return results, nil
	}

	spaceTimeToResolve = spacetime.BucketSpaceTimeByLocationAndMergeDates(spaceTimeToResolve)

	for _, provider := range svc.aq {
		svc.InfoContext(ctx, "fetching air quality from provider", "provider", provider.String(), "space_time", st)

		data, errBuckets := provider.GetAirQuality(ctx, spaceTimeToResolve)
		if len(data) > 0 {
			svc.InfoContext(ctx, "provider returned data", "provider", provider.String(), "len", len(data))
			results = append(results, data...)
		}

		spaceTimeToResolve = errBuckets
		if len(spaceTimeToResolve) > 0 {
			svc.InfoContext(ctx, "provider could not resolve spacetime", "provider", provider.String(), "errBuckets", spaceTimeToResolve)
		} else if len(errBuckets) == 0 {
			break
		}
	}

	if len(spaceTimeToResolve) > 0 {
		svc.InfoContext(ctx, "partial resolution of data", "data_type", "air_quality", "space_time", spaceTimeToResolve)
	}
	if len(results) == 0 {
		svc.WarnContext(ctx, "no air quality data found", "space_time", st)
		return results, nil
	}

	if err := svc.config.Repository.AirQuality.Insert(ctx, results); err != nil {
		svc.ErrorContext(ctx, "failed to save air quality data", "err", err)
		return results, err
	}
	return results, nil
}

// GetWeather resolves the given space time into weather data points and then saves them to the database before returning them to the caller.
// If the data is already in the database, the save step is a no-op.
func (svc *environmentService) GetWeather(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.WeatherV2, error) {
	var (
		results            = make([]wxtypes.WeatherV2, 0)
		spaceTimeToResolve = make([]wxtypes.SpaceTime, 0)
	)

	for _, bucket := range st {
		data, emptyBuckets, err := svc.config.Repository.Weather.FetchWithEmptyBuckets(ctx, bucket)
		if err != nil {
			svc.ErrorContext(ctx, "could not fetch data from the database", "data_type", "weather", "space_time", bucket, "err", err)
			spaceTimeToResolve = append(spaceTimeToResolve, bucket)
			continue
		}
		if len(emptyBuckets) > 0 {
			spaceTimeToResolve = append(spaceTimeToResolve, emptyBuckets...)
		}
		if len(data) > 0 {
			results = append(results, data...)
		}
	}

	if len(spaceTimeToResolve) == 0 {
		svc.InfoContext(ctx, "data fully loaded from cache", "len", len(results))
		return results, nil
	}

	spaceTimeToResolve = spacetime.BucketSpaceTimeByLocationAndMergeDates(spaceTimeToResolve)

	for _, provider := range svc.weather {
		svc.InfoContext(ctx, "fetching weather from provider", "provider", provider.String(), "space_time", st)

		data, errBuckets := provider.GetWeather(ctx, spaceTimeToResolve)
		if len(data) > 0 {
			svc.InfoContext(ctx, "provider returned data", "provider", provider.String(), "len", len(data))
			results = append(results, data...)
		}

		spaceTimeToResolve = errBuckets
		if len(spaceTimeToResolve) > 0 {
			svc.InfoContext(ctx, "provider could not resolve spacetime", "provider", provider.String(), "errBuckets", spaceTimeToResolve)
		} else if len(errBuckets) == 0 {
			break
		}
	}

	if len(spaceTimeToResolve) > 0 {
		svc.InfoContext(ctx, "partial resolution of data", "data_type", "weather", "space_time", spaceTimeToResolve)
	}
	if len(results) == 0 {
		svc.WarnContext(ctx, "no weather data found", "space_time", st)
		return results, nil
	}

	if err := svc.config.Repository.Weather.Insert(ctx, results); err != nil {
		svc.ErrorContext(ctx, "failed to save weather data", "err", err)
		return results, err
	}
	return results, nil
}

// GetPollen resolves the given space time into pollen data points and then saves them to the database before returning them to the caller.
// If the data is already in the database, the save step is a no-op.
func (svc *environmentService) GetPollen(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.PollenV2, error) {
	var (
		results            = make([]wxtypes.PollenV2, 0)
		spaceTimeToResolve = make([]wxtypes.SpaceTime, 0)
	)

	for _, bucket := range st {
		data, emptyBuckets, err := svc.config.Repository.Pollen.FetchWithEmptyBuckets(ctx, bucket)
		if err != nil {
			svc.ErrorContext(ctx, "could not fetch data from the database", "data_type", "pollen", "space_time", bucket, "err", err)
			spaceTimeToResolve = append(spaceTimeToResolve, bucket)
			continue
		}
		if len(emptyBuckets) > 0 {
			spaceTimeToResolve = append(spaceTimeToResolve, emptyBuckets...)
		}
		if len(data) > 0 {
			results = append(results, data...)
		}
	}

	if len(spaceTimeToResolve) == 0 {
		svc.InfoContext(ctx, "data fully loaded from cache", "len", len(results))
		return results, nil
	}

	spaceTimeToResolve = spacetime.BucketSpaceTimeByLocationAndMergeDates(spaceTimeToResolve)

	for _, provider := range svc.pollen {
		svc.InfoContext(ctx, "fetching pollen from provider", "provider", provider.String(), "space_time", st)

		data, errBuckets := provider.GetPollen(ctx, spaceTimeToResolve)
		if len(data) > 0 {
			svc.InfoContext(ctx, "provider returned data", "provider", provider.String(), "len", len(data))
			results = append(results, data...)
		}

		spaceTimeToResolve = errBuckets
		if len(spaceTimeToResolve) > 0 {
			svc.InfoContext(ctx, "provider could not resolve spacetime", "provider", provider.String(), "errBuckets", spaceTimeToResolve)
		} else if len(errBuckets) == 0 {
			break
		}
	}

	if len(spaceTimeToResolve) > 0 {
		svc.InfoContext(ctx, "partial resolution of data", "data_type", "pollen", "space_time", spaceTimeToResolve)
	}
	if len(results) == 0 {
		svc.WarnContext(ctx, "no pollen data found", "space_time", st)
		return results, nil
	}

	if err := svc.config.Repository.Pollen.Insert(ctx, results); err != nil {
		svc.ErrorContext(ctx, "failed to save pollen data", "err", err)
		return results, err
	}
	return results, nil
}

// GetAirQualityForecast resolves the given space time into air quality forecast data points.
func (svc *environmentService) GetAirQualityForecast(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.AirQualityV2, error) {
	var (
		results      = make([]wxtypes.AirQualityV2, 0)
		errSpaceTime = make([]wxtypes.SpaceTime, 0)
	)

	for _, provider := range svc.aqForecast {
		// @TODO: The providers need to accept a slice of space time and return their own failed space time.
		for _, spacetime := range st {
			svc.InfoContext(ctx, "fetching air quality forecast from provider", "provider", provider.String(), "space_time", spacetime)
			data, err := provider.GetAirQualityForecast(ctx, spacetime)
			if err != nil {
				svc.ErrorContext(ctx, "received error from provider", "err", err, "space_time", spacetime, "provider", provider.String())
				errSpaceTime = append(errSpaceTime, spacetime)
				continue
			}
			results = append(results, data...)
		}
		if len(errSpaceTime) == 0 {
			break
		} else {
			// Set the next provider's input as the error output of this provider
			st = errSpaceTime
			errSpaceTime = make([]wxtypes.SpaceTime, 0)
		}
	}
	if len(errSpaceTime) > 0 {
		svc.InfoContext(ctx, "partial resolution of data", "data_type", "air_quality_forecast", "errSpaceTime", errSpaceTime)
	}
	if len(results) == 0 {
		svc.WarnContext(ctx, "no air quality forecast data found", "space_time", st)
		return results, nil
	}
	return results, nil
}

// GetWeatherForecast resolves the given space time into weather forecast data points.
func (svc *environmentService) GetWeatherForecast(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.WeatherV2, error) {
	var (
		results      = make([]wxtypes.WeatherV2, 0)
		errSpaceTime = make([]wxtypes.SpaceTime, 0)
	)

	for _, provider := range svc.weatherForecast {
		// @TODO: The providers need to accept a slice of space time and return their own failed space time.
		for _, spacetime := range st {
			svc.InfoContext(ctx, "fetching weather forecast from provider", "provider", provider.String(), "space_time", spacetime)
			data, err := provider.GetWeatherForecast(ctx, spacetime)
			if err != nil {
				svc.ErrorContext(ctx, "received error from provider", "err", err, "space_time", spacetime, "provider", provider.String())
				errSpaceTime = append(errSpaceTime, spacetime)
			}
			results = append(results, data...)
		}
		if len(errSpaceTime) == 0 {
			break
		} else {
			// Set the next provider's input as the error output of this provider
			st = errSpaceTime
			errSpaceTime = make([]wxtypes.SpaceTime, 0)
		}
	}
	if len(errSpaceTime) > 0 {
		svc.InfoContext(ctx, "partial resolution of data", "data_type", "weather_forecast", "errSpaceTime", errSpaceTime)
	}
	if len(results) == 0 {
		svc.WarnContext(ctx, "no weather forecast data found", "space_time", st)
		return results, nil
	}
	return results, nil
}

// GetPollenForecast resolves the given space time into weather forecast data points.
func (svc *environmentService) GetPollenForecast(ctx context.Context, st []wxtypes.SpaceTime) ([]wxtypes.PollenV2, error) {
	var (
		results      = make([]wxtypes.PollenV2, 0)
		errSpaceTime = make([]wxtypes.SpaceTime, 0)
	)

	for _, provider := range svc.pollenForecast {
		// @TODO: The providers need to accept a slice of space time and return their own failed space time.
		for _, spacetime := range st {
			svc.InfoContext(ctx, "fetching pollen forecast from provider", "provider", provider.String(), "space_time", spacetime)
			data, err := provider.GetPollenForecast(ctx, spacetime)
			if err != nil {
				svc.ErrorContext(ctx, "received error from provider", "err", err, "space_time", spacetime, "provider", provider.String())
				errSpaceTime = append(errSpaceTime, spacetime)
			}
			results = append(results, data...)
		}
		if len(errSpaceTime) == 0 {
			break
		} else {
			// Set the next provider's input as the error output of this provider
			st = errSpaceTime
			errSpaceTime = make([]wxtypes.SpaceTime, 0)
		}
	}
	if len(errSpaceTime) > 0 {
		svc.InfoContext(ctx, "partial resolution of data", "data_type", "pollen_forecast", "errSpaceTime", errSpaceTime)
	}
	if len(results) == 0 {
		svc.WarnContext(ctx, "no pollen forecast data found", "space_time", st)
		return results, nil
	}
	return results, nil
}
