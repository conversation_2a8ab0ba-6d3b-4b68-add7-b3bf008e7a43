package setup

import (
	"context"
	"encoding/json"
	"log/slog"
	"os"
	"strings"

	"github.com/caarlos0/env/v10"
	"github.com/getsentry/sentry-go"
	"llif.org/wxcache/internal/config"
	"llif.org/wxcache/internal/config/database"
	"llif.org/wxcache/internal/hook"
	llifaws "llif.org/wxcache/pkg/aws"
	"llif.org/wxcache/pkg/wxtypes"
)

// NewConfig creates an initialised version of the whole environment along with repository setups and database connections.
//
// In remote environments, the setup calls AWS Secrets Manager to override environmental values from there,
// and initialises Sentry as part of the logger created within the resulting config.
func NewConfig() (*config.Config, error) {
	baseConfig, err := getConfigFromEnv[config.BaseConfig]()
	if err != nil {
		return nil, err
	}
	awsConfig, err := getConfigFromEnv[config.AWSConfig]()
	if err != nil {
		return nil, err
	}
	// Override the endpoint if we are running locally outside of a container with a LocalStack address
	if !baseConfig.IsContainerized && baseConfig.RunEnv == "local" {
		slog.Info("overriding aws endpoint", "endpoint", awsConfig.EndpointLocal)
		awsConfig.Endpoint = awsConfig.EndpointLocal
	}
	if baseConfig.RunEnv != "local" {
		if err := loadEnvFromSecretsManager(context.Background(), awsConfig); err != nil {
			return nil, err
		}
	}

	sentryConfig, err := getConfigFromEnv[config.SentryConfig]()
	if err != nil {
		return nil, err
	}
	logger, err := setupLogger(baseConfig, sentryConfig)
	if err != nil {
		return nil, err
	}
	slog.SetDefault(logger)

	rateLimitConfig, err := getConfigFromEnv[config.RateLimitConfig]()
	if err != nil {
		return nil, err
	}

	messagingConfig, err := getConfigFromEnv[config.MessagingConfig]()
	if err != nil {
		return nil, err
	}
	providerConfig, err := getConfigFromEnv[config.ProviderAPIKey]()
	if err != nil {
		return nil, err
	}
	serverConfig, err := getConfigFromEnv[config.ServerConfig]()
	if err != nil {
		return nil, err
	}

	databaseConfigOpts, err := getConfigFromEnv[database.ConfigOpts]()
	if err != nil {
		return nil, err
	}
	databaseConfig, err := database.NewDatabaseConfigFromConfigOpts(logger, databaseConfigOpts)
	if err != nil {
		return nil, err
	}

	c := &config.Config{
		BaseConfig: baseConfig,

		Logger:    logger,
		Config:    databaseConfig,
		AWS:       awsConfig,
		Sentry:    sentryConfig,
		Server:    serverConfig,
		APIKey:    providerConfig,
		RateLimit: rateLimitConfig,
		Messaging: messagingConfig,
	}

	if err := wxtypes.ValidateStruct(c); err != nil {
		c.Logger.Error("got validation err", "err", err)
		return nil, err
	}
	return c, nil
}

// loadEnvFromSecretsManager connects to the secrets manager and exports the necessary values as environmental variables
func loadEnvFromSecretsManager(ctx context.Context, c config.AWSConfig) error {
	sm, err := llifaws.NewAWSSecretsManager(ctx, c.Region)
	if err != nil {
		return err
	}
	// API keys
	value, err := sm.GetValue(ctx, "prod/environment/api_keys") // all remote environments source from "prod/" for wxcache
	if err != nil {
		return err
	}
	var data llifaws.AWSApiKeysSecretOutput
	if err = json.Unmarshal([]byte(value), &data); err != nil {
		return err
	}
	os.Setenv("AMBEE_APIKEY", data.AmbeeAPIKey)
	os.Setenv("WAPI_KEY", data.WeatherAPIKey)
	os.Setenv("VISUALCROSSING_APIKEY", data.VisualCrossingAPIKey)

	// Sentry
	value, err = sm.GetValue(ctx, "myllif/sentry")
	if err != nil {
		return err
	}
	var sentry llifaws.AWSSentryKeysSecretOutput
	if err = json.Unmarshal([]byte(value), &sentry); err != nil {
		return err
	}
	os.Setenv("SENTRY_DSN", sentry.SentryDsn)
	return nil
}

// setupLogger initializes a slog logger to the given log level. If the service is running in a remote environment it also adds a Sentry hook to the slog logger.
func setupLogger(c config.BaseConfig, sc config.SentryConfig) (*slog.Logger, error) {
	var level = slog.LevelInfo
	switch strings.ToLower(c.LogLevel) {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn", "warning":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	var handler = slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: level,
	})
	var defaultLogger = slog.New(hook.NewCtxHandler(handler))
	slog.SetDefault(defaultLogger)

	slog.Info("setup default logger", "run_env", c.RunEnv)

	// Only initialise Sentry if we are not in a local environment
	if c.RunEnv != "local" {
		sentryLogger, err := addSentryHook(c, sc, handler)
		if err != nil {
			return nil, err
		}
		slog.SetDefault(sentryLogger)
		slog.Info("initialised sentry", "environment", c.RunEnv)
		return sentryLogger, nil
	}
	return defaultLogger, nil
}

// setupSentryLogger creates a Slog logger with a custom hook for Sentry
func addSentryHook(c config.BaseConfig, sc config.SentryConfig, h slog.Handler) (*slog.Logger, error) {
	var err = sentry.Init(sentry.ClientOptions{
		AttachStacktrace: true,
		SendDefaultPII:   true,
		Environment:      c.RunEnv,
		Release:          c.AppVersion,
		Dsn:              sc.Dsn,
		Debug:            sc.Debug,
		TracesSampleRate: sc.TracesSampleRate,
		EnableLogs:       true,
	})
	if err != nil {
		return nil, err
	}
	hook := hook.NewSentryHandler(h, []slog.Level{slog.LevelWarn, slog.LevelError})
	return slog.New(hook), nil
}

// getConfigFromEnv serializes the environment into type T.
//
// It is up to the caller to ensure type T has proper env tags to be able to serialize the environment into it.
//
//	struct Env struct {
//		A int `env:"ENV_KEY"`
//	}
func getConfigFromEnv[T any]() (T, error) {
	var (
		result T
		err    = env.Parse(&result)
	)
	if err != nil {
		return result, err
	}
	err = wxtypes.ValidateStruct[T](result)
	return result, err
}
