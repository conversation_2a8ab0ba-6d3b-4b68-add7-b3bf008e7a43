package aqi

import (
	"testing"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/convertor"
)

func TestGBTransformerCalculationsAreCorrectShouldPass(t *testing.T) {
	tests := []struct {
		input          AQIConcentration
		expectedOutput aqiResult
	}{
		{
			input: AQIConcentration{
				NO2:  convertor.NumberToPtr(1.0),
				SO2:  convertor.NumberToPtr(0.3),
				O3:   convertor.NumberToPtr(68.0),
				CO:   convertor.NumberToPtr(223.6),
				PM25: convertor.NumberToPtr(0.5),
				PM10: convertor.NumberToPtr(1.5),
			},
			expectedOutput: aqiResult{
				AQI:       convertor.NumberToPtr(3),
				IndexType: GB,
			},
		},
		{
			input: AQIConcentration{
				NO2:  nil,
				SO2:  convertor.NumberToPtr(0.3),
				O3:   nil,
				CO:   nil,
				PM25: convertor.NumberToPtr(0.5),
				PM10: convertor.NumberToPtr(1.5),
			},
			expectedOutput: aqiResult{
				AQI:       convertor.NumberToPtr(1),
				IndexType: GB,
			},
		},
		{
			input: AQIConcentration{
				NO2:  nil,
				SO2:  nil,
				O3:   nil,
				CO:   nil,
				PM25: nil,
				PM10: nil,
			},
			expectedOutput: aqiResult{
				AQI:       nil,
				IndexType: GB,
			},
		},
		{
			input: AQIConcentration{
				NO2:  convertor.NumberToPtr(0.0),
				SO2:  convertor.NumberToPtr(0.0),
				O3:   convertor.NumberToPtr(0.0),
				CO:   convertor.NumberToPtr(0.0),
				PM25: convertor.NumberToPtr(0.0),
				PM10: convertor.NumberToPtr(0.0),
			},
			expectedOutput: aqiResult{
				AQI:       convertor.NumberToPtr(1),
				IndexType: GB,
			},
		},
	}

	for _, tt := range tests {
		output, err := newTransformerAQI_GB(tt.input).CalculateAQI()
		require.NoError(t, err)
		require.Equal(t, tt.expectedOutput, output)
	}
}
