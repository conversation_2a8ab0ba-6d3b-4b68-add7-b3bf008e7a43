package aqi

import (
	"testing"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/convertor"
)

func TestUSTransformerCalculationsAreCorrectShouldPass(t *testing.T) {
	tests := []struct {
		input          AQIConcentration
		expectedOutput aqiResult
	}{
		{
			input: AQIConcentration{
				NO2:  convertor.NumberToPtr(45.2),
				SO2:  convertor.NumberToPtr(6.8),
				O3:   convertor.NumberToPtr(15.2),
				CO:   convertor.NumberToPtr(35.5),
				PM25: convertor.NumberToPtr(7.8),
				PM10: convertor.NumberToPtr(12.9),
			},
			expectedOutput: aqiResult{
				AQI:       convertor.NumberToPtr(32),
				IndexType: US,
			},
		},
		{
			input: AQIConcentration{
				NO2:  convertor.NumberToPtr(45.2),
				SO2:  convertor.NumberToPtr(6.8),
				O3:   convertor.NumberToPtr(15.2),
				CO:   convertor.NumberToPtr(305.5),
				PM25: convertor.NumberToPtr(7.8),
				PM10: convertor.NumberToPtr(12.9),
			},
			expectedOutput: aqiResult{
				AQI:       convertor.NumberToPtr(32),
				IndexType: US,
			},
		},
		{
			input: AQIConcentration{
				NO2:  nil,
				SO2:  nil,
				O3:   convertor.NumberToPtr(15.2),
				CO:   nil,
				PM25: convertor.NumberToPtr(7.8),
				PM10: convertor.NumberToPtr(12.9),
			},
			expectedOutput: aqiResult{
				AQI:       convertor.NumberToPtr(32),
				IndexType: US,
			},
		},
		{
			input: AQIConcentration{
				NO2:  nil,
				SO2:  nil,
				O3:   nil,
				CO:   nil,
				PM25: nil,
				PM10: nil,
			},
			expectedOutput: aqiResult{
				AQI:       nil,
				IndexType: US,
			},
		},
		{
			input: AQIConcentration{
				NO2:  convertor.NumberToPtr(4.1),
				SO2:  convertor.NumberToPtr(4.3),
				O3:   convertor.NumberToPtr(67.0),
				CO:   convertor.NumberToPtr(195.0),
				PM25: convertor.NumberToPtr(2.7),
				PM10: convertor.NumberToPtr(3.9),
			},
			expectedOutput: aqiResult{
				AQI:       convertor.NumberToPtr(11),
				IndexType: US,
			},
		},
		{
			input: AQIConcentration{
				NO2:  convertor.NumberToPtr(0.0),
				SO2:  convertor.NumberToPtr(0.0),
				O3:   convertor.NumberToPtr(0.0),
				CO:   convertor.NumberToPtr(0.0),
				PM25: convertor.NumberToPtr(0.0),
				PM10: convertor.NumberToPtr(0.0),
			},
			expectedOutput: aqiResult{
				AQI:       nil,
				IndexType: US,
			},
		},
	}

	for _, tt := range tests {
		output, err := newTransformerAQI_US(tt.input).CalculateAQI()
		require.NoError(t, err)
		require.Equal(t, tt.expectedOutput, output)
	}
}
