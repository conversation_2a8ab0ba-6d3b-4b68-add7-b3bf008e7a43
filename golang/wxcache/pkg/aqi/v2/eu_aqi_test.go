package aqi

import (
	"testing"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/convertor"
)

func TestEUTransformerCalculationsAreCorrectShouldPass(t *testing.T) {
	tests := []struct {
		input          AQIConcentration
		expectedOutput aqiResult
	}{
		{
			input: AQIConcentration{
				NO2:  convertor.NumberToPtr(12.0),
				SO2:  convertor.NumberToPtr(2.5),
				O3:   convertor.NumberToPtr(37.6),
				CO:   convertor.NumberToPtr(336.0),
				PM25: convertor.NumberToPtr(3.7),
				PM10: convertor.NumberToPtr(6.2),
			},
			expectedOutput: aqiResult{
				AQI:       convertor.NumberToPtr(1),
				IndexType: EU,
			},
		},
		{
			input: AQIConcentration{
				NO2:  nil,
				SO2:  convertor.NumberToPtr(2.5),
				O3:   convertor.NumberToPtr(37.6),
				CO:   nil,
				PM25: convertor.NumberToPtr(3.7),
				PM10: convertor.NumberToPtr(600.2),
			},
			expectedOutput: aqiResult{
				AQI:       convertor.NumberToPtr(5),
				IndexType: EU,
			},
		},
		{
			input: AQIConcentration{
				NO2:  nil,
				SO2:  nil,
				O3:   nil,
				CO:   nil,
				PM25: nil,
				PM10: nil,
			},
			expectedOutput: aqiResult{
				AQI:       nil,
				IndexType: EU,
			},
		},
		{
			input: AQIConcentration{
				NO2:  convertor.NumberToPtr(0.0),
				SO2:  convertor.NumberToPtr(0.0),
				O3:   convertor.NumberToPtr(0.0),
				CO:   convertor.NumberToPtr(0.0),
				PM25: convertor.NumberToPtr(0.0),
				PM10: convertor.NumberToPtr(0.0),
			},
			expectedOutput: aqiResult{
				AQI:       convertor.NumberToPtr(1),
				IndexType: EU,
			},
		},
	}

	for _, tt := range tests {
		output, err := newTransformerAQI_EU(tt.input).CalculateAQI()
		require.NoError(t, err)
		require.Equal(t, tt.expectedOutput, output)
	}
}
