package openmeteo

import (
	"context"
	"math/rand"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/geo"
	"llif.org/wxcache/pkg/testutil"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/testutil/testsetup"
	"llif.org/wxcache/pkg/wxtypes"
)

func TestOpenMeteoReturnsAllTypesShouldPassIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		om        *OpenMeteo          = Init(c)
		ctx                           = context.Background()
		locations []location.Location = location.GetAll()
	)

	t.Run("Air Quality", func(t *testing.T) {
		var spacetime = make([]wxtypes.SpaceTime, 0)

		for _, loc := range locations {
			spacetime = append(spacetime, wxtypes.SpaceTime{
				Lat:      loc.Lat,
				Long:     loc.Long,
				TimeFrom: testutil.GetISOTimeFromString("2023-09-11T10:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2023-09-11T12:00:00Z"),
			})
		}

		res, errBuckets := om.GetAirQuality(ctx, spacetime)
		require.Empty(t, errBuckets)
		require.NotEmpty(t, res)
	})

	t.Run("Air Quality Forecast", func(t *testing.T) {
		timeNow := time.Now().UTC()
		// random step backwards
		step := rand.Intn(24)
		timeFrom := timeNow.Add(-time.Duration(step) * time.Hour)

		for _, loc := range locations {
			max_bucket_count := 4*24 - 1
			hours := rand.Intn(max_bucket_count) + 1

			var st = wxtypes.SpaceTime{
				Lat:      loc.Lat,
				Long:     loc.Long,
				TimeFrom: timeFrom,
				TimeTo:   timeFrom.Add(time.Duration(hours) * time.Hour),
			}
			res, err := om.GetAirQualityForecast(ctx, st)
			require.NoError(t, err)
			require.NotEmpty(t, res)
			require.GreaterOrEqual(t, len(res), hours)
		}
	})

	t.Run("Weather", func(t *testing.T) {
		var spacetime = make([]wxtypes.SpaceTime, 0)

		for _, loc := range locations {
			spacetime = append(spacetime, wxtypes.SpaceTime{
				Lat:      loc.Lat,
				Long:     loc.Long,
				TimeFrom: testutil.GetISOTimeFromString("2023-09-11T10:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2023-09-11T12:00:00Z"),
			})
		}

		res, errBuckets := om.GetWeather(ctx, spacetime)
		require.Empty(t, errBuckets)
		require.NotEmpty(t, res)
	})

	t.Run("Weather Forecast", func(t *testing.T) {
		timeNow := time.Now().UTC()
		// random step backwards
		step := rand.Intn(24)
		timeFrom := timeNow.Add(-time.Duration(step) * time.Hour)

		for _, loc := range locations {
			max_bucket_count := 4*24 - 1
			hours := rand.Intn(max_bucket_count) + 1
			var st = wxtypes.SpaceTime{
				Lat:      loc.Lat,
				Long:     loc.Long,
				TimeFrom: timeFrom,
				TimeTo:   timeFrom.Add(time.Duration(hours) * time.Hour),
			}
			res, err := om.GetWeatherForecast(ctx, st)
			require.NoError(t, err)
			require.NotEmpty(t, res)
			require.GreaterOrEqual(t, len(res), hours)
		}
	})

	t.Run("Pollen", func(t *testing.T) {
		var spacetime = make([]wxtypes.SpaceTime, 0)

		for _, loc := range locations {
			// OpenMeteo supports pollen only in Europe AND only during the pollen season
			if ok, err := geo.CoordinatesInContinent(geo.Europe, loc.Lat, loc.Long); err == nil && ok {
				spacetime = append(spacetime, wxtypes.SpaceTime{
					Lat:      loc.Lat,
					Long:     loc.Long,
					TimeFrom: testutil.GetISOTimeFromString("2023-07-11T10:00:00Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-07-11T12:00:00Z"),
				})
			}
		}

		res, errBuckets := om.GetPollen(ctx, spacetime)
		require.Empty(t, errBuckets)
		require.NotEmpty(t, res)
	})

	t.Run("Pollen Forecast", func(t *testing.T) {
		timeNow := time.Now().UTC()
		// random step backwards
		step := rand.Intn(24)
		timeFrom := timeNow.Add(-time.Duration(step) * time.Hour)

		for _, loc := range locations {
			max_bucket_count := 4*24 - 1
			hours := rand.Intn(max_bucket_count) + 1

			if ok, err := geo.CoordinatesInContinent(geo.Europe, loc.Lat, loc.Long); err == nil && ok {
				var st = wxtypes.SpaceTime{
					Lat:      loc.Lat,
					Long:     loc.Long,
					TimeFrom: timeFrom,
					TimeTo:   timeFrom.Add(time.Duration(hours) * time.Hour),
				}
				res, err := om.GetPollenForecast(ctx, st)
				require.NoError(t, err)
				require.NotEmpty(t, res)
				require.GreaterOrEqual(t, len(res), hours)

			}
		}
	})
}

func TestOpenMeteoReturnsPartialDataForForecastShouldPassIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		om        *OpenMeteo          = Init(c)
		ctx                           = context.Background()
		locations []location.Location = location.GetAll()
	)

	t.Run("AirQuality", func(t *testing.T) {
		var (
			timeFrom = time.Now().UTC()
			timeTo   = timeFrom.AddDate(0, 0, 10) // way over the allowed range
		)

		for _, loc := range locations {
			var st = wxtypes.SpaceTime{
				Lat:      loc.Lat,
				Long:     loc.Long,
				TimeFrom: timeFrom,
				TimeTo:   timeTo,
			}
			res, err := om.GetAirQualityForecast(ctx, st)
			require.NoError(t, err)
			require.NotEmpty(t, res)
		}
	})

	t.Run("Weather", func(t *testing.T) {
		var (
			timeFrom = time.Now().UTC()
			timeTo   = timeFrom.AddDate(0, 0, 25) // way over the allowed range
		)

		for _, loc := range locations {
			var st = wxtypes.SpaceTime{
				Lat:      loc.Lat,
				Long:     loc.Long,
				TimeFrom: timeFrom,
				TimeTo:   timeTo,
			}
			res, err := om.GetWeatherForecast(ctx, st)
			require.NoError(t, err)
			require.NotEmpty(t, res)
		}
	})

}

func getOpenMeteoPollenResponse() OpenMeteoAirQualityResponse {
	response := OpenMeteoAirQualityResponse{
		Latitude:  location.London.Lat,
		Longitude: location.London.Long,
	}

	// Only do two hourly buckets to simplify the test
	response.Hourly.Time = []string{
		"2023-07-25T00:00", "2023-07-25T01:00",
	}

	// Weed
	response.Hourly.RagweedPollen = []*float64{
		convertor.NumberToPtr(10.0),
		convertor.NumberToPtr(5.0),
	}
	response.Hourly.MugwortPollen = []*float64{
		nil,
		convertor.NumberToPtr(20.0),
	}

	// Trees
	response.Hourly.BirchPollen = []*float64{
		convertor.NumberToPtr(1.0),
		convertor.NumberToPtr(15.0),
	}
	response.Hourly.AlderPollen = []*float64{
		convertor.NumberToPtr(0.0),
		convertor.NumberToPtr(20.0),
	}

	// Grass
	response.Hourly.GrassPollen = []*float64{
		convertor.NumberToPtr(0.0),
		convertor.NumberToPtr(50.0),
	}

	return response
}

func getOpenMeteoPollenNormalisedResponse() []wxtypes.PollenV2 {
	return []wxtypes.PollenV2{
		{
			Timestamp: time.Date(2023, time.July, 25, 0, 0, 0, 0, time.UTC),
			Tree: &wxtypes.PollenSpecies{
				Count: 1,
				Subspecies: []wxtypes.PollenSubspecies{
					{
						Name:  "birch",
						Count: 1,
					},
					{
						Name:  "alder",
						Count: 0,
					},
				},
			},
			Weed: &wxtypes.PollenSpecies{
				Count: 10,
				Subspecies: []wxtypes.PollenSubspecies{
					{
						Name:  "ragweed",
						Count: 10,
					},
				},
			},
			Grass: &wxtypes.PollenSpecies{
				Count: 0,
				Subspecies: []wxtypes.PollenSubspecies{
					{
						Name:  "grass",
						Count: 0,
					},
				},
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  location.London.Lat,
				Longitude: location.London.Long,
			},
			Metadata: wxtypes.Metadata{
				Provider: "openmeteo",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Date(2023, time.November, 30, 11, 40, 0, 50989000, time.UTC),
				Backfill:  false,
			},
		},
		{
			Timestamp: time.Date(2023, time.July, 25, 1, 0, 0, 0, time.UTC),
			Tree: &wxtypes.PollenSpecies{
				Count: 35,
				Subspecies: []wxtypes.PollenSubspecies{
					{
						Name:  "birch",
						Count: 15,
					},
					{
						Name:  "alder",
						Count: 20,
					},
				},
			},
			Weed: &wxtypes.PollenSpecies{
				Count: 25,
				Subspecies: []wxtypes.PollenSubspecies{
					{
						Name:  "ragweed",
						Count: 5,
					},
					{
						Name:  "mugwort",
						Count: 20,
					},
				},
			},
			Grass: &wxtypes.PollenSpecies{
				Count: 50,
				Subspecies: []wxtypes.PollenSubspecies{
					{
						Name:  "grass",
						Count: 50,
					},
				},
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  location.London.Lat,
				Longitude: location.London.Long,
			},
			Metadata: wxtypes.Metadata{
				Provider: "openmeteo",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Date(2023, time.November, 30, 11, 40, 0, 50990000, time.UTC),
				Backfill:  false,
			},
		},
	}
}

func getOpenMeteoForecastWeatherResponse() OpenMeteoWeatherForecastResponse {
	response := OpenMeteoWeatherForecastResponse{
		Latitude:  location.London.Lat,
		Longitude: location.London.Long,
	}

	response.UtcOffsetSeconds = 0
	response.Hourly.Time = []string{
		"2023-08-17T00:00", "2023-08-17T01:00",
	}

	response.Hourly.Temperature2M = []*float64{convertor.NumberToPtr(5.0), convertor.NumberToPtr(10.0)}
	response.Hourly.ApparentTemperature = []*float64{convertor.NumberToPtr(6.0), convertor.NumberToPtr(15.0)}
	response.Hourly.Dewpoint2M = []*float64{convertor.NumberToPtr(10.0), convertor.NumberToPtr(10.0)}
	response.Hourly.RelativeHumidity2M = []*float64{convertor.NumberToPtr(3.5), convertor.NumberToPtr(6.5)}
	response.Hourly.PressureMSL = []*float64{convertor.NumberToPtr(12.5), convertor.NumberToPtr(14.5)}
	response.Hourly.WindSpeed10M = []*float64{convertor.NumberToPtr(30.5), convertor.NumberToPtr(15.5)}
	response.Hourly.WindGusts10M = []*float64{convertor.NumberToPtr(10.0), convertor.NumberToPtr(10.0)}
	response.Hourly.CloudCover = []*float64{convertor.NumberToPtr(0.0), convertor.NumberToPtr(5.0)}
	response.Hourly.Visibility = []*float64{convertor.NumberToPtr(30000.0), convertor.NumberToPtr(40000.0)}
	response.Hourly.Precipitation = []*float64{convertor.NumberToPtr(1.0), convertor.NumberToPtr(15.0), convertor.NumberToPtr(0.0)}
	response.Daily.Time = []string{"2023-08-17"}
	response.Daily.UVIndexMax = []*float64{convertor.NumberToPtr(1.0)}
	return response
}

func getOpenMeteoForecastWeatherNormalisedResponse() []wxtypes.WeatherV2 {
	var (
		uvindex                                    = 1
		gust1, gust2                               = 10.0, 10.0
		humidity1, humidity2                       = 3.5, 6.5
		cloudCover1, cloudCover2                   = 0, 5
		pressure1, pressure2                       = 12.5, 14.5
		visibility1, visibility2                   = 30.0, 40.0
		precipitation1, precipitation2             = 1.0, 15.0
		temperature1, temperature2                 = 5.0, 10.0
		apparentTemperature1, apparentTemperature2 = 6.0, 15.0
		windSpeed1, windSpeed2                     = 30.5, 15.5

		loc = time.UTC
	)
	timestamp1, err := time.ParseInLocation("2006-01-02T15:04", "2023-08-17T00:00", loc)
	if err != nil {
		panic(err)
	}

	timestamp2, err := time.ParseInLocation("2006-01-02T15:04", "2023-08-17T01:00", loc)
	if err != nil {
		panic(err)
	}

	return []wxtypes.WeatherV2{
		{
			Timestamp: timestamp1,
			Temperature: wxtypes.WeatherTemperature{
				Temperature: &temperature1,
				FeelsLike:   &apparentTemperature1,
			},
			Wind: wxtypes.WeatherWind{
				Speed:     &windSpeed1,
				Gust:      &gust1,
				Degree:    nil,
				Direction: nil,
			},
			Humidity:      &humidity1,
			CloudCover:    &cloudCover1,
			UV:            &uvindex,
			Pressure:      &pressure1,
			Visiblity:     &visibility1,
			Precipitation: &precipitation1,
			Coordinates: wxtypes.Coordinates{
				Latitude:  location.London.Lat,
				Longitude: location.London.Long,
			},
			Metadata: wxtypes.Metadata{
				Provider: "openmeteo",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Date(2023, time.November, 30, 11, 30, 48, 858963000, time.UTC),
				Backfill:  false,
			},
		},
		{
			Timestamp: timestamp2,
			Temperature: wxtypes.WeatherTemperature{
				Temperature: &temperature2,
				FeelsLike:   &apparentTemperature2,
			},
			Wind: wxtypes.WeatherWind{
				Speed:     &windSpeed2,
				Gust:      &gust2,
				Degree:    nil,
				Direction: nil,
			},
			Humidity:      &humidity2,
			CloudCover:    &cloudCover2,
			UV:            &uvindex,
			Pressure:      &pressure2,
			Visiblity:     &visibility2,
			Precipitation: &precipitation2,
			Coordinates: wxtypes.Coordinates{
				Latitude:  location.London.Lat,
				Longitude: location.London.Long,
			},
			Metadata: wxtypes.Metadata{
				Provider: "openmeteo",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Date(2023, time.November, 30, 11, 30, 48, 858963000, time.UTC),
				Backfill:  false,
			},
		},
	}
}

func getOpenMeteoAirQualityResponse() OpenMeteoAirQualityResponse {
	response := OpenMeteoAirQualityResponse{
		Latitude:  location.NewYork.Lat,
		Longitude: location.NewYork.Long,
	}

	response.Hourly.Time = []string{
		"2023-08-17T00:00",
	}

	var (
		co    = 10.0
		ozone = 1.1
		pm10  = 3.4
		pm25  = 1.3
		no2   = 30.5
		so2   = 15.5
	)

	response.Hourly.CarbonMonoxide = []*float64{convertor.NumberToPtr(co)}
	response.Hourly.Ozone = []*float64{convertor.NumberToPtr(ozone)}
	response.Hourly.PM10 = []*float64{convertor.NumberToPtr(pm10)}
	response.Hourly.PM25 = []*float64{convertor.NumberToPtr(pm25)}
	response.Hourly.NitrogenDioxide = []*float64{convertor.NumberToPtr(no2)}
	response.Hourly.SulphurDioxide = []*float64{convertor.NumberToPtr(so2)}
	response.Hourly.USAQI = []*int{convertor.NumberToPtr(0)}
	response.Hourly.EuropeanAQI = []*int{convertor.NumberToPtr(1)}
	return response
}

func getOpenMeteoAirQualityNormalisedResponse() []wxtypes.AirQualityV2 {
	var (
		co    = 10.0
		ozone = 1.1
		pm10  = 3.4
		pm25  = 1.3
		so2   = 15.5
		no2   = 30.5

		us_aqi = 0
		gb_aqi = 1
		eu_aqi = 1
	)

	return []wxtypes.AirQualityV2{
		{
			Timestamp: time.Date(2023, time.August, 17, 0, 0, 0, 0, time.UTC),
			Pollutants: wxtypes.AirQualityPollutants{
				PM10: &pm10,
				PM25: &pm25,
				CO:   &co,
				O3:   &ozone,
				SO2:  &so2,
				NO2:  &no2,
			},
			AQI: wxtypes.AirQualityIndex{
				EU: &eu_aqi,
				US: &us_aqi,
				GB: &gb_aqi,
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  location.NewYork.Lat,
				Longitude: location.NewYork.Long,
			},
			Metadata: wxtypes.Metadata{
				Provider: "openmeteo",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Date(2023, time.November, 30, 10, 36, 35, 732732000, time.UTC),
				Backfill:  false,
			},
		},
	}
}

func getOpenMeteoWeatherResponse() OpenMeteoWeatherResponse {
	response := OpenMeteoWeatherResponse{
		Latitude:  location.London.Lat,
		Longitude: location.London.Long,
	}

	response.Hourly.Time = []string{
		"2023-08-17T00:00", "2023-08-17T01:00",
	}

	response.Hourly.Temperature2M = []*float64{convertor.NumberToPtr(5.0), convertor.NumberToPtr(10.0)}
	response.Hourly.ApparentTemperature = []*float64{convertor.NumberToPtr(6.0), convertor.NumberToPtr(15.0)}
	response.Hourly.Dewpoint2M = []*float64{convertor.NumberToPtr(10.0), convertor.NumberToPtr(10.0)}
	response.Hourly.RelativeHumidity2M = []*float64{convertor.NumberToPtr(3.5), convertor.NumberToPtr(6.5)}
	response.Hourly.PressureMSL = []*float64{convertor.NumberToPtr(12.5), convertor.NumberToPtr(14.5)}
	response.Hourly.WindSpeed10M = []*float64{convertor.NumberToPtr(30.5), convertor.NumberToPtr(15.5)}
	response.Hourly.WindGusts10M = []*float64{convertor.NumberToPtr(10.0), convertor.NumberToPtr(10.0)}
	response.Hourly.CloudCover = []*float64{convertor.NumberToPtr(0.0), convertor.NumberToPtr(5.0)}
	response.Hourly.Precipitation = []*float64{convertor.NumberToPtr(1.0), convertor.NumberToPtr(15.0), convertor.NumberToPtr(0.0)}
	return response
}

func getOpenMeteoWeatherNormalisedResponse() []wxtypes.WeatherV2 {
	var (
		gust1, gust2                               = 10.0, 10.0
		humidity1, humidity2                       = 3.5, 6.5
		cloudCover1, cloudCover2                   = 0, 5
		pressure1, pressure2                       = 12.5, 14.5
		precipitation1, precipitation2             = 1.0, 15.0
		temperature1, temperature2                 = 5.0, 10.0
		apparentTemperature1, apparentTemperature2 = 6.0, 15.0
		windSpeed1, windSpeed2                     = 30.5, 15.5
	)
	return []wxtypes.WeatherV2{
		{
			Timestamp: time.Date(2023, time.August, 17, 0, 0, 0, 0, time.UTC),
			Temperature: wxtypes.WeatherTemperature{
				Temperature: &temperature1,
				FeelsLike:   &apparentTemperature1,
			},
			Wind: wxtypes.WeatherWind{
				Speed:     &windSpeed1,
				Gust:      &gust1,
				Degree:    nil,
				Direction: nil,
			},
			Humidity:      &humidity1,
			CloudCover:    &cloudCover1,
			UV:            nil,
			Pressure:      &pressure1,
			Precipitation: &precipitation1,
			Coordinates: wxtypes.Coordinates{
				Latitude:  51.5064,
				Longitude: -0.1393,
			},
			Metadata: wxtypes.Metadata{
				Provider: "openmeteo",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Date(2023, time.November, 30, 11, 30, 48, 858963000, time.UTC),
				Backfill:  false,
			},
		},
		{
			Timestamp: time.Date(2023, time.August, 17, 1, 0, 0, 0, time.UTC),
			Temperature: wxtypes.WeatherTemperature{
				Temperature: &temperature2,
				FeelsLike:   &apparentTemperature2,
			},
			Wind: wxtypes.WeatherWind{
				Speed:     &windSpeed2,
				Gust:      &gust2,
				Degree:    nil,
				Direction: nil,
			},
			Humidity:      &humidity2,
			CloudCover:    &cloudCover2,
			UV:            nil,
			Pressure:      &pressure2,
			Precipitation: &precipitation2,
			Coordinates: wxtypes.Coordinates{
				Latitude:  51.5064,
				Longitude: -0.1393,
			},
			Metadata: wxtypes.Metadata{
				Provider: "openmeteo",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Date(2023, time.November, 30, 11, 30, 48, 858963000, time.UTC),
				Backfill:  false,
			},
		},
	}
}
