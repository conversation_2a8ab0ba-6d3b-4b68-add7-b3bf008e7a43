package openmeteo

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"time"

	"golang.org/x/time/rate"
	"llif.org/wxcache/internal/config"
	"llif.org/wxcache/internal/httpclient"
	"llif.org/wxcache/pkg/geo"
	"llif.org/wxcache/pkg/wxtypes"
)

type HourlyData interface {
	GetTimes() []string
}

type OpenMeteo struct {
	httpclient *httpclient.HttpClient
	radius     string
}

func Init(c *config.Config) *OpenMeteo {
	var (
		maxRequestsPerDay = c.RateLimit.OpenMeteo
		rateLimit         = rate.Every(time.Duration(24 * int(time.Hour) / maxRequestsPerDay))

		limiter = rate.NewLimiter(rateLimit, maxRequestsPerDay)
		client  = httpclient.NewHttpClient(limiter)
	)

	return &OpenMeteo{httpclient: client, radius: c.Radius}
}

func (om *OpenMeteo) String() string {
	return "openmeteo"
}

// GetAirQuality resolves given space time and returns the available data as well as a slice of space time it could not resolve
func (om *OpenMeteo) GetAirQuality(ctx context.Context, spacetime []wxtypes.SpaceTime) (result []wxtypes.AirQualityV2, errBuckets []wxtypes.SpaceTime) {
	for _, st := range spacetime {
		res, err := om.getAirQuality(ctx, st)
		if err != nil {
			slog.ErrorContext(ctx, "error fetching air quality", "st", st, "err", err, "provider", om.String())
		}
		if res == nil {
			errBuckets = append(errBuckets, st)
			continue
		}
		normalised, err := normaliseAirQuality(ctx, *res)
		if err != nil {
			slog.ErrorContext(ctx, "error normalising air quality", "err", err, "provider", om.String())
			errBuckets = append(errBuckets, st)
			continue
		}
		result = append(result, normalised...)
	}
	return
}

// GetAirQualityForecast resolves given space time and returns the available forecasted data
func (om *OpenMeteo) GetAirQualityForecast(ctx context.Context, st wxtypes.SpaceTime) ([]wxtypes.AirQualityV2, error) {
	// OpenMeteo has AQ forecast data only 4 days into the future even though they claim to have up to 7
	var latestDate = time.Now().UTC().AddDate(0, 0, 4)
	if st.TimeTo.After(latestDate) {
		st.TimeTo = latestDate
	}

	res, err := om.getAirQuality(ctx, st)
	if err != nil {
		return nil, err
	}

	return normaliseAirQuality(ctx, *res)
}

// GetWeather resolves given space time and returns the available data as well as a slice of space time it could not resolve
func (om *OpenMeteo) GetWeather(ctx context.Context, spacetime []wxtypes.SpaceTime) (result []wxtypes.WeatherV2, errBuckets []wxtypes.SpaceTime) {
	for _, st := range spacetime {
		res, err := om.getWeather(ctx, st)
		if err != nil {
			slog.ErrorContext(ctx, "error fetching weather", "st", st, "err", err, "provider", om.String())
		}
		if res == nil {
			errBuckets = append(errBuckets, st)
			continue
		}
		normalised, err := normaliseWeather(ctx, *res)
		if err != nil {
			slog.ErrorContext(ctx, "error normalising weather", "err", err, "provider", om.String())
			errBuckets = append(errBuckets, st)
			continue
		}
		result = append(result, normalised...)
	}
	return
}

// GetWeatherForecast resolves given space time and returns the available forecasted data
func (om *OpenMeteo) GetWeatherForecast(ctx context.Context, st wxtypes.SpaceTime) ([]wxtypes.WeatherV2, error) {
	// OpenMeteo has weather forecast only for 15 days into the future
	var latestDate = time.Now().UTC().AddDate(0, 0, 15)
	if st.TimeTo.After(latestDate) {
		st.TimeTo = latestDate
	}

	var (
		startDate = st.TimeFrom.UTC().Format("2006-01-02")
		endDate   = st.TimeTo.UTC().Format("2006-01-02")
	)

	hourly := []OpenMeteoHourlyWeatherParameter{
		Temperature2M,
		ApparentTemperature,
		Dewpoint2M,
		RelativeHumidity2M,
		PressureMSL,
		WindSpeed10M,
		WindGusts10M,
		CloudCover,
		Visibility,
		Precipitation,
	}

	daily := []OpenMeteoDailyWeatherParameter{
		UVIndexMax,
	}

	opts := OpenMeteoWeatherForecastRequestOpts{
		Lat:       st.Lat,
		Long:      st.Long,
		Hourly:    &hourly,
		Daily:     &daily,
		StartDate: &startDate,
		EndDate:   &endDate,
	}
	body, err := om.getFromOpenMeteo(wxtypes.ForecastWeather, opts)
	if err != nil {
		return nil, err
	}

	var rr OpenMeteoWeatherForecastResponse
	if err = json.Unmarshal(body, &rr); err != nil {
		return nil, err
	}
	if err = wxtypes.ValidateStruct(rr); err != nil {
		return nil, err
	}
	// If data returned are from sensor out of radius, skip it
	isInRadius := geo.IsWithinRadius(st.Lat, st.Long, rr.Latitude, rr.Longitude, om.radius)
	if !isInRadius {
		slog.InfoContext(ctx, "provider out of radius", "provider", om.String(), "lat", st.Lat, "long", st.Long, "radius", om.radius)
		return nil, nil
	}
	return normaliseForecastWeather(ctx, rr)
}

// GetPollen resolves given space time and returns the available data as well as a slice of space time it could not resolve
func (om *OpenMeteo) GetPollen(ctx context.Context, spacetime []wxtypes.SpaceTime) (result []wxtypes.PollenV2, errBuckets []wxtypes.SpaceTime) {
	for _, st := range spacetime {
		// OpenMeteo supports pollen only in Europe
		if ok, _ := geo.CoordinatesInContinent(geo.Europe, st.Lat, st.Long); !ok {
			errBuckets = append(errBuckets, st)
			continue
		}
		res, err := om.getPollen(ctx, st)
		if err != nil {
			slog.ErrorContext(ctx, "error fetching pollen", "st", st, "err", err, "provider", om.String())
		}
		if res == nil {
			errBuckets = append(errBuckets, st)
			continue
		}
		normalised, err := normalisePollen(ctx, *res)
		if err != nil {
			slog.ErrorContext(ctx, "error normalising pollen", "err", err, "provider", om.String())
			errBuckets = append(errBuckets, st)
			continue
		}
		result = append(result, normalised...)
	}
	return
}

func (om *OpenMeteo) GetPollenForecast(ctx context.Context, st wxtypes.SpaceTime) ([]wxtypes.PollenV2, error) {
	// OpenMeteo has pollen forecast data 4 days into the future, only for Europe
	var latestDate = time.Now().UTC().AddDate(0, 0, 4)
	if st.TimeTo.After(latestDate) {
		st.TimeTo = latestDate
	}

	res, err := om.getPollen(ctx, st)
	if err != nil {
		return nil, err
	}

	return normalisePollen(ctx, *res)
}

func (om *OpenMeteo) getAirQuality(ctx context.Context, st wxtypes.SpaceTime) (res *OpenMeteoAirQualityResponse, err error) {
	var (
		startDate = st.TimeFrom.UTC().Format("2006-01-02")
		endDate   = st.TimeTo.UTC().Format("2006-01-02")
		hourly    = make([]OpenMeteoHourlyAirQualityParameter, 0)
	)

	params := []OpenMeteoHourlyAirQualityParameter{
		CarbonMonoxide,
		PM10,
		PM25,
		NitrogenDioxide,
		SulphurDioxide,
		USAQI,
		EuropeanAQI,
		Ozone,
	}
	hourly = append(hourly, params...)

	opts := OpenMeteoAirQualityRequestOpts{
		Lat:       st.Lat,
		Long:      st.Long,
		Hourly:    &hourly,
		StartDate: &startDate,
		EndDate:   &endDate,
	}
	body, err := om.getFromOpenMeteo(wxtypes.AirQuality, opts)
	if err != nil {
		return
	}

	if err = json.Unmarshal(body, &res); err != nil {
		return
	}
	if err = wxtypes.ValidateStruct(res); err != nil {
		return nil, err
	}

	// If data returned are from sensor out of radius, skip it
	isInRadius := geo.IsWithinRadius(st.Lat, st.Long, res.Latitude, res.Longitude, om.radius)
	if !isInRadius {
		slog.InfoContext(ctx, "provider out of radius", "provider", om.String(), "lat", st.Lat, "long", st.Long, "radius", om.radius)
		return nil, nil
	}

	return res, nil
}

func (om *OpenMeteo) getWeather(ctx context.Context, st wxtypes.SpaceTime) (res *OpenMeteoWeatherResponse, err error) {
	var (
		startDate = st.TimeFrom.UTC().Format("2006-01-02")
		endDate   = st.TimeTo.UTC().Format("2006-01-02")
	)

	params := []OpenMeteoHourlyWeatherParameter{
		Temperature2M,
		ApparentTemperature,
		Dewpoint2M,
		RelativeHumidity2M,
		PressureMSL,
		WindSpeed10M,
		WindGusts10M,
		CloudCover,
		Precipitation,
	}

	opts := OpenMeteoWeatherRequestOpts{
		Lat:       st.Lat,
		Long:      st.Long,
		Hourly:    &params,
		StartDate: &startDate,
		EndDate:   &endDate,
	}

	body, err := om.getFromOpenMeteo(wxtypes.Weather, opts)
	if err != nil {
		return nil, err
	}

	if err = json.Unmarshal(body, &res); err != nil {
		return nil, err
	}
	if err = wxtypes.ValidateStruct(res); err != nil {
		return nil, err
	}
	// If data returned are from sensor out of radius, skip it
	isInRadius := geo.IsWithinRadius(st.Lat, st.Long, res.Latitude, res.Longitude, om.radius)
	if !isInRadius {
		slog.InfoContext(ctx, "provider out of radius", "provider", om.String(), "lat", st.Lat, "long", st.Long, "radius", om.radius)
		return nil, nil
	}
	return
}

func (om *OpenMeteo) getPollen(ctx context.Context, st wxtypes.SpaceTime) (res *OpenMeteoAirQualityResponse, err error) {
	var (
		startDate = st.TimeFrom.UTC().Format("2006-01-02")
		endDate   = st.TimeTo.UTC().Format("2006-01-02")
		hourly    = make([]OpenMeteoHourlyAirQualityParameter, 0)
	)

	params := []OpenMeteoHourlyAirQualityParameter{
		AlderPollen,
		BirchPollen,
		GrassPollen,
		MugwortPollen,
		OlivePollen,
		RagweedPollen,
	}
	hourly = append(hourly, params...)

	options := OpenMeteoAirQualityRequestOpts{
		Lat:       st.Lat,
		Long:      st.Long,
		StartDate: &startDate,
		EndDate:   &endDate,
		Hourly:    &hourly,
	}

	body, err := om.getFromOpenMeteo(wxtypes.Pollen, options)
	if err != nil {
		return
	}

	if err = json.Unmarshal(body, &res); err != nil {
		return
	}
	if err = wxtypes.ValidateStruct(res); err != nil {
		return nil, err
	}
	// If data returned are from sensor out of radius, skip it
	isInRadius := geo.IsWithinRadius(st.Lat, st.Long, res.Latitude, res.Longitude, om.radius)
	if !isInRadius {
		slog.InfoContext(ctx, "provider out of radius", "provider", om.String(), "lat", st.Lat, "long", st.Long, "radius", om.radius)
		return nil, nil
	}
	return res, nil
}

func (om *OpenMeteo) getFromOpenMeteo(t wxtypes.RequestType, opts any) (r []byte, err error) {
	err = wxtypes.ValidateStruct(opts)
	if err != nil {
		return
	}

	req, err := httpclient.BuildRequest(om.getHostFromRequestType(t), om.getEndpointFromRequestType(t), opts)
	if err != nil {
		return
	}

	response, err := om.httpclient.Do(req)
	if err != nil {
		return
	}
	defer response.Body.Close()

	r, err = io.ReadAll(response.Body)
	if err != nil {
		return
	}

	if response.StatusCode/100 != 2 {
		// Open-Meteo returns a specific error body if request returns HTTP 400 BAD REQUEST
		if response.StatusCode == http.StatusBadRequest {
			var errorResponse OpenMeteoErrorResponse
			if err = json.Unmarshal(r, &errorResponse); err != nil {
				return
			}
			err = fmt.Errorf("Open-Meteo returned HTTP 400 due to=%s", errorResponse.Reason)
			return
		}

		err = fmt.Errorf("received non 2xx HTTP response code=%d, body=%s", response.StatusCode, string(r))
		return
	}
	return r, nil
}

func (om *OpenMeteo) getEndpointFromRequestType(t wxtypes.RequestType) string {
	switch t {
	case wxtypes.AirQuality:
		return "/v1/air-quality"
	case wxtypes.ForecastAirQuality:
		return "/v1/air-quality"
	case wxtypes.Pollen:
		return "/v1/air-quality"
	case wxtypes.ForecastPollen:
		return "/v1/air-quality"
	case wxtypes.Weather:
		return "/v1/archive"
	case wxtypes.ForecastWeather:
		return "/v1/forecast"
	default:
		slog.Error("unsupported request type", "provider", om.String(), "request_type", t.String())
		return ""
	}
}

func (om *OpenMeteo) getHostFromRequestType(t wxtypes.RequestType) string {
	switch t {
	case wxtypes.AirQuality:
		return "air-quality-api.open-meteo.com"
	case wxtypes.ForecastAirQuality:
		return "air-quality-api.open-meteo.com"
	case wxtypes.Pollen:
		return "air-quality-api.open-meteo.com"
	case wxtypes.ForecastPollen:
		return "air-quality-api.open-meteo.com"
	case wxtypes.Weather:
		return "archive-api.open-meteo.com"
	case wxtypes.ForecastWeather:
		return "api.open-meteo.com"
	default:
		slog.Error("unsupported host address for request type", "provider", om.String(), "request_type", t.String())
		return ""
	}
}
