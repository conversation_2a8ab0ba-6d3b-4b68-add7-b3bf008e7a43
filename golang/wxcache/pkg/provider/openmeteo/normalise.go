package openmeteo

import (
	"context"
	"log/slog"
	"slices"
	"time"

	"llif.org/wxcache/internal/ckey"
	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/aqi/v2"
	"llif.org/wxcache/pkg/wxtypes"
)

func normaliseAirQuality(ctx context.Context, resp OpenMeteoAirQualityResponse) ([]wxtypes.AirQualityV2, error) {
	var (
		result     = make([]wxtypes.AirQualityV2, 0)
		isBackfill = ctx.Value(ckey.Runtime) == ckey.RuntimeMsgProc
	)

	for i, t := range resp.Hourly.Time {
		timestamp, err := time.Parse("2006-01-02T15:04", t)
		if err != nil {
			slog.WarnContext(ctx, "error parsing timestamp field", "value", t, "err", err)
			continue
		}
		timestamp = timestamp.Truncate(time.Second).UTC()

		aq, err := aqi.CalculateAQI(aqi.AQIConcentration{
			CO:   resp.Hourly.CarbonMonoxide[i],
			O3:   resp.Hourly.Ozone[i],
			PM25: resp.Hourly.PM25[i],
			PM10: resp.Hourly.PM10[i],
			SO2:  resp.Hourly.SulphurDioxide[i],
			NO2:  resp.Hourly.NitrogenDioxide[i],
		})
		if err != nil {
			slog.ErrorContext(ctx, "could not calculate AQI", "err", err)
		}

		na := wxtypes.AirQualityV2{
			Timestamp: timestamp,
			Pollutants: wxtypes.AirQualityPollutants{
				PM10: resp.Hourly.PM10[i],
				PM25: resp.Hourly.PM25[i],
				CO:   resp.Hourly.CarbonMonoxide[i],
				O3:   resp.Hourly.Ozone[i],
				SO2:  resp.Hourly.SulphurDioxide[i],
				NO2:  resp.Hourly.NitrogenDioxide[i],
			},
			AQI: wxtypes.AirQualityIndex{
				US: resp.Hourly.USAQI[i],
				EU: resp.Hourly.EuropeanAQI[i],
				GB: aq.GBIndex,
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  resp.Latitude,
				Longitude: resp.Longitude,
			},
			Metadata: wxtypes.Metadata{
				Provider: "openmeteo",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
				Backfill:  isBackfill,
			},
		}
		if err := wxtypes.ValidateStruct(na); err != nil {
			slog.WarnContext(ctx, "could not validate struct", "struct", na, "provider", "openmeteo", "err", err)
			continue
		}
		result = append(result, na)
	}
	return result, nil
}

func normaliseForecastWeather(ctx context.Context, resp OpenMeteoWeatherForecastResponse) ([]wxtypes.WeatherV2, error) {
	var result = make([]wxtypes.WeatherV2, 0)

	// Go by default uses system's IANA data, which means it does not recognise every single time zone out of the box.
	// We circumvent this by creating a dummy zone with our desired UTC offset.
	loc := time.FixedZone("", resp.UtcOffsetSeconds)

	for i, t := range resp.Hourly.Time {
		timestamp, err := time.ParseInLocation("2006-01-02T15:04", t, loc)
		if err != nil {
			slog.WarnContext(ctx, "error parsing timestamp field", "value", t, "err", err)
			continue
		}
		cloudCover := convertor.FloatPtrToInt(resp.Hourly.CloudCover[i])
		timestamp = timestamp.Truncate(time.Second).UTC()

		// OpenMeteo provides UV index in the forecast only as single point daily value.
		// We take the current hourly bucket's day and get the day's UV index forecast and populate it into the hourly bucket.
		var (
			uvIndex  *int = nil
			posIndex int  = slices.Index(resp.Daily.Time, timestamp.Format("2006-01-02"))
		)
		if posIndex != -1 {
			uvIndex = convertor.NumberToPtr(convertor.FloatPtrToInt(resp.Daily.UVIndexMax[posIndex]))
		}

		// OpenMeteo provides visibility in meters, we convert it to km
		var visibility *float64 = nil
		if resp.Hourly.Visibility[i] != nil {
			vis := *resp.Hourly.Visibility[i] / 1000
			visibility = convertor.NumberToPtr(vis)
		}

		nw := wxtypes.WeatherV2{
			Timestamp: timestamp,
			Temperature: wxtypes.WeatherTemperature{
				Temperature: resp.Hourly.Temperature2M[i],
				FeelsLike:   resp.Hourly.ApparentTemperature[i],
			},
			Wind: wxtypes.WeatherWind{
				Speed: resp.Hourly.WindSpeed10M[i],
				Gust:  resp.Hourly.WindGusts10M[i],
			},
			Humidity:      resp.Hourly.RelativeHumidity2M[i],
			CloudCover:    &cloudCover,
			Pressure:      resp.Hourly.PressureMSL[i],
			Visiblity:     visibility,
			UV:            uvIndex,
			Precipitation: resp.Hourly.Precipitation[i],
			Coordinates: wxtypes.Coordinates{
				Latitude:  resp.Latitude,
				Longitude: resp.Longitude,
			},
			Metadata: wxtypes.Metadata{
				Provider: "openmeteo",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
				Backfill:  false,
			},
		}
		if err := wxtypes.ValidateStruct(nw); err != nil {
			slog.WarnContext(ctx, "could not validate struct", "struct", nw, "provider", "openmeteo", "err", err)
			continue
		}
		result = append(result, nw)
	}
	return result, nil
}

func normaliseWeather(ctx context.Context, resp OpenMeteoWeatherResponse) ([]wxtypes.WeatherV2, error) {
	result := make([]wxtypes.WeatherV2, 0)
	isBackfill := ctx.Value(ckey.Runtime) == ckey.RuntimeMsgProc

	for i, t := range resp.Hourly.Time {
		timestamp, err := time.Parse("2006-01-02T15:04", t)
		if err != nil {
			slog.WarnContext(ctx, "error parsing timestamp field", "value", t, "err", err)
			continue
		}
		timestamp = timestamp.Truncate(time.Second).UTC()

		cloudCover := convertor.FloatPtrToInt(resp.Hourly.CloudCover[i])

		nw := wxtypes.WeatherV2{
			Timestamp: timestamp,
			Temperature: wxtypes.WeatherTemperature{
				Temperature: resp.Hourly.Temperature2M[i],
				FeelsLike:   resp.Hourly.ApparentTemperature[i],
			},
			Wind: wxtypes.WeatherWind{
				Speed: resp.Hourly.WindSpeed10M[i],
				Gust:  resp.Hourly.WindGusts10M[i],
			},
			Humidity:      resp.Hourly.RelativeHumidity2M[i],
			CloudCover:    &cloudCover,
			Pressure:      resp.Hourly.PressureMSL[i],
			Precipitation: resp.Hourly.Precipitation[i],
			Coordinates: wxtypes.Coordinates{
				Latitude:  resp.Latitude,
				Longitude: resp.Longitude,
			},
			Metadata: wxtypes.Metadata{
				Provider: "openmeteo",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
				Backfill:  isBackfill,
			},
		}
		if err := wxtypes.ValidateStruct(nw); err != nil {
			slog.WarnContext(ctx, "could not validate struct", "struct", nw, "provider", "openmeteo", "err", err)
			continue
		}
		result = append(result, nw)
	}
	return result, nil
}

func normalisePollen(ctx context.Context, resp OpenMeteoAirQualityResponse) ([]wxtypes.PollenV2, error) {
	var (
		result     = make([]wxtypes.PollenV2, 0)
		isBackfill = ctx.Value(ckey.Runtime) == ckey.RuntimeMsgProc
	)

	for i, t := range resp.Hourly.Time {
		timestamp, err := time.Parse("2006-01-02T15:04", t)
		if err != nil {
			slog.WarnContext(ctx, "error parsing timestamp field", "value", t, "err", err)
			continue
		}
		timestamp = timestamp.Truncate(time.Second).UTC()

		var (
			tree  = &wxtypes.PollenSpecies{}
			weed  = &wxtypes.PollenSpecies{}
			grass = &wxtypes.PollenSpecies{}
		)

		// Count weeds (ragweed_pollen, mugwort_pollen)
		if len(resp.Hourly.RagweedPollen) != 0 && resp.Hourly.RagweedPollen[i] != nil {
			value := convertor.FloatPtrToInt(resp.Hourly.RagweedPollen[i])
			weed.Count += value
			weed.Subspecies = append(weed.Subspecies, wxtypes.PollenSubspecies{
				Name:  "ragweed", // @TODO: maybe have an enum of values rather than strings?
				Count: value,
			})
		}
		if len(resp.Hourly.MugwortPollen) != 0 && resp.Hourly.MugwortPollen[i] != nil {
			value := convertor.FloatPtrToInt(resp.Hourly.MugwortPollen[i])
			weed.Count += value
			weed.Subspecies = append(weed.Subspecies, wxtypes.PollenSubspecies{
				Name:  "mugwort",
				Count: value,
			})
		}

		// Count trees (olive_pollen, birch_pollen, alder_pollen)
		if len(resp.Hourly.OlivePollen) != 0 && resp.Hourly.OlivePollen[i] != nil {
			value := convertor.FloatPtrToInt(resp.Hourly.OlivePollen[i])
			tree.Count += value
			tree.Subspecies = append(tree.Subspecies, wxtypes.PollenSubspecies{
				Name:  "olive",
				Count: value,
			})
		}
		if len(resp.Hourly.BirchPollen) != 0 && resp.Hourly.BirchPollen[i] != nil {
			value := convertor.FloatPtrToInt(resp.Hourly.BirchPollen[i])
			tree.Count += value
			tree.Subspecies = append(tree.Subspecies, wxtypes.PollenSubspecies{
				Name:  "birch",
				Count: value,
			})
		}
		if len(resp.Hourly.AlderPollen) != 0 && resp.Hourly.AlderPollen[i] != nil {
			value := convertor.FloatPtrToInt(resp.Hourly.AlderPollen[i])
			tree.Count += value
			tree.Subspecies = append(tree.Subspecies, wxtypes.PollenSubspecies{
				Name:  "alder",
				Count: value,
			})
		}

		// Count grass (grass_pollen)
		if len(resp.Hourly.GrassPollen) != 0 && resp.Hourly.GrassPollen[i] != nil {
			value := convertor.FloatPtrToInt(resp.Hourly.GrassPollen[i])
			grass.Count += value
			grass.Subspecies = append(grass.Subspecies, wxtypes.PollenSubspecies{
				Name:  "grass",
				Count: value,
			})
		}

		np := wxtypes.PollenV2{
			Timestamp: timestamp,
			Tree:      tree,
			Weed:      weed,
			Grass:     grass,
			Coordinates: wxtypes.Coordinates{
				Latitude:  resp.Latitude,
				Longitude: resp.Longitude,
			},
			Metadata: wxtypes.Metadata{
				Provider: "openmeteo",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
				Backfill:  isBackfill,
			},
		}
		if err := wxtypes.ValidateStruct(np); err != nil {
			slog.WarnContext(ctx, "could not validate struct", "struct", np, "provider", "openmeteo", "err", err)
			continue
		}
		result = append(result, np)
	}
	return result, nil
}
