package visualcrossing

type VisualCrossingWeatherResponse struct {
	Latitude        float64                    `json:"latitude"`
	Longitude       float64                    `json:"longitude"`
	ResolvedAddress string                     `json:"resolvedAddress"`
	Address         string                     `json:"address"`
	TimeZone        string                     `json:"timezone"`
	TzOffset        float64                    `json:"tzoffset"` // The time zone offset in hours
	Source          string                     `json:"source"`   // Type of weather data used for this weather object
	Days            []VisualCrossingWeatherDay `json:"days"`
}

type VisualCrossingWeatherDay struct {
	VisualCrossingWeatherBucket                               // The day includes a summary of the whole day
	Hours                       []VisualCrossingWeatherBucket `json:"hours"` // Individual hourly buckets
}

type VisualCrossingWeatherBucket struct {
	DateTime       string    `json:"datetime"`       // ISO 8601 formatted date/time in local time zone
	DateTimeEpoch  float64   `json:"datetimeEpoch"`  // Number of seconds since Jan 1, 1970 in UTC time
	Temperature    *float64  `json:"temp"`           // Temperature at the location, daily values are average
	WindSpeed      *float64  `json:"windspeed"`      // Sustained wind speed measured as average over preceding 1-2 minutes
	WindGust       *float64  `json:"windgust"`       // Instantaneous wind speed at a location
	WindDirection  *float64  `json:"winddir"`        // Direction from which the wind is blowing in degrees
	CloudCover     *float64  `json:"cloudcover"`     // How much of the sky is covered in cloud (0-100%)
	Conditions     *string   `json:"conditions"`     // Textual representation of the weather conditions
	Description    *string   `json:"description"`    // Longer text descriptions suitable for displaying in weather displays
	Dew            *float64  `json:"dew"`            // Dew point temperature
	FeelsLike      *float64  `json:"feelslike"`      // What the temperature feels like accounting for heat index or wind chill
	Humidity       *float64  `json:"humidity"`       // Relative humidity in %
	Icon           *string   `json:"icon"`           // A fixed, machine readable summary that can be used to display an icon
	OffsetSeconds  *float64  `json:"offsetseconds"`  // Time zone offset for this weather data object in seconds
	Precip         *float64  `json:"precip"`         // Amount of liquid precipitation that fell or is predicted to fall
	PrecipRemote   *float64  `json:"precipremote"`   // Radar estimated precipitation amount
	PrecipProb     *float64  `json:"precipprob"`     // Likelihood of measurable precipitation (0-100%)
	PrecipType     *[]string `json:"preciptype"`     // Array indicating the type(s) of precipitation (rain, snow, etc.)
	Reflectivity   *float64  `json:"reflectivity"`   // Estimates of radar-based reflectivity values indicating precipitation intensity
	Pressure       *float64  `json:"pressure"`       // Sea level atmospheric or barometric pressure in millibars
	Snow           *float64  `json:"snow"`           // Amount of snow that fell or is predicted to fall
	SnowDepth      *float64  `json:"snowdepth"`      // Depth of snow on the ground
	UVIndex        *float64  `json:"uvindex"`        // Value between 0 and 10 indicating the level of UV exposure
	UVIndex2       *float64  `json:"uvindex2"`       // Alternative UV index using US National Weather Service algorithms
	Visibility     *float64  `json:"visibility"`     // Distance at which distant objects are visible (km)
	SolarRadiation *float64  `json:"solarradiation"` // Solar radiation power (W/m2) at the instantaneous moment
	SolarEnergy    *float64  `json:"solarenergy"`    // Total energy from the sun (MJ/m2) that builds up over an hour or day
	SevereRisk     *float64  `json:"severerisk"`     // Value between 0 and 100 representing risk of convective storms
	CAPE           *float64  `json:"cape"`           // Convective available potential energy for thunderstorms
	CIN            *float64  `json:"cin"`            // Convective inhibition representing atmospheric tendency to prevent thunderstorms
}
