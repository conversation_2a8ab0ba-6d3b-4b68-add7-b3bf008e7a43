package weatherapi

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"golang.org/x/exp/rand"
	"llif.org/wxcache/pkg/testutil"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/testutil/testsetup"
	"llif.org/wxcache/pkg/wxtypes"
)

func TestWeatherAPIReturnsAllTypesShouldPassIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		ctx                           = context.Background()
		wapi      *WeatherAPI         = Init(c)
		locations []location.Location = location.GetAll()
	)

	t.Run("Air Quality", func(t *testing.T) {
		var (
			spacetime = make([]wxtypes.SpaceTime, 0)
			timeNow   = time.Now().UTC()
		)

		for _, loc := range locations {
			spacetime = append(spacetime, wxtypes.SpaceTime{
				Lat:  loc.Lat,
				Long: loc.Long,
				// WeatherAPI only supports real time air quality
				TimeFrom: timeNow,
				TimeTo:   timeNow,
			})
		}

		res, errBuckets := wapi.GetAirQuality(ctx, spacetime)
		require.Empty(t, errBuckets)
		require.NotEmpty(t, res)
	})

	t.Run("Air Quality Forecast", func(t *testing.T) {
		timeFrom := time.Now().UTC()

		for _, loc := range locations {
			max_bucket_count := 10*24 - 1
			hours := rand.Intn(max_bucket_count) + 1
			var st = wxtypes.SpaceTime{
				Lat:      loc.Lat,
				Long:     loc.Long,
				TimeFrom: timeFrom,
				TimeTo:   timeFrom.Add(time.Duration(hours) * time.Hour),
			}
			res, err := wapi.GetAirQualityForecast(ctx, st)
			require.NoError(t, err)
			require.NotEmpty(t, res)
		}
	})

	t.Run("Weather", func(t *testing.T) {
		var spacetime = make([]wxtypes.SpaceTime, 0)

		for _, loc := range locations {
			spacetime = append(spacetime, wxtypes.SpaceTime{
				Lat:      loc.Lat,
				Long:     loc.Long,
				TimeFrom: testutil.GetISOTimeFromString("2023-07-31T11:59:04Z"),
				TimeTo:   testutil.GetISOTimeFromString("2023-07-31T12:10:04Z"),
			})
		}

		res, errBuckets := wapi.GetWeather(ctx, spacetime)
		require.Empty(t, errBuckets)
		require.NotEmpty(t, res)
	})
}

func TestWeatherAPIReturnsPartialDataForForecastShouldPassIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		ctx                           = context.Background()
		wapi      *WeatherAPI         = Init(c)
		locations []location.Location = location.GetAll()
	)

	t.Run("AirQuality", func(t *testing.T) {
		var (
			timeFrom = time.Now().UTC()
			timeTo   = timeFrom.AddDate(0, 0, 15) // way over the allowed range
		)

		for _, loc := range locations {
			var st = wxtypes.SpaceTime{
				Lat:      loc.Lat,
				Long:     loc.Long,
				TimeFrom: timeFrom,
				TimeTo:   timeTo,
			}
			res, err := wapi.GetAirQualityForecast(ctx, st)
			require.NoError(t, err)
			require.NotEmpty(t, res)
		}
	})
}

func TestWeatherAPIPollenNotImplementedShouldReturnError(t *testing.T) {
	var (
		wapi = WeatherAPI{}
		ctx  = context.Background()
	)
	response, errBuckets := wapi.GetPollen(
		ctx,
		[]wxtypes.SpaceTime{
			{
				Lat:  location.NewYork.Lat,
				Long: location.NewYork.Long,
			},
		},
	)
	require.NotEmpty(t, errBuckets)
	require.Nil(t, response)
}
