package weatherapi

type WeatherAPILocation struct {
	Latitude       float64 `json:"lat"`             // Latitude in decimal degree
	Longitude      float64 `json:"lon"`             // Longitude in decimal degree
	Name           string  `json:"name"`            // Location name
	Region         string  `json:"region"`          // Region or state of the location, if available
	Country        string  `json:"country"`         // Location country
	TZID           string  `json:"tz_id"`           // Time zone name
	LocaltimeEpoch int     `json:"localtime_epoch"` // Local date and time in unix time
	Localtime      string  `json:"localtime"`       // Local date and time
}

type WeatherAPIWeatherAlert struct {
	Headline    string `json:"headline"`
	MessageType string `json:"msgType"`
	Severity    string `json:"severity"`
	Urgency     string `json:"urgency"`
	Areas       string `json:"areas"`
	Category    string `json:"category"`
	Certainty   string `json:"certainty"`
	Event       string `json:"event"`
	Note        string `json:"note"`
	Effective   string `json:"effective"` // ISO timestamp of the start of the alert
	Expires     string `json:"expires"`   // ISO timestamp of when the alert is ending
	Desc        string `json:"desc"`
	Instruction string `json:"instruction"`
}

type WeatherAPIAirQuality struct {
	CO           float64 `json:"co"`             // Carbon Monoxide (μg/m3)
	O3           float64 `json:"o3"`             // Ozone (μg/m3)
	NO2          float64 `json:"no2"`            // Nitrogen dioxide (μg/m3)
	SO2          float64 `json:"so2"`            // Sulphur dioxide (μg/m3)
	PM25         float64 `json:"pm2_5"`          // PM2.5 (μg/m3)
	PM10         float64 `json:"pm10"`           // PM10 (μg/m3)
	USEpaIndex   int     `json:"us-epa-index"`   // US - EPA standard (see table here https://www.weatherapi.com/docs/#intro-aqi)
	GBDefraIndex int     `json:"gb-defra-index"` // UK Defra Index (see table here https://www.weatherapi.com/docs/#intro-aqi)
}

type WeatherAPICurrent struct {
	LastUpdated      string  `json:"last_updated"`       // Local time when the real time data was updated
	LastUpdatedEpoch int     `json:"last_updated_epoch"` // Local time when the real time data was updated in unix time
	TempC            float64 `json:"temp_c"`             // Temperature in celsius
	TempF            float64 `json:"temp_f"`             // Temperature in fahrenheit
	FeelslikeC       float64 `json:"feelslike_c"`        // Feels like temperature in celsius
	FeelslikeF       float64 `json:"feelslike_f"`        // Feels like temperature in fahrenheit
	Condition        struct {
		Text string `json:"text"` // Weather condition text
		Icon string `json:"icon"` // Weather icon url
		Code int    `json:"code"` // Weather condition unique code
	} `json:"condition"`
	WindMPH         float64               `json:"wind_mph"`    // Wind speed in miles per hour
	WindKPH         float64               `json:"wind_kph"`    // Wind speed in kilometer per hour
	WindDegree      int                   `json:"wind_degree"` // Wind direction in degrees
	WindDirection   string                `json:"wind_dir"`    // Wind direction as 16 point compass. e.g.: NSW
	PressureMB      float64               `json:"pressure_mb"` // Pressure in millibars
	PressureIN      float64               `json:"pressure_in"` // Pressure in inches
	PrecipitationMM float64               `json:"precip_mm"`   // Precipitation amount in millimeters
	PrecipitationIN float64               `json:"precip_in"`   // Precipitation amount in inches
	Humidity        int                   `json:"humidity"`    // Humidity as percentage
	Cloud           int                   `json:"cloud"`       // Cloud cover as percentage
	IsDay           int                   `json:"is_day"`      // 1 = Yes 0 = No
	UV              float64               `json:"uv"`          // UV Index
	GustMPH         float64               `json:"gust_mph"`    // Wind gust in miles per hour
	GustKPH         float64               `json:"gust_kph"`    // Wind gust in kilometer per hour
	AirQuality      *WeatherAPIAirQuality `json:"air_quality"`
}

type WeatherAPICurrentEnvironmentResponse struct {
	Location WeatherAPILocation `json:"location"`
	Current  WeatherAPICurrent  `json:"current"`
}

type WeatherAPIEnvironmentResponse struct {
	Location WeatherAPILocation `json:"location"`
	Forecast struct {
		Forecastday []WeatherAPIForecastDay `json:"forecastday"`
	} `json:"forecast"`
}

type WeatherAPIForecastDay struct {
	Date      string                   `json:"date"`
	DateEpoch int                      `json:"date_epoch"`
	Day       WeatherAPIForecastDayDay `json:"day"`
	Astro     WeatherAPIForecastAstro  `json:"astro"`
	Hour      []WeatherAPIForecastHour `json:"hour"`
}

type WeatherAPIForecastDayDay struct {
	MaxTempC               float64 `json:"maxtemp_c"`      // Maximum temperature in celsius for the day
	MaxTempF               float64 `json:"maxtemp_f"`      // Maximum temperature in fahrenheit for the day
	MinTempC               float64 `json:"mintemp_c"`      // Minimum temperature in celsius for the day
	MinTempF               float64 `json:"mintemp_f"`      // Minimum temperature in fahrenheit for the day
	AverageTempC           float64 `json:"avgtemp_c"`      // Average temperature in celsius for the day
	AverageTempF           float64 `json:"avgtemp_f"`      // Average temperature in fahrenheit for the day
	MaxWindMPH             float64 `json:"maxwind_mph"`    // Maximum wind speed in miles per hour
	MaxWindKPH             float64 `json:"maxwind_kph"`    // Maximum wind speed in kilometer per hour
	TotalPrecipitationMM   float64 `json:"totalprecip_mm"` // Total precipitation in milimeter
	TotalPrecipitationIN   float64 `json:"totalprecip_in"` // Total precipitation in inches
	AverageVisibilityKM    float64 `json:"avgvis_km"`      // Average visibility in kilometer
	AverageVisibilityMiles float64 `json:"avgvis_miles"`   // Average visibility in miles
	AverageHumidity        float64 `json:"avghumidity"`    // Average humidity as percentage
	Condition              struct {
		Text string `json:"text"` // Weather condition text
		Icon string `json:"icon"` // Weather condition icon
		Code int    `json:"code"` // Weather condition code
	} `json:"condition"`
	UV float64 `json:"uv"` // UV Index
}

type WeatherAPIForecastAstro struct {
	Sunrise          string  `json:"sunrise"`           // Sunrise time in format HH:MM [AM/PM], e.g. "04:52 AM"
	Sunset           string  `json:"sunset"`            // Sunset time in format HH:MM [AM/PM], e.g. "04:52 AM"
	Moonrise         string  `json:"moonrise"`          // Moonrise time in format HH:MM [AM/PM], e.g. "04:52 AM"
	Moonset          string  `json:"moonset"`           // Moonset time in format HH:MM [AM/PM], e.g. "04:52 AM"
	MoonIllumination float64 `json:"moon_illumination"` // Moon illumination as percentage

	// Moon phases
	//
	// Values returned: "New Moon", "Waxing Crescent", "FirstQuarter", "Waxing Gibbous", "Full Moon", "Waning Gibbous", "Last Quarter", "Waning Crescent"
	MoonPhase string `json:"moon_phase"`
}

type WeatherAPIForecastHour struct {
	TimeEpoch int     `json:"time_epoch"` // Time as epoch
	Time      string  `json:"time"`       // Date and time
	TempC     float64 `json:"temp_c"`     // Temperature in celsius
	TempF     float64 `json:"temp_f"`     // Temperature in fahrenheit
	Condition struct {
		Text string `json:"text"` // Weather condition text
		Icon string `json:"icon"` // Weather condition icon
		Code int    `json:"code"` // Weather condition code
	} `json:"condition"`
	WindMph         float64               `json:"wind_mph"`       // Maximum wind speed in miles per hour
	WindKph         float64               `json:"wind_kph"`       // Maximum wind speed in kilometer per hour
	WindDegree      int                   `json:"wind_degree"`    // Wind direction in degrees
	WindDir         string                `json:"wind_dir"`       // Wind direction as 16 point compass. e.g.: NSW
	PressureMb      float64               `json:"pressure_mb"`    // Pressure in millibars
	PressureIn      float64               `json:"pressure_in"`    // Pressure in inches
	PrecipitationMm float64               `json:"precip_mm"`      // Precipitation amount in millimeters
	PrecipitationIn float64               `json:"precip_in"`      // Precipitation amount in inches
	Humidity        int                   `json:"humidity"`       // Humidity as percentage
	Cloud           int                   `json:"cloud"`          // Cloud cover as percentage
	FeelslikeC      float64               `json:"feelslike_c"`    // Feels like temperature as celcius
	FeelslikeF      float64               `json:"feelslike_f"`    // Feels like temperature as fahrenheit
	WindchillC      float64               `json:"windchill_c"`    // Windchill temperature in celcius
	WindchillF      float64               `json:"windchill_f"`    // Windchill temperature in fahrenheit
	HeatindexC      float64               `json:"heatindex_c"`    // Heat index in celcius
	HeatindexF      float64               `json:"heatindex_f"`    // Heat index in fahrenheit
	DewpointC       float64               `json:"dewpoint_c"`     // Dew point in celcius
	DewpointF       float64               `json:"dewpoint_f"`     // Dew point in fahrenheit
	WillItRain      int                   `json:"will_it_rain"`   // Will it will rain or not: 1 = Yes 0 = No
	WillItSnow      int                   `json:"will_it_snow"`   // Will it snow or not: 1 = Yes 0 = No
	IsDay           int                   `json:"is_day"`         // 1 = Yes 0 = No
	VisibilityKm    float64               `json:"vis_km"`         // Visibility in kilometer
	VisibilityMiles float64               `json:"vis_miles"`      // Visibility in miles
	ChanceOfRain    int                   `json:"chance_of_rain"` // Chance of rain as percentage
	ChanceOfSnow    int                   `json:"chance_of_snow"` // Chance of snow as percentage
	GustMph         float64               `json:"gust_mph"`       // Wind gust in miles per hour
	GustKph         float64               `json:"gust_kph"`       // Wind gust in kilometer per hour
	UV              float64               `json:"uv"`             // UV Index
	AirQuality      *WeatherAPIAirQuality `json:"air_quality"`    // Only returned if aqi=yes query applied
}

type weatherAPIAQIBucket struct {
	Location   WeatherAPILocation
	AirQuality WeatherAPIAirQuality
	Timestamp  string
}
