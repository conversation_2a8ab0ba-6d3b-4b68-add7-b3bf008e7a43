package weatherapi

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"time"

	"golang.org/x/time/rate"
	"llif.org/wxcache/internal/config"
	"llif.org/wxcache/internal/httpclient"
	"llif.org/wxcache/internal/spacetime"
	"llif.org/wxcache/pkg/geo"
	"llif.org/wxcache/pkg/wxtypes"
)

const (
	WeatherBuffer  = time.Minute * 30
	requestTimeout = time.Second * 10
	hourLimit      = 1 * time.Hour

	host = "api.weatherapi.com"
)

type WeatherAPI struct {
	APIKey     string
	httpclient *httpclient.HttpClient
	radius     string
}

func Init(c *config.Config) *WeatherAPI {
	var (
		maxRequestsPerDay = c.RateLimit.WeatherAPI
		rateLimit         = rate.Every(time.Duration(24 * int(time.Hour) / maxRequestsPerDay))

		limiter = rate.NewLimiter(rateLimit, maxRequestsPerDay)
		client  = httpclient.NewHttpClient(limiter)
	)
	return &WeatherAPI{
		APIKey:     c.APIKey.WeatherAPI,
		httpclient: client,
		radius:     c.Radius,
	}
}

// GetAirQuality resolves given space time and returns the available data as well as a slice of space time it could not resolve
func (w *WeatherAPI) GetAirQuality(ctx context.Context, st []wxtypes.SpaceTime) (result []wxtypes.AirQualityV2, errBuckets []wxtypes.SpaceTime) {
	// WeatherAPI only supports daily buckets
	dailySpacetime := spacetime.BucketSpaceTimeIntoDailyBuckets(st)
	for _, st := range dailySpacetime {
		data, err := w.getAirQuality(ctx, st)
		if err != nil {
			slog.ErrorContext(ctx, "error fetching air quality", "st", st, "err", err, "provider", w.String())
		}
		if len(data) == 0 {
			errBuckets = append(errBuckets, st)
			continue
		}
		result = append(result, data...)
	}
	return
}

// GetWeather resolves given space time and returns the available data as well as a slice of space time it could not resolve
func (w *WeatherAPI) GetWeather(ctx context.Context, st []wxtypes.SpaceTime) (result []wxtypes.WeatherV2, errBuckets []wxtypes.SpaceTime) {
	// WeatherAPI only supports daily buckets
	dailySpacetime := spacetime.BucketSpaceTimeIntoDailyBuckets(st)
	for _, st := range dailySpacetime {
		data, err := w.getWeather(ctx, st)
		if err != nil {
			slog.ErrorContext(ctx, "error fetching weather", "st", st, "err", err, "provider", w.String())
		}
		if len(data) == 0 {
			errBuckets = append(errBuckets, st)
			continue
		}
		result = append(result, data...)
	}
	return
}

// GetPollen resolves given space time and returns the available data as well as a slice of space time it could not resolve
func (w *WeatherAPI) GetPollen(ctx context.Context, st []wxtypes.SpaceTime) (result []wxtypes.PollenV2, errBuckets []wxtypes.SpaceTime) {
	// WeatherAPI only supports daily buckets
	dailySpacetime := spacetime.BucketSpaceTimeIntoDailyBuckets(st)
	for _, st := range dailySpacetime {
		data, err := w.getPollen(ctx, st)
		if err != nil {
			slog.ErrorContext(ctx, "error fetching pollen", "st", st, "err", err, "provider", w.String())
		}
		if len(data) == 0 {
			errBuckets = append(errBuckets, st)
			continue
		}
		result = append(result, data...)
	}
	return
}

func (om *WeatherAPI) GetPollenForecast(ctx context.Context, spacetime wxtypes.SpaceTime) ([]wxtypes.PollenV2, error) {
	return nil, errors.New("not implemented")
}

// GetAirQualityForecast resolves given space time and returns the available forecasted data
func (w *WeatherAPI) GetAirQualityForecast(ctx context.Context, st wxtypes.SpaceTime) (r []wxtypes.AirQualityV2, err error) {
	var (
		currentTime = time.Now().UTC().Truncate(time.Hour)
		timeHourAgo = currentTime.Add(-time.Hour)

		timeFrom     = st.TimeFrom.UTC()
		timeTo       = st.TimeTo.UTC()
		numberOfDays = len(spacetime.SpreadToDailyTimeBuckets(timeFrom, timeTo))
	)

	if timeFrom.Before(timeHourAgo) {
		// Return empty data if the data is in the past
		// @TODO: Add out of range error
		slog.InfoContext(ctx, "WeatherAPI AQ forecast does not support historical data", "time_from", timeFrom, "time_to", timeTo)
		return
	}

	// WeatherAPI only supports 10 days of forecast
	if numberOfDays > 10 {
		numberOfDays = 10
	}

	fetchAqi := "yes"
	opts := WeatherAPIForecastRequestOpts{
		APIKey: w.APIKey,
		LatLon: fmt.Sprintf("%f,%f", st.Lat, st.Long),
		AQI:    &fetchAqi,
		Days:   &numberOfDays,
	}

	body, err := w.getFromWeatherAPI(wxtypes.ForecastAirQuality, opts)
	if err != nil {
		return
	}

	res := WeatherAPIEnvironmentResponse{}
	if err = json.Unmarshal(body, &res); err != nil {
		return
	}

	if err = wxtypes.ValidateStruct(res); err != nil {
		return nil, err
	}

	// If data returned are from sensor out of radius, skip it
	isInRadius := geo.IsWithinRadius(st.Lat, st.Long, res.Location.Latitude, res.Location.Longitude, w.radius)
	if !isInRadius {
		slog.InfoContext(ctx, "provider out of radius", "provider", w.String(), "lat", st.Lat, "long", st.Long, "radius", w.radius)
		return
	}
	aqs := []weatherAPIAQIBucket{}
	for _, forecastDay := range res.Forecast.Forecastday {
		for _, forecastHour := range forecastDay.Hour {

			aq := weatherAPIAQIBucket{AirQuality: *forecastHour.AirQuality, Timestamp: forecastHour.Time, Location: res.Location}
			if err := wxtypes.ValidateStruct(aq); err != nil {
				slog.WarnContext(ctx, "could not validate AQ bucket", "provider", w.String(), "err", err, "struct", aq)
				continue
			}
			aqs = append(aqs, aq)
		}

	}
	return normaliseAirQuality(ctx, aqs)
}

// GetWeatherForecast resolves given space time and returns the available forecasted data
func (w *WeatherAPI) GetWeatherForecast(ctx context.Context, st wxtypes.SpaceTime) (r []wxtypes.WeatherV2, err error) {
	return nil, errors.New("not implemented")
}

func (w *WeatherAPI) getAirQuality(ctx context.Context, st wxtypes.SpaceTime) (r []wxtypes.AirQualityV2, err error) {
	var (
		currentTime    = time.Now().UTC().Truncate(time.Hour)
		timeHourAgo    = currentTime.Add(-time.Hour)
		timeHourFuture = currentTime.Add(time.Hour)

		timeFrom = st.TimeFrom.UTC()
		timeTo   = st.TimeTo.UTC()
	)
	if timeFrom.Before(timeHourAgo) || timeTo.After(timeHourFuture) {
		// Return empty data if the data is in the future
		// @TODO: Add out of range error
		slog.WarnContext(ctx, "WeatherAPI only supports AQ in real time", "time_from", timeFrom, "time_to", timeTo)
		return
	}

	fetchAqi := "yes"
	opts := WeatherAPICurrentRequestOpts{
		APIKey: w.APIKey,
		LatLon: fmt.Sprintf("%f,%f", st.Lat, st.Long),
		AQI:    &fetchAqi,
	}

	body, err := w.getFromWeatherAPI(wxtypes.AirQuality, opts)
	if err != nil {
		return
	}

	res := WeatherAPICurrentEnvironmentResponse{}
	if err = json.Unmarshal(body, &res); err != nil {
		return
	}

	// If data returned are from sensor out of radius, skip it
	isInRadius := geo.IsWithinRadius(st.Lat, st.Long, res.Location.Latitude, res.Location.Longitude, w.radius)
	if !isInRadius {
		slog.InfoContext(ctx, "provider out of radius", "provider", w.String(), "lat", st.Lat, "long", st.Long, "radius", w.radius)
		return
	}
	if res.Current.AirQuality == nil {
		err = fmt.Errorf("air quality request did not contain air quality data in body=%s", string(body))
		return
	}

	if err = wxtypes.ValidateStruct(res); err != nil {
		return nil, err
	}
	return normaliseAirQuality(ctx, []weatherAPIAQIBucket{
		{Location: res.Location, AirQuality: *res.Current.AirQuality, Timestamp: res.Current.LastUpdated}})
}

func (w *WeatherAPI) getWeather(ctx context.Context, st wxtypes.SpaceTime) (r []wxtypes.WeatherV2, err error) {
	var (
		timeBuckets = spacetime.SpreadToDailyTimeBuckets(st.TimeFrom.UTC(), st.TimeTo.UTC())
		result      = make([]WeatherAPIEnvironmentResponse, 0, len(timeBuckets))
	)

	for _, bucket := range timeBuckets {
		var opts = WeatherAPIHistoryRequestOpts{
			APIKey:   w.APIKey,
			LatLon:   fmt.Sprintf("%f,%f", st.Lat, st.Long),
			TimeFrom: bucket.Format("2006-01-02"),
		}
		var body []byte
		body, err = w.getFromWeatherAPI(wxtypes.Weather, opts)
		if err != nil {
			return nil, err
		}

		res := WeatherAPIEnvironmentResponse{}
		if err = json.Unmarshal(body, &res); err != nil {
			return nil, err
		}
		if err = wxtypes.ValidateStruct(res); err != nil {
			return nil, err
		}
		// If data returned are from sensor out of radius, skip it
		isInRadius := geo.IsWithinRadius(st.Lat, st.Long, res.Location.Latitude, res.Location.Longitude, w.radius)
		if !isInRadius {
			slog.InfoContext(ctx, "provider out of radius", "provider", w.String(), "lat", st.Lat, "long", st.Long, "radius", w.radius)
			return
		}
		result = append(result, res)
	}
	return normaliseWeather(ctx, result)
}

func (w *WeatherAPI) getPollen(ctx context.Context, st wxtypes.SpaceTime) (r []wxtypes.PollenV2, err error) {
	return nil, errors.New("not implemented")
}

func (w *WeatherAPI) String() string {
	return "weatherapi"
}

func (w *WeatherAPI) getFromWeatherAPI(t wxtypes.RequestType, opts any) (r []byte, err error) {
	if err = wxtypes.ValidateStruct(opts); err != nil {
		return
	}

	req, err := httpclient.BuildRequest(host, w.getEndpointFromRequestType(t), opts)
	if err != nil {
		return
	}

	response, err := w.httpclient.Do(req)
	if err != nil {
		return
	}
	defer response.Body.Close()

	r, err = io.ReadAll(response.Body)
	if err != nil {
		return
	}

	if response.StatusCode/100 != 2 {
		err = fmt.Errorf("received non 2xx HTTP response code: %d, body: %s", response.StatusCode, r)
		return
	}
	return
}

func (w *WeatherAPI) getEndpointFromRequestType(t wxtypes.RequestType) string {
	switch t {
	case wxtypes.AirQuality:
		return "/v1/current.json"
	case wxtypes.ForecastAirQuality:
		return "/v1/forecast.json"
	case wxtypes.Weather:
		return "/v1/history.json"
	default:
		slog.Error("unsupported request type", "provider", w.String(), "request_type", t.String())
		return ""
	}
}
