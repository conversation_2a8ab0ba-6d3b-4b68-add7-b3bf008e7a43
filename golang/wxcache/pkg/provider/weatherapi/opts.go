package weatherapi

// There is more work to be done to support complex parameters, e.g. `q` parameter supporting location names, zip codes, UK post codes, IPs, etc. etc.
// The alerts also support different language responses via the `lang` parameter. They have an enum of countries.
// We currently do not support those usecases, so it's been redacted from here. See more at https://www.weatherapi.com/docs/#intro-request

type WeatherAPIForecastRequestOpts struct {
	APIKey string `url:"key"` // API key
	LatLon string `url:"q"`   // Value should honor format of "Lat,Lon"

	// Number of days of forecast required. Required only with forecast API method.
	//
	// Days parameter value ranges between 1 and 14. e.g: days=5. Required only with forecast API method.
	// If no days parameter is provided then only today's weather is returned.
	Days *int `url:"days"`

	// Restricting forecast or history output to a specific hour in a given day.
	//
	// Must be in 24 hour. For example 5 pm should be hour=17, 6 am as hour=6
	Hour *int `url:"hour"`

	AQI    *string `url:"aqi"`    // Enable/Disable Air Quality data in forecast API output. Supported values are yes/no, default is no.
	Alerts *bool   `url:"alerts"` // Disable alerts in forecast API output
}

type WeatherAPICurrentRequestOpts struct {
	APIKey string `url:"key"` // API key
	LatLon string `url:"q"`   // Value should honor format of "Lat,Lon"

	AQI *string `url:"aqi"` // Enable/Disable Air Quality data. Supported values are yes/no, default is no.
}

type WeatherAPIHistoryRequestOpts struct {
	APIKey string `url:"key"` // API key
	LatLon string `url:"q"`   // Value should honor format of "Lat,Lon"

	// Restrict date output for Forecast and History API method. (Required for History and Future API).
	//
	// For history API 'dt' should be on or after 1st Jan, 2010 in yyyy-MM-dd format (i.e. dt=2010-01-01).
	// For forecast API 'dt' should be between today and next 14 day in yyyy-MM-dd format (i.e. dt=2010-01-01).
	// For future API 'dt' should be between 14 days and 300 days from today in the future in yyyy-MM-dd format (i.e. dt=2023-01-01).
	TimeFrom string `url:"dt"`
}
