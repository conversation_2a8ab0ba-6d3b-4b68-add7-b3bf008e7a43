package weatherapi

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/wxtypes"
)

func TestWeatherAPINormaliseWeatherShouldPass(t *testing.T) {
	input := WeatherAPIEnvironmentResponse{
		Location: WeatherAPILocation{
			Latitude:  location.NewYork.Lat,
			Longitude: location.NewYork.Long,
		},
		Forecast: struct {
			Forecastday []WeatherAPIForecastDay "json:\"forecastday\""
		}{
			[]WeatherAPIForecastDay{
				{
					Hour: []WeatherAPIForecastHour{
						{
							Time:            "2015-05-05 15:04",
							TempC:           15.0,
							WindchillC:      10.0,
							HeatindexC:      1,
							DewpointC:       5.0,
							FeelslikeC:      17.0,
							Humidity:        50,
							PressureMb:      25.0,
							WindKph:         35.0,
							WindDegree:      10.0,
							Cloud:           5,
							VisibilityKm:    0.32,
							UV:              10.0,
							PrecipitationMm: 10.0,
							GustKph:         1.0,
						},
					},
				},
			},
		},
	}

	var (
		feelslike_c   = 17.0
		gust          = 1.0
		humidity      = 50.0
		cloudCover    = 5
		uv            = 10
		pressure      = 25.0
		visibility    = 0.32
		precipitation = 10.0
	)

	timestamp, _ := time.Parse("2006-01-02 15:04", "2015-05-05 15:04")
	expectedOutput := []wxtypes.WeatherV2{
		{
			Timestamp: timestamp,
			Temperature: wxtypes.WeatherTemperature{
				Temperature: convertor.NumberToPtr(15.0),
				FeelsLike:   &feelslike_c,
			},
			Wind: wxtypes.WeatherWind{
				Speed: convertor.NumberToPtr(35.0),
				Gust:  &gust,
			},
			Humidity:      &humidity,
			CloudCover:    &cloudCover,
			UV:            &uv,
			Pressure:      &pressure,
			Visiblity:     &visibility,
			Precipitation: &precipitation,
			Coordinates: wxtypes.Coordinates{
				Latitude:  location.NewYork.Lat,
				Longitude: location.NewYork.Long,
			},
			Metadata: wxtypes.Metadata{
				Provider: "weatherapi",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
				Backfill:  false,
			},
		},
	}

	ctx := context.Background()
	output, err := normaliseWeather(ctx, []WeatherAPIEnvironmentResponse{input})
	require.NoError(t, err)

	for i := 0; i < len(output); i++ {
		expectedOutput[i].SystemProperties.CreatedAt = output[i].SystemProperties.CreatedAt
	}

	require.Equal(t, expectedOutput, output)
}

func TestWeatherAPINormaliseAirQualityShouldPass(t *testing.T) {
	// Input variables which should be the same in the output, too
	var (
		co    = 193.0
		ozone = 84.0
		no2   = 0.89
		so2   = 0.69
		pm25  = 1.60
		pm10  = 3.40

		lastUpdated = "2023-08-01 10:52"
	)

	input := []weatherAPIAQIBucket{
		{
			Timestamp: lastUpdated,
			Location: WeatherAPILocation{
				Latitude:  location.NewYork.Lat,
				Longitude: location.NewYork.Long,
			},
			AirQuality: WeatherAPIAirQuality{
				CO:         co,
				O3:         ozone,
				NO2:        no2,
				SO2:        so2,
				PM25:       pm25,
				PM10:       pm10,
				USEpaIndex: 1,
			},
		},
	}

	var (
		us_aqi = 6
		gb_aqi = 3
		eu_aqi = 2
	)

	timestamp, _ := time.Parse("2006-01-02 15:04", lastUpdated)
	expectedOutput := []wxtypes.AirQualityV2{
		{
			Timestamp: timestamp,
			Pollutants: wxtypes.AirQualityPollutants{
				PM10: &pm10,
				PM25: &pm25,
				CO:   &co,
				SO2:  &so2,
				NO2:  &no2,
				O3:   &ozone,
			},
			AQI: wxtypes.AirQualityIndex{
				US: &us_aqi,
				GB: &gb_aqi,
				EU: &eu_aqi,
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  location.NewYork.Lat,
				Longitude: location.NewYork.Long,
			},
			Metadata: wxtypes.Metadata{
				Provider: "weatherapi",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
				Backfill:  false,
			},
		},
	}

	ctx := context.Background()
	output, err := normaliseAirQuality(ctx, input)
	require.NoError(t, err)
	require.Len(t, output, 1)

	for i := 0; i < len(output); i++ {
		expectedOutput[i].SystemProperties.CreatedAt = output[i].SystemProperties.CreatedAt
	}

	require.Equal(t, expectedOutput, output)
}

func TestWeatherAPINormaliseAirQualityWithZeroesShouldPass(t *testing.T) {
	var (
		co    = 0.0
		ozone = 0.0
		no2   = 0.89
		so2   = 0.69
		pm25  = 1.60
		pm10  = 3.40

		lastUpdated = "2023-08-01 10:52"
	)

	input := []weatherAPIAQIBucket{
		{
			Timestamp: lastUpdated,

			Location: WeatherAPILocation{
				Latitude:  location.NewYork.Lat,
				Longitude: location.NewYork.Long,
			},
			AirQuality: WeatherAPIAirQuality{
				CO:         co,
				O3:         ozone,
				NO2:        no2,
				SO2:        so2,
				PM25:       pm25,
				PM10:       pm10,
				USEpaIndex: 1,
			},
		},
	}

	var (
		us_aqi = 6
		gb_aqi = 1
		eu_aqi = 1
	)

	timestamp, _ := time.Parse("2006-01-02 15:04", lastUpdated)
	expectedOutput := []wxtypes.AirQualityV2{
		{
			Timestamp: timestamp,
			Pollutants: wxtypes.AirQualityPollutants{
				PM10: &pm10,
				PM25: &pm25,
				CO:   nil,
				O3:   nil,
				SO2:  &so2,
				NO2:  &no2,
			},
			AQI: wxtypes.AirQualityIndex{
				US: &us_aqi,
				GB: &gb_aqi,
				EU: &eu_aqi,
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  location.NewYork.Lat,
				Longitude: location.NewYork.Long,
			},
			Metadata: wxtypes.Metadata{
				Provider: "weatherapi",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
				Backfill:  false,
			},
		},
	}

	ctx := context.Background()
	output, err := normaliseAirQuality(ctx, input)
	require.NoError(t, err)
	require.Len(t, output, 1)

	for i := 0; i < len(output); i++ {
		expectedOutput[i].SystemProperties.CreatedAt = output[i].SystemProperties.CreatedAt
	}

	require.Equal(t, expectedOutput, output)
}
