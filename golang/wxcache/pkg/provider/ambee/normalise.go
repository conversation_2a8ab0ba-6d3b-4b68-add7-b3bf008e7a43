package ambee

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"llif.org/wxcache/internal/ckey"
	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/aqi/v2"
	"llif.org/wxcache/pkg/wxtypes"
)

func normaliseAirQuality(ctx context.Context, resp AmbeeAirQualityResponse) []wxtypes.AirQualityV2 {
	result := make([]wxtypes.AirQualityV2, 0, len(resp.Data))
	isBackfill := ctx.Value(ckey.Runtime) == ckey.RuntimeMsgProc

	for i := 0; i < len(resp.Data); i++ {
		entry := resp.Data[i]

		timestamp, err := time.Parse("2006-01-02T15:04:05Z0700", entry.CreatedAt)
		if err != nil {
			slog.WarnContext(ctx, "could not parse CreatedAt", "value", entry.CreatedAt, "err", err)
			continue
		}
		timestamp = timestamp.Truncate(time.Second).UTC()

		resp.Data[i].Concentration.CO = resp.Data[i].Concentration.CO / 1000       // Convert from ppb to ppm
		resp.Data[i].Concentration.OZONE = resp.Data[i].Concentration.OZONE / 1000 // Convert from ppb to ppm

		// @TODO: Invalid calculations for AQI. Requires implementation of ppb/ppm to μg/m³.
		slog.WarnContext(ctx, "Ambee processing air quality with invalid AQI calculations")
		aq, err := aqi.CalculateAQI(aqi.AQIConcentration{
			NO2:  &entry.NO2,
			SO2:  &entry.SO2,
			CO:   &entry.CO,
			O3:   &entry.OZONE,
			PM10: &entry.PM10,
			PM25: &entry.PM25,
		})
		if err != nil {
			slog.ErrorContext(ctx, "could not calculate AQI", "err", err)
		}

		na := wxtypes.AirQualityV2{
			Timestamp: timestamp,
			Pollutants: wxtypes.AirQualityPollutants{
				PM10: &entry.PM10,
				PM25: &entry.PM25,
				CO:   &entry.CO,
				O3:   &entry.OZONE,
				SO2:  &entry.SO2,
				NO2:  &entry.NO2,
			},
			AQI: wxtypes.AirQualityIndex{
				US: aq.USIndex,
				GB: aq.GBIndex,
				EU: aq.EUIndex,
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  entry.Lat,
				Longitude: entry.Lng,
			},
			Metadata: wxtypes.Metadata{
				Provider: "Ambee",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
				Backfill:  isBackfill, // @TODO: propagate somehow later?
			},
		}
		if err := wxtypes.ValidateStruct[wxtypes.AirQualityV2](na); err != nil {
			slog.WarnContext(ctx, "could not validate struct", "struct", na, "provider", "ambee", "err", err)
			continue
		}
		result = append(result, na)
	}
	return result
}

func normaliseWeather(ctx context.Context, resp AmbeeWeatherHistoryResponse) []wxtypes.WeatherV2 {
	result := make([]wxtypes.WeatherV2, 0, len(resp.Data.History))
	isBackfill := ctx.Value(ckey.Runtime) == ckey.RuntimeMsgProc

	for _, data := range resp.Data.History {
		timestamp := time.Unix(data.Timestamp, 0).Truncate(time.Second).UTC()
		nw := wxtypes.WeatherV2{
			Timestamp: timestamp,
			Temperature: wxtypes.WeatherTemperature{
				Temperature: &data.Temperature,
				FeelsLike:   &data.ApparentTemperature,
			},
			Wind: wxtypes.WeatherWind{
				Speed: &data.WindSpeed,
				Gust:  &data.WindGust,
			},
			Humidity:      &data.Humidity,
			CloudCover:    convertor.NumberToPtr(int(data.CloudCover)),
			UV:            &data.UvIndex,
			Pressure:      &data.Pressure,
			Visiblity:     &data.Visibility,
			Precipitation: data.PrecipIntensity,
			Coordinates: wxtypes.Coordinates{
				Latitude:  resp.Data.Lat,
				Longitude: resp.Data.Lng,
			},
			Metadata: wxtypes.Metadata{
				Provider: "Ambee",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
				Backfill:  isBackfill,
			},
		}
		if err := wxtypes.ValidateStruct(nw); err != nil {
			slog.WarnContext(ctx, "could not validate struct", "struct", nw, "provider", "ambee", "err", err)
			continue
		}
		result = append(result, nw)
	}
	return result
}

func normalisePollen(ctx context.Context, resp AmbeePollenResponse) []wxtypes.PollenV2 {
	result := make([]wxtypes.PollenV2, 0, len(resp.Data))
	isBackfill := ctx.Value(ckey.Runtime) == ckey.RuntimeMsgProc

	for _, r := range resp.Data {
		timestamp := time.Unix(r.Timestamp, 0).Truncate(time.Second).UTC()

		var (
			treeSpecies  *AmbeePollenTreeSpecies  = nil
			grassSpecies *AmbeePollenGrassSpecies = nil
			weedSpecies  *AmbeePollenWeedSpecies  = nil
		)
		if r.Species != nil {
			treeSpecies = &r.Species.Tree
			grassSpecies = &r.Species.Grass
			weedSpecies = &r.Species.Weed
		}
		var (
			treePollen  = processSpecies(ctx, treeSpecies, r.Count.TreePollen)
			weedPollen  = processSpecies(ctx, weedSpecies, r.Count.WeedPollen)
			grassPollen = processSpecies(ctx, grassSpecies, r.Count.GrassPollen)
		)

		np := wxtypes.PollenV2{
			Timestamp: timestamp,
			Tree:      treePollen,
			Weed:      weedPollen,
			Grass:     grassPollen,
			Coordinates: wxtypes.Coordinates{
				Latitude:  resp.Lat,
				Longitude: resp.Lng,
			},
			Metadata: wxtypes.Metadata{
				Provider: "Ambee",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
				Backfill:  isBackfill,
			},
		}
		if err := wxtypes.ValidateStruct(np); err != nil {
			slog.WarnContext(ctx, "could not validate struct", "struct", np, "provider", "ambee", "err", err)
			continue
		}
		result = append(result, np)
	}
	return result
}

// processSpecies takes in an interface for all Ambee pollen species to then iterate over the fields
// and return a filled in object `wxtypes.PollenSpecies`.
//
// Assumes that field names of the passed in object are the real names of the species
func processSpecies(ctx context.Context, species AmbeeSpeciesType, count int) *wxtypes.PollenSpecies {
	var result = &wxtypes.PollenSpecies{Count: count}
	if count == 0 {
		return result
	}

	// For locations like Singapore, Ambee does not return subspecies, but does return counts,
	// so it is a valid data point even with no subspecies present.
	if species == nil {
		return result
	}

	// May not the best approach, but better than using reflect
	marshalledStruct, err := json.Marshal(species)
	if err != nil {
		slog.ErrorContext(ctx, "could not marshal Ambee species type", "value", species)
		return result
	}

	kvmap := map[string]any{}
	json.Unmarshal(marshalledStruct, &kvmap)

	for name, value := range kvmap {
		if value == nil {
			continue
		}

		count, ok := value.(float64) // json.Unmarshal turns all valid numbers into float64
		if !ok {
			slog.ErrorContext(ctx, "could not convert species count number to float64", "name", name, "value", value, "type", fmt.Sprintf("%T", value))
			continue
		}

		if count == 0 {
			slog.WarnContext(ctx, "skipping empty pollen species", "name", name)
			continue
		}

		subspecies := wxtypes.PollenSubspecies{
			Name:  strings.ToLower(name),
			Count: int(count),
		}
		result.Subspecies = append(result.Subspecies, subspecies)
	}
	return result
}
