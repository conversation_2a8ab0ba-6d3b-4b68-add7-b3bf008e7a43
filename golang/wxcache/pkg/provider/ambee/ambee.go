package ambee

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"time"

	"golang.org/x/time/rate"
	"llif.org/wxcache/internal/config"
	"llif.org/wxcache/internal/httpclient"
	"llif.org/wxcache/pkg/geo"
	"llif.org/wxcache/pkg/wxtypes"
)

type Ambee struct {
	APIKey     string
	httpclient *httpclient.HttpClient
	radius     string
}

const (
	AmbeeDayHistoryLimit = 2 * 24 * time.Hour

	host = "api.ambeedata.com"
)

func Init(c *config.Config) *Ambee {
	var (
		maxRequestsPerDay = c.RateLimit.Ambee
		rateLimit         = rate.Every(time.Duration(24 * int(time.Hour) / maxRequestsPerDay))

		limiter = rate.NewLimiter(rateLimit, maxRequestsPerDay)
		client  = httpclient.NewHttpClient(limiter)
	)

	return &Ambee{
		APIKey:     c.APIKey.Ambee,
		httpclient: client,
		radius:     c.Radius,
	}
}

func (a *Ambee) String() string {
	return "Ambee"
}

// GetAirQuality resolves given space time and returns the available data as well as a slice of space time it could not resolve
func (a *Ambee) GetAirQuality(ctx context.Context, spacetime []wxtypes.SpaceTime) (result []wxtypes.AirQualityV2, errBuckets []wxtypes.SpaceTime) {
	for _, st := range spacetime {
		validST, invalidST := a.splitSpaceTimeForAmbeeLimit(st)
		if invalidST != nil {
			errBuckets = append(errBuckets, *invalidST)
		}

		if validST != nil {
			data, err := a.getAirQuality(ctx, *validST)
			if err != nil {
				slog.ErrorContext(ctx, "error fetching air quality", "st", *validST, "err", err, "provider", a.String())
				errBuckets = append(errBuckets, *validST)
				continue
			}
			if len(data) == 0 {
				errBuckets = append(errBuckets, *validST)
				continue
			}
			result = append(result, data...)
		}
	}
	return
}

// GetAirQualityForecast resolves given space time and returns the available forecasted data
func (w *Ambee) GetAirQualityForecast(ctx context.Context, st wxtypes.SpaceTime) (r []wxtypes.AirQualityV2, err error) {
	return nil, errors.New("not implemented")
}

// GetWeather resolves given space time and returns the available data as well as a slice of space time it could not resolve
func (a *Ambee) GetWeather(ctx context.Context, spacetime []wxtypes.SpaceTime) (result []wxtypes.WeatherV2, errBuckets []wxtypes.SpaceTime) {
	for _, st := range spacetime {
		validST, invalidST := a.splitSpaceTimeForAmbeeLimit(st)
		if invalidST != nil {
			errBuckets = append(errBuckets, *invalidST)
		}

		if validST != nil {
			data, err := a.getWeather(ctx, *validST)
			if err != nil {
				slog.ErrorContext(ctx, "error fetching weather", "st", *validST, "err", err, "provider", a.String())
				errBuckets = append(errBuckets, *validST)
				continue
			}
			if len(data) == 0 {
				errBuckets = append(errBuckets, *validST)
				continue
			}
			result = append(result, data...)
		}
	}
	return
}

// GetWeatherForecast resolves given space time and returns the available forecasted data
func (w *Ambee) GetWeatherForecast(ctx context.Context, st wxtypes.SpaceTime) (r []wxtypes.WeatherV2, err error) {
	return nil, errors.New("not implemented")
}

// GetPollen resolves given space time and returns the available data as well as a slice of space time it could not resolve
func (a *Ambee) GetPollen(ctx context.Context, st []wxtypes.SpaceTime) (result []wxtypes.PollenV2, errBuckets []wxtypes.SpaceTime) {
	for _, st := range st {
		validST, invalidST := a.splitSpaceTimeForAmbeeLimit(st)

		// Add invalid portion to error buckets if it exists
		if invalidST != nil {
			errBuckets = append(errBuckets, *invalidST)
		}

		// Process valid portion if it exists
		if validST != nil {
			data, err := a.getPollen(ctx, *validST)
			if err != nil {
				slog.ErrorContext(ctx, "error fetching pollen", "st", *validST, "err", err, "provider", a.String())
				errBuckets = append(errBuckets, *validST)
				continue
			}
			if len(data) == 0 {
				errBuckets = append(errBuckets, *validST)
				continue
			}
			result = append(result, data...)
		}
	}
	return
}

func (a *Ambee) GetPollenForecast(ctx context.Context, st wxtypes.SpaceTime) ([]wxtypes.PollenV2, error) {
	var latestDate = time.Now().UTC().AddDate(0, 0, 2)
	if st.TimeTo.After(latestDate) {
		st.TimeTo = latestDate
	}

	res, err := a.getPollenForecast(ctx, st)
	if err != nil {
		return nil, err
	}
	return normalisePollen(ctx, *res), nil
}

func (a *Ambee) getAirQuality(ctx context.Context, st wxtypes.SpaceTime) (r []wxtypes.AirQualityV2, err error) {
	opts := AmbeeAirQualityHistoryRequestOpts{
		Lat:  st.Lat,
		Long: st.Long,
		From: st.TimeFrom.Format("2006-01-02 15:04:05"),
		To:   st.TimeTo.Format("2006-01-02 15:04:05"),
	}

	body, err := a.getFromAmbee(wxtypes.AirQuality, opts)
	if err != nil {
		return
	}

	var res AmbeeAirQualityResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return
	}
	if err = wxtypes.ValidateStruct(res); err != nil {
		return nil, err
	}
	return
}

func (a *Ambee) getWeather(ctx context.Context, st wxtypes.SpaceTime) (r []wxtypes.WeatherV2, err error) {
	opts := AmbeeWeatherHistoryRequestOpts{
		Lat:  st.Lat,
		Long: st.Long,
		From: st.TimeFrom.Format("2006-01-02 15:04:05"),
		To:   st.TimeTo.Format("2006-01-02 15:04:05"),
	}

	body, err := a.getFromAmbee(wxtypes.Weather, opts)
	if err != nil {
		return
	}

	res := AmbeeWeatherHistoryResponse{}
	if err = json.Unmarshal(body, &res); err != nil {
		return
	}
	if err = wxtypes.ValidateStruct(res); err != nil {
		return nil, err
	}
	// If data returned are from sensor out of radius, skip it
	isInRadius := geo.IsWithinRadius(st.Lat, st.Long, res.Data.Lat, res.Data.Lng, a.radius)
	if !isInRadius {
		slog.InfoContext(ctx, "provider out of radius", "provider", a.String(), "lat", st.Lat, "long", st.Long, "radius", a.radius)
		return nil, nil
	}
	return normaliseWeather(ctx, res), nil
}

func (a *Ambee) getPollen(ctx context.Context, st wxtypes.SpaceTime) (r []wxtypes.PollenV2, err error) {
	opts := AmbeePollenHistoryRequestOpts{
		Lat:  st.Lat,
		Long: st.Long,
		From: st.TimeFrom.Format("2006-01-02 15:04:05"),
		To:   st.TimeTo.Format("2006-01-02 15:04:05"),
	}

	body, err := a.getFromAmbee(wxtypes.Pollen, opts)
	if err != nil {
		return nil, err
	}

	res := AmbeePollenResponse{}
	if err = json.Unmarshal(body, &res); err != nil {
		return nil, err
	}
	if err = wxtypes.ValidateStruct(res); err != nil {
		return nil, err
	}
	// If data returned are from sensor out of radius, skip it
	isInRadius := geo.IsWithinRadius(st.Lat, st.Long, res.Lat, res.Lng, a.radius)
	if !isInRadius {
		slog.InfoContext(ctx, "provider out of radius", "provider", a.String(), "lat", st.Lat, "long", st.Long, "radius", a.radius)
		return nil, nil
	}
	return normalisePollen(ctx, res), nil
}

func (a *Ambee) getPollenForecast(ctx context.Context, st wxtypes.SpaceTime) (res *AmbeePollenResponse, err error) {
	opts := AmbeePollenForecastRequestOpts{
		Lat:  st.Lat,
		Long: st.Long,
	}

	body, err := a.getFromAmbee(wxtypes.ForecastPollen, opts)
	if err != nil {
		return nil, err
	}

	if err = json.Unmarshal(body, &res); err != nil {
		return nil, err
	}
	if err = wxtypes.ValidateStruct(res); err != nil {
		return nil, err
	}
	// If data returned are from sensor out of radius, skip it
	isInRadius := geo.IsWithinRadius(st.Lat, st.Long, res.Lat, res.Lng, a.radius)
	if !isInRadius {
		slog.InfoContext(ctx, "provider out of radius", "provider", a.String(), "lat", st.Lat, "long", st.Long, "radius", a.radius)
		return nil, nil
	}
	return res, nil
}

func (a *Ambee) getFromAmbee(t wxtypes.RequestType, opts any) (r []byte, err error) {
	if err = wxtypes.ValidateStruct(opts); err != nil {
		return
	}

	req, err := httpclient.BuildRequest(host, a.getEndpointFromRequestType(t), opts)
	if err != nil {
		return
	}

	req.Header.Add("x-api-key", a.APIKey)

	slog.Info("ambee request", "request", req.URL.String())

	response, err := a.httpclient.Do(req)
	if err != nil {
		return
	}
	defer response.Body.Close()

	r, err = io.ReadAll(response.Body)
	if err != nil {
		return
	}

	if response.StatusCode/100 != 2 && response.StatusCode != 404 {
		var errorResponse AmbeeErrorResponse
		if err = json.Unmarshal(r, &errorResponse); err != nil {
			return
		}

		err = fmt.Errorf("Ambee return non 2xx HTTP response with message=%s", errorResponse.Message)
		return
	}

	body := make(map[string]interface{})
	if err = json.Unmarshal(r, &body); err != nil {
		return
	}

	_, ok := body["message"].(string)
	if !ok {
		err = fmt.Errorf("Ambee request body has no message field, got body=%s", string(r))
		return
	}

	return
}

func (a *Ambee) getEndpointFromRequestType(t wxtypes.RequestType) string {
	switch t {
	case wxtypes.AirQuality:
		return "/history/by-lat-lng"
	case wxtypes.Weather:
		return "/weather/history/by-lat-lng"
	case wxtypes.Pollen:
		return "/history/pollen/by-lat-lng"
	case wxtypes.ForecastPollen:
		return "/forecast/pollen/by-lat-lng"
	default:
		slog.Error("unsupported request type", "provider", a.String(), "request_type", t.String())
		return ""
	}
}

func (a *Ambee) validateAmbeeTimeInput(timeFrom, timeTo time.Time) (*wxtypes.TimeBucket, error) {
	var (
		timeFromUtc       = timeFrom.UTC().Truncate(time.Minute)
		timeToUtc         = timeTo.UTC().Truncate(time.Minute)
		timeNowUtc        = time.Now().UTC().Truncate(time.Minute)
		timeTwoDaysAgoUtc = timeNowUtc.Add(-AmbeeDayHistoryLimit)
		err               error
	)

	// Checks whether time_from is before time_to
	if timeFromUtc.After(timeToUtc) {
		err = fmt.Errorf("TimeFrom greater than TimeTo")
		return nil, err
	}

	// Check whether both time_from and time_to are in the ambee past limit
	if !(timeFromUtc.After(timeTwoDaysAgoUtc) && timeToUtc.After(timeTwoDaysAgoUtc)) {
		return nil, nil
	}

	// Replace timeTo with current time if it spans to the future
	if timeToUtc.After(timeNowUtc) {
		timeToUtc = timeNowUtc
	}
	return &wxtypes.TimeBucket{TimeFrom: timeFromUtc, TimeTo: timeToUtc}, nil
}

// splitSpaceTimeForAmbeeLimit splits a spacetime bucket into valid (last 2 days) and invalid portions
func (a *Ambee) splitSpaceTimeForAmbeeLimit(st wxtypes.SpaceTime) (validST *wxtypes.SpaceTime, invalidST *wxtypes.SpaceTime) {
	var (
		timeNowUtc        = time.Now().UTC().Truncate(time.Minute)
		timeTwoDaysAgoUtc = timeNowUtc.Add(-AmbeeDayHistoryLimit)
		timeFromUtc       = st.TimeFrom.UTC().Truncate(time.Minute)
		timeToUtc         = st.TimeTo.UTC().Truncate(time.Minute)
	)

	// If the entire range is before the 2-day limit, return as invalid
	if timeToUtc.Before(timeTwoDaysAgoUtc) || timeToUtc.Equal(timeTwoDaysAgoUtc) {
		return nil, &wxtypes.SpaceTime{
			Lat:      st.Lat,
			Long:     st.Long,
			TimeFrom: st.TimeFrom,
			TimeTo:   st.TimeTo,
		}
	}

	// If the entire range is within the 2-day limit, return as valid
	if timeFromUtc.After(timeTwoDaysAgoUtc) || timeFromUtc.Equal(timeTwoDaysAgoUtc) {
		// Replace timeTo with current time if it spans to the future
		if timeToUtc.After(timeNowUtc) {
			timeToUtc = timeNowUtc
		}
		return &wxtypes.SpaceTime{
			Lat:      st.Lat,
			Long:     st.Long,
			TimeFrom: timeFromUtc,
			TimeTo:   timeToUtc,
		}, nil
	}

	// The range spans both valid and invalid portions - split it
	validST = &wxtypes.SpaceTime{
		Lat:      st.Lat,
		Long:     st.Long,
		TimeFrom: timeTwoDaysAgoUtc,
		TimeTo:   timeToUtc,
	}

	// Replace timeTo with current time if it spans to the future
	if validST.TimeTo.After(timeNowUtc) {
		validST.TimeTo = timeNowUtc
	}

	invalidST = &wxtypes.SpaceTime{
		Lat:      st.Lat,
		Long:     st.Long,
		TimeFrom: timeFromUtc,
		TimeTo:   timeTwoDaysAgoUtc,
	}

	return validST, invalidST
}
