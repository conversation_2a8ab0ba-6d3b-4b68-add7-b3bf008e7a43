package ambee

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/testutil/location"
)

func TestAmbeeNormalisePollen(t *testing.T) {
	now := time.Now().UTC()
	response := AmbeePollenResponse{
		Message: "success",
		Lat:     location.London.Lat,
		Lng:     location.London.Long,
		Data: []AmbeePollenData{
			{
				Risk: struct {
					GrassPollen string "json:\"grass_pollen\""
					TreePollen  string "json:\"tree_pollen\""
					WeedPollen  string "json:\"weed_pollen\""
				}{
					GrassPollen: "Low",
					TreePollen:  "High",
					WeedPollen:  "Moderate",
				},
				Count: struct {
					GrassPollen int "json:\"grass_pollen\""
					TreePollen  int "json:\"tree_pollen\""
					WeedPollen  int "json:\"weed_pollen\""
				}{
					GrassPollen: 10,
					TreePollen:  50,
					WeedPollen:  15,
				},
				Timestamp: now.Unix(),
				Species: &AmbeePollenSpecies{
					Tree: AmbeePollenTreeSpecies{
						Hazel: convertor.NumberToPtr(15),
					},
					Weed: AmbeePollenWeedSpecies{
						Mugwort: convertor.NumberToPtr(10),
					},
					Grass: AmbeePollenGrassSpecies{
						Grass: convertor.NumberToPtr(50),
					},
				},
			},
			{
				Risk: struct {
					GrassPollen string "json:\"grass_pollen\""
					TreePollen  string "json:\"tree_pollen\""
					WeedPollen  string "json:\"weed_pollen\""
				}{
					GrassPollen: "Low",
					TreePollen:  "Low",
					WeedPollen:  "Low",
				},
				Count: struct {
					GrassPollen int "json:\"grass_pollen\""
					TreePollen  int "json:\"tree_pollen\""
					WeedPollen  int "json:\"weed_pollen\""
				}{
					GrassPollen: 0,
					TreePollen:  0,
					WeedPollen:  0,
				},
				Timestamp: now.Unix(),
				Species: &AmbeePollenSpecies{
					Tree: AmbeePollenTreeSpecies{
						Hazel: convertor.NumberToPtr(0),
					},
					Weed: AmbeePollenWeedSpecies{
						Mugwort: convertor.NumberToPtr(0),
					},
					Grass: AmbeePollenGrassSpecies{
						Grass: convertor.NumberToPtr(0),
					},
				},
			},
		},
	}
	pollen := normalisePollen(context.Background(), response)
	require.NotEmpty(t, pollen)
	require.Len(t, pollen, len(response.Data))

	for idx, p := range pollen {
		require.Equal(t, response.Data[idx].Count.GrassPollen, p.Grass.Count)
		require.Equal(t, response.Data[idx].Count.TreePollen, p.Tree.Count)
		require.Equal(t, response.Data[idx].Count.WeedPollen, p.Weed.Count)

		require.Equal(t, response.Lat, p.Coordinates.Latitude)
		require.Equal(t, response.Lng, p.Coordinates.Longitude)
	}
}
