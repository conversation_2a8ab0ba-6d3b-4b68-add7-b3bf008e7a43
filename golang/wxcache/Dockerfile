FROM golang:1.25.0-alpine AS wxcache_builder
RUN --mount=type=cache,sharing=locked,target=/var/cache/apk apk update && apk upgrade && apk add --update alpine-sdk
RUN apk add --no-cache git ca-certificates && update-ca-certificates
RUN go install github.com/jstemmer/go-junit-report/v2@latest
RUN go install github.com/boumenot/gocover-cobertura@latest
# See https://stackoverflow.com/a/55757473/12429735RUN 
RUN adduser \
    --disabled-password \
    --no-create-home \
    --shell "/sbin/nologin" \
    --uid 1000 appuser
RUN mkdir /wxcache
WORKDIR /wxcache

COPY go.mod ./
COPY go.sum ./
RUN go mod download

FROM wxcache_builder AS wxcache_build_cache
COPY ./ ./
RUN make build-linux
RUN chmod +x bin/wxcache

FROM scratch AS wxcache
COPY --from=wxcache_builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=wxcache_builder /etc/passwd /etc/passwd

ENV BUILD_VERSION $BUILD_VERSION
ENV IS_CONTAINERIZED true
ENV APP_VERSION $APP_VERSION

USER appuser

COPY --from=wxcache_build_cache /wxcache/bin/* ./

EXPOSE 8006
CMD [ "./wxcache" ]
