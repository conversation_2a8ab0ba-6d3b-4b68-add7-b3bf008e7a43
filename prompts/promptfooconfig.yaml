description: LLM Prompts
prompts: [input/forecast_summary_prompt.txt]
providers: [openai:gpt-4.1-mini]
tests:
  - assert:
      - type: contains-json
      - type: llm-rubric
        value: ensure that the output is relevant
      - type: llm-rubric
        value: does not claim to know the name of the location
      - type: llm-rubric
        value: does not suggest residents how to behave
      - type: not-contains
        value: AI language model
