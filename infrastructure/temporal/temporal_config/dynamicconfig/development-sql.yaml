# This file configures Temporal's runtime behavior.
# It tells Temporal which data stores to use for persistence and visibility.

# Enables the worker service on the frontend.
# Required for Workflow Execution.
frontend.enableWorkerService:
  - value: true
    constraints: {}

# Specifies the default data store for core persistence (history, queues, tasks, etc.).
system.persistence.dataStore:
  - value: default
    constraints: {}

# IMPORTANT: Specifies the data store for visibility (listing and searching workflows).
# By setting this to 'default', Temporal will use the same SQL database
# configured for persistence (PostgreSQL in your case) for visibility.
system.visibility.dataStore:
  - value: default
    constraints: {}
# Optional: If you want to use the advanced visibility features provided by SQL databases
# (available from Temporal Server v1.20+), this is implicitly enabled by setting
# system.visibility.dataStore to 'default' for SQL.
# You can add more dynamic configuration settings here as needed,
# such as metrics, logging levels, or rate limits.
