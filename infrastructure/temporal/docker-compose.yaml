services:
  temporal-postgresql:
    container_name: temporal-postgresql
    image: postgres:17.4
    restart: unless-stopped
    environment:
      POSTGRES_DB: temporal
      POSTGRES_USER: temporal
      POSTGRES_PASSWORD: temporal
    volumes: [temporal_pgdata:/var/lib/postgresql/data]
    networks: [stack]
    ports: ['5432']

  temporal:
    container_name: temporal
    depends_on: [temporal-postgresql]
    environment:
      - DB=postgres12
      - DB_PORT=5432
      - POSTGRES_USER=temporal
      - POSTGRES_PWD=temporal
      - POSTGRES_SEEDS=temporal-postgresql
      - DYNAMIC_CONFIG_FILE_PATH=/etc/temporal/config/dynamicconfig/development-sql.yaml
      - AUTO_SETUP_SCHEMA=true
      - AUTO_SETUP_ADMIN_NAMESPACE=true
    image: temporalio/auto-setup:1.24.0
    networks: [stack]
    ports: [7233:7233]
    volumes: [./temporal_config/dynamicconfig:/etc/temporal/config/dynamicconfig, temporal_data:/var/lib/temporal]

  temporal-admin-tools:
    container_name: temporal-admin-tools
    depends_on: [temporal]
    environment: [TEMPORAL_ADDRESS=temporal:7233, TEMPORAL_CLI_ADDRESS=temporal:7233]
    image: temporalio/admin-tools:latest
    networks: [stack]
    stdin_open: true
    tty: true

  temporal-ui:
    container_name: temporal-ui
    depends_on: [temporal]
    environment: [TEMPORAL_ADDRESS=temporal:7233, 'TEMPORAL_CORS_ORIGINS=http://localhost:3000,http://localhost:8089']
    image: temporalio/ui:2.25.0
    networks: [stack]
    ports:
      - 8089:8080

# Volumes definition
volumes:
  temporal_dynamic_config:
    driver: local
  temporal_data:
  temporal_pgdata:

networks:
  stack:
    external: true
