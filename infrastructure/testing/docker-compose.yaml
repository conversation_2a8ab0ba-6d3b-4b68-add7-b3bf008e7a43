services:
  file_service:
    container_name: $BUILD_VERSION-file_service
    restart: on-failure
    build:
      target: local
      context: .
      dockerfile: ./services/file_service/Dockerfile
    command: uvicorn services.file_service.main:app --host 0.0.0.0 --port 8001
    environment: [PYTHONPATH=/app_code/]
    ports: ['8001']
    networks: [stack]
    env_file: ['./settings/.env.${RUN_ENV:-local}']

  data_service:
    container_name: $BUILD_VERSION-data_service
    restart: on-failure
    build:
      target: local
      context: .
      dockerfile: ./services/data_service/Dockerfile
    command: uvicorn services.data_service.main:app --host 0.0.0.0 --port 8003
    environment: [PYTHONPATH=/app_code/]
    ports: ['8003']
    networks: [stack]
    env_file: ['./settings/.env.${RUN_ENV:-local}']

  user_service:
    container_name: $BUILD_VERSION-user_service
    restart: on-failure
    build:
      target: local
      context: .
      dockerfile: ./services/user_service/Dockerfile
    command: >
      sh -c
          "cd /app_code/services/base/infrastructure/database/sql_alchemy/migrations &&
          alembic -x load_data=yes upgrade head &&
          cd /app_code/ &&
          python3 -m services.base.infrastructure.database.sql_alchemy.run_seeders &&
          uvicorn services.user_service.main:app --host 0.0.0.0 --port 8004"
    environment: [PYTHONPATH=/app_code/]
    ports: ['8004']
    networks: [stack]
    env_file: ['./settings/.env.${RUN_ENV:-local}']

  mobile_service:
    container_name: $BUILD_VERSION-mobile_service
    restart: on-failure
    build:
      target: local
      context: .
      dockerfile: ./services/mobile_service/Dockerfile
    command: uvicorn services.mobile_service.main:app --host 0.0.0.0 --port 8005
    environment: [PYTHONPATH=/app_code/]
    ports: ['8005']
    networks: [stack]
    env_file: ['./settings/.env.${RUN_ENV:-local}']

  wxcache:
    container_name: $BUILD_VERSION-wxcache
    restart: on-failure
    build:
      context: ./golang/wxcache
    environment:
      MOCK_HTTP: 'false'
      CACHE_SIZE: 10000
      INCLUDE_CACHE: 'false'
      APP_VERSION:
      BUILD_VERSION:
    ports: ['8006']
    networks: [stack]
    env_file: ['./settings/.env.${RUN_ENV:-local}']

  serverless_test_handler:
    container_name: $BUILD_VERSION-serverless_test_handler
    restart: on-failure
    build:
      context: .
      dockerfile: ./services/serverless/apps/test_handler/Dockerfile
    networks: [stack]
    depends_on: [temporal]
    env_file: ['./settings/.env.${RUN_ENV:-local}']

  db01:
    container_name: $BUILD_VERSION-db01
    image: postgres:17.6
    restart: on-failure
    ports: ['5432']
    networks: [stack]
    env_file: ['./settings/.env.${RUN_ENV:-local}']

  azurite:
    container_name: $BUILD_VERSION-azurite
    command: azurite --blobHost 0.0.0.0 --blobPort 10000
    image: mcr.microsoft.com/azure-storage/azurite:3.35.0
    restart: on-failure
    ports: ['10000']
    env_file: ['./settings/.env.${RUN_ENV:-local}']
    networks: [stack]

  localstack:
    container_name: $BUILD_VERSION-localstack
    image: localstack/localstack:4.7
    restart: on-failure
    networks: [stack]
    ports: [4566-4580]
    environment: ['SERVICES=sqs,sns,lambda,sts,ecr,cloudformation,s3,iam,events,logs,apigateway,ssm', DEBUG=1, LAMBDA_IGNORE_ARCHITECTURE=1]
    healthcheck:
      test: awslocal sns list-topics && awslocal sqs list-queues && awslocal lambda list-functions
      interval: 5s
      timeout: 10s
      start_period: 10s
      retries: 5
    env_file: ['./settings/.env.${RUN_ENV:-local}']

  os01:
    container_name: $BUILD_VERSION-os01
    image: opensearchproject/opensearch:2.19.2
    restart: on-failure
    environment: [DISABLE_SECURITY_PLUGIN=true, bootstrap.memory_lock=true, discovery.type=single-node, ES_JAVA_OPTS=-Xms512m -Xmx1g]
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    cap_add: [IPC_LOCK]
    ports: ['9200']
    networks: [stack]

  temporal-postgresql:
    container_name: $BUILD_VERSION-temporal-postgresql
    image: postgres:17.4
    restart: on-failure
    environment:
      POSTGRES_DB: temporal
      POSTGRES_USER: temporal
      POSTGRES_PASSWORD: temporal
    networks: [stack]
    ports: ['5432']

  temporal:
    container_name: $BUILD_VERSION-temporal
    restart: on-failure
    depends_on: [temporal-postgresql]
    environment:
      - DB=postgres12
      - DB_PORT=5432
      - POSTGRES_USER=temporal
      - POSTGRES_PWD=temporal
      - POSTGRES_SEEDS=temporal-postgresql
      - DYNAMIC_CONFIG_FILE_PATH=/etc/temporal/config/dynamicconfig/development-sql.yaml
      - AUTO_SETUP_SCHEMA=true
      - AUTO_SETUP_ADMIN_NAMESPACE=true
    image: temporalio/auto-setup:1.24.0
    networks: [stack]
    ports: ['7233']
    volumes: [./infrastructure/temporal/temporal_config/dynamicconfig:/etc/temporal/config/dynamicconfig]

networks:
  stack:
    name: $BUILD_VERSION-stack
    driver: bridge
