[project]
name = "My-LLIF"
version = "1.0"
requires-python = ">= 3.13"

[tool.coverage.run]
omit = [
    "infrastructure/*",
    "seed_data/*",
    "scripts/*",
    "golang/*",
    ".venv/*",
    "venv/*",
]
[tool.coverage.report]
skip_covered = true

[tool.setuptools]
packages = ["services", "seed_data", "settings"]

[tool.black]
line-length = 120
target-version = ['py313']
exclude = '/(\.git|.github|.idea|.mypy_cache|.pytest_cache|.ruff_cache|.vscode|.venv|venv)/'

[tool.ruff]
lint.select = ["E", "F", "I"]
lint.ignore = ["E501"]
line-length = 160
exclude = [
    ".eggs",
    ".git",
    ".github",
    ".idea",
    ".mypy_cache",
    ".pytest_cache",
    ".ruff_cache",
    ".vscode",
    "golang",
    "seed_data",
    ".venv",
    "venv",
]
target-version = "py313"

[tool.pyright]
exclude = [
    ".eggs",
    ".git",
    ".github",
    ".idea",
    ".mypy_cache",
    ".pytest_cache",
    ".ruff_cache",
    ".vscode",
    "golang",
    "seed_data",
    ".venv",
    "venv",
]

[tool.pytest.ini_options]
asyncio_mode = "auto"
filterwarnings = [
    "ignore::pytest.PytestCacheWarning",
    "once::DeprecationWarning",
]
markers = ["integration: mark a test as a third party integration test."]
addopts = "--cov-report=term-missing --durations-min=1.0 --durations=10"

[tool.logfire]
ignore_no_config = true

[tool.yamlfix]
# Sadly does not currently work, see https://github.com/lyz-code/yamlfix/issues/252
# quote_representation = "\""
explicit_start = false
whitelines = 1
line_length = 160