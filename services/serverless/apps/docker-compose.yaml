services:
  alexa_voice_log:
    image: alexa_voice_log
    build:
      context: ./../../../
      dockerfile: ./services/serverless/apps/alexa_voice_log/Dockerfile
      args:
        RUN_ENV: ${RUN_ENV-local}

  analytics_scheduler:
    image: analytics_scheduler
    build:
      context: ./../../../
      dockerfile: ./services/serverless/apps/analytics_scheduler/Dockerfile
      args:
        RUN_ENV: ${RUN_ENV-local}

  data_consistency_validator:
    image: data_consistency_validator
    build:
      context: ./../../../
      dockerfile: ./services/serverless/apps/data_consistency_validator/Dockerfile
      args:
        RUN_ENV: ${RUN_ENV-local}

  notify_handler:
    image: notify_handler
    build:
      context: ./../../../
      dockerfile: ./services/serverless/apps/notify_handler/Dockerfile
      args:
        RUN_ENV: ${RUN_ENV-local}

  single_correlation_app:
    image: single_correlation_app
    build:
      context: ./../../../
      dockerfile: ./services/serverless/apps/single_correlation_app/Dockerfile
      args:
        RUN_ENV: ${RUN_ENV-local}

  trend_insights:
    image: trend_insights
    build:
      context: ./../../../
      dockerfile: ./services/serverless/apps/trend_insights/Dockerfile
      args:
        RUN_ENV: ${RUN_ENV-local}

  usage_statistics_generator:
    image: usage_statistics_generator
    build:
      context: ./../../../
      dockerfile: ./services/serverless/apps/usage_statistics_generator/Dockerfile
      args:
        RUN_ENV: ${RUN_ENV-local}

  test_handler:
    image: test_handler
    build:
      context: ./../../../
      dockerfile: ./services/serverless/apps/test_handler/Dockerfile
      args:
        RUN_ENV: ${RUN_ENV-local}
    profiles: [test, local]
    networks: [stack]

networks:
  stack:
    name: stack
    external: true
