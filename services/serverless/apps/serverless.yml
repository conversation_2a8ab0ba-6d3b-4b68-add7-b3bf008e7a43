# Welcome to Serverless!
#
# For full config options, check the docs:
#    docs.serverless.com
#

service: llif-apps

# You can pin your service to only deploy with a specific Serverless version
frameworkVersion: '3'

plugins: [serverless-localstack, serverless-offline]

custom:
  stage: ${opt:stage, self:provider.stage}
  localstack:
    debug: true
    # list of stages for which the plugin should be enabled
    stages: [local]
    host: http://localhost  # optional - LocalStack host to connect to
    autostart: false
    edgePort: 4566  # optional - LocalStack edge port to connect to
    lambda:
      # Enable this flag to improve performance
      mountCode: false
    docker:
      # Enable this flag to run "docker ..." commands as sudo
      sudo: false
  dotenvVars: ${file(parse_env.js)}

provider:
  name: aws
  runtime: python3.10
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - secretsmanager:GetResourcePolicy
            - secretsmanager:GetSecretValue
            - secretsmanager:DescribeSecret
            - secretsmanager:ListSecretVersionIds
            - secretsmanager:ListSecrets
            - secretsmanager:BatchGetSecretValue
          Resource:
            - '*'  # TODO: this should be limited to just the required secrets
        - Effect: Allow
          Action: [sns:Publish, sns:Subscribe, sns:Unsubscribe, sns:ListTopics, sns:CreateTopic, sns:TagResource]
          Resource: ['*']
        - Effect: Allow
          Action: [sqs:ReceiveMessage, sqs:GetQueueAttributes, sqs:DeleteMessage]
          Resource: ['*']
  region: us-east-1
  environment: ${file(parse_env.js)}
  vpc:
    securityGroupIds: []
    subnetIds: []
  tags:
    env: ${self:custom.dotenvVars.RUN_ENV}

  ecr:
    images:
      alexa_voice_log:
        path: ./../../../
        file: ./services/serverless/apps/alexa_voice_log/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      analytics_app_scheduler:
        path: ./../../../
        file: ./services/serverless/apps/analytics_scheduler/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      data_consistency_validator:
        path: ./../../../
        file: ./services/serverless/apps/data_consistency_validator/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      notify_handler:
        path: ./../../../
        file: ./services/serverless/apps/notify_handler/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      single_correlation_app:
        path: ./../../../
        file: ./services/serverless/apps/single_correlation_app/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      trend_insights:
        path: ./../../../
        file: ./services/serverless/apps/trend_insights/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      usage_statistics_generator:
        path: ./../../../
        file: ./services/serverless/apps/usage_statistics_generator/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}

functions:
  AlexaBestLifeEventLog:
    timeout: 300
    handler: api.handler
    image:
      name: alexa_voice_log
    events:
      - alexaSkill: ${self:custom.dotenvVars.ALEXA_VOICE_LOG_ID}
      - http:
          path: /alexa
          method: post

  AnalyticsAppScheduler:
    timeout: 300
    handler: api.handler
    image:
      name: notify_handler
    events:
      - http:
          path: /analytics_scheduler
          method: post

  DataConsistencyValidator:
    timeout: 900
    handler: api.handler
    image:
      name: data_consistency_validator
    events:
      - http:
          path: /data_consistency_validator
          method: post

  NotifyHandler:
    timeout: 900
    handler: api.handler
    reservedConcurrency: 5
    image:
      name: notify_handler
    events:
      - http:
          path: /notify
          method: post
      - sqs: arn:aws:sqs:us-east-1:${self:custom.dotenvVars.AWS_ACCOUNT}:${self:custom.dotenvVars.ENVIRONMENT_PREFIX}${self:custom.dotenvVars.QUEUE_NOTIFY_HANDLER_JOB}

  SingleCorrelationApp:
    timeout: 900
    reservedConcurrency: 5
    handler: api.handler
    image:
      name: single_correlation_app
    events:
      - http:
          path: /single_correlation
          method: post
      - sqs: arn:aws:sqs:us-east-1:${self:custom.dotenvVars.AWS_ACCOUNT}:${self:custom.dotenvVars.ENVIRONMENT_PREFIX}${self:custom.dotenvVars.QUEUE_SINGLE_CORRELATION_JOB}

  TrendInsights:
    timeout: 900
    reservedConcurrency: 5
    handler: api.handler
    image:
      name: trend_insights
    events:
      - http:
          path: /trend_insights
          method: post
      - sqs: arn:aws:sqs:us-east-1:${self:custom.dotenvVars.AWS_ACCOUNT}:${self:custom.dotenvVars.ENVIRONMENT_PREFIX}${self:custom.dotenvVars.QUEUE_TREND_INSIGHTS_JOB}

  UsageStatisticsGenerator:
    timeout: 300
    handler: api.handler
    module: usage_statistics_generator
    events:
      - http:
          path: /usage_statistics
          method: post
