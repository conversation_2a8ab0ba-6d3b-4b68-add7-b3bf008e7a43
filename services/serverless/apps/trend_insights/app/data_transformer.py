from typing import List, Sequence

import pandas
from dateutil.relativedelta import relativedelta
from pandas import DataFrame

from services.base.application.boundaries.time_input import TimeInput
from services.base.application.utils.hashmap import HashMapUtils
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.shared import TimeIntervalModel
from services.serverless.apps.trend_insights.app.analytic_mappings import AggregationFunctionToMethod
from services.serverless.apps.trend_insights.app.models.analytic_series_models import (
    SeriesAnalysisAveragedOutputBucket,
    SeriesAnalysisListValuesBucket,
)


class DataTransformer:
    @staticmethod
    def from_v3_documents_to_sorted_data_frame(
        documents: Sequence[Document], series_value_field: str, series: str
    ) -> DataFrame:
        data = []
        for document in documents:
            timestamp = getattr(document, DocumentLabels.TIMESTAMP, None)
            if timestamp is None:
                raise AttributeError(f"{DocumentLabels.TIMESTAMP} attribute not found in {type(document).__name__}")

            if DataTransformer._is_nested_field(series_value_field):
                value = HashMapUtils.get_nested_field_value(document, series_value_field)
            else:
                value = getattr(document, series_value_field)

            data.append(
                {
                    DocumentLabels.TIMESTAMP: timestamp,
                    series: value,
                }
            )

        df = pandas.DataFrame(data)
        df[DocumentLabels.TIMESTAMP] = pandas.to_datetime(df[DocumentLabels.TIMESTAMP], utc=True)
        df = df.dropna()
        return df.sort_values(by=DocumentLabels.TIMESTAMP, ascending=False)

    @staticmethod
    def to_sorted_data_frame(input_data: List[dict], analytic_series: str) -> DataFrame:
        """Converts raw input data to sorted pandas data frame"""
        # Currently only accepts non nested fields, could be expanded into a different method to create dataframe
        data_frame = pandas.DataFrame(input_data)
        data_frame.timestamp = pandas.to_datetime(data_frame.timestamp, utc=True)
        data_frame = data_frame[
            [DocumentLabels.TIMESTAMP, analytic_series]
        ]  # Limits the dataframe to only timestamp and the
        # value column
        data_frame = data_frame.dropna()
        return data_frame.sort_values(by=DocumentLabels.TIMESTAMP, ascending=False)  # pyright: ignore

    @classmethod
    def create_averaged_buckets(
        cls,
        time_input: TimeInput,
        bucket_range: relativedelta,
        data_frame: DataFrame,
        analytic_series: str,
        agg_function: str,
    ) -> List[SeriesAnalysisAveragedOutputBucket]:
        value_lists_buckets = cls.create_empty_value_lists_buckets(bucket_range=bucket_range, time_input=time_input)
        averaged_buckets = cls.create_averaged_buckets_from_value_lists_buckets(
            data_frame=data_frame,
            analytic_series=analytic_series,
            value_lists_buckets=value_lists_buckets,
            agg_function=agg_function,
        )
        return averaged_buckets

    @classmethod
    def create_empty_value_lists_buckets(
        cls, time_input: TimeInput, bucket_range: relativedelta
    ) -> List[SeriesAnalysisListValuesBucket]:
        """Creates value empty averaged buckets"""
        timestamps_list = [time_input.time_gte + bucket_range * i for i in reversed(range(4))]
        value_lists_buckets = []
        for timestamp in timestamps_list:
            end_time = timestamp + bucket_range
            duration = int((end_time - timestamp).total_seconds())
            value_lists_buckets.append(
                SeriesAnalysisListValuesBucket(
                    value_list=[],
                    time_range=TimeIntervalModel(timestamp=timestamp, end_time=end_time, duration=duration),
                )
            )
        return value_lists_buckets

    @classmethod
    def create_averaged_buckets_from_value_lists_buckets(
        cls,
        data_frame: DataFrame,
        value_lists_buckets: List[SeriesAnalysisListValuesBucket],
        analytic_series: str,
        agg_function: str,
    ) -> List[SeriesAnalysisAveragedOutputBucket]:
        averaged_buckets = []
        # filters out fields that don't have values for analytic series
        data_frame = DataFrame(data_frame[~data_frame[analytic_series].isna()])
        for index, row in data_frame.iterrows():
            for bucket in value_lists_buckets:
                cls.check_if_in_bucket_and_append_to_values_list(
                    row=row, bucket=bucket, analytic_series=analytic_series
                )

        for bucket in value_lists_buckets:
            aggregated_value = AggregationFunctionToMethod[agg_function](bucket.value_list)
            averaged_buckets.append(
                SeriesAnalysisAveragedOutputBucket(time_range=bucket.time_range, aggregated_value=aggregated_value)
            )

        return averaged_buckets

    @classmethod
    def check_if_in_bucket_and_append_to_values_list(
        cls, bucket: SeriesAnalysisListValuesBucket, row, analytic_series: str
    ):
        bucket_start = bucket.time_range.timestamp
        bucket_end = bucket.time_range.end_time
        # Currently disregards zero values, but these can also be valid
        # TODO Create a extension specific aggregation use case where we can specify what to return
        if (bucket_start <= row[DocumentLabels.TIMESTAMP] < bucket_end) and row[analytic_series] != 0:
            bucket.value_list.append(row[analytic_series])

    @classmethod
    def has_enough_consecutive_buckets(
        cls, averaged_buckets: List[SeriesAnalysisAveragedOutputBucket], consecutive: int = 2
    ) -> bool:
        aggregated_values = [bucket.aggregated_value for bucket in averaged_buckets]
        return len(aggregated_values) >= consecutive and all(
            value is not None for value in aggregated_values[:consecutive]
        )

    @staticmethod
    def _is_nested_field(field: str) -> bool:
        return "." in field
