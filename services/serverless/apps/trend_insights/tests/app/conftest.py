import random
from datetime import datetime, timedelta, timezone
from typing import Any, AsyncGenerator, Awaitable, Callable, List, Sequence
from uuid import UUID
from zoneinfo import ZoneInfo

import pandas as pd
import pytest
from pandas import DataFrame, Series

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.utils.hashmap import HashMapUtils
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.body_metric.blood_glucose import BloodGlucose
from services.base.domain.schemas.events.body_metric.blood_pressure import BloodPressure
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetric
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.feeling.emotion import Emotion, EmotionCategory
from services.base.domain.schemas.events.feeling.stress import Stress, StressCategory
from services.base.domain.schemas.events.note import Note
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.metadata import DeprIdentifiableMetadataModel
from services.base.domain.schemas.resting_heart_rate import RestingHeartRate
from services.base.domain.schemas.sleep import Sleep
from services.base.domain.schemas.steps import Steps
from services.base.tests.domain.builders.blood_glucose_builder import BloodGlucoseBuilder
from services.base.tests.domain.builders.blood_pressure_builder import BloodPressureBuilder
from services.base.tests.domain.builders.body_metric_builder import BodyMetricBuilder
from services.base.tests.domain.builders.emotion_builder import EmotionBuilder
from services.base.tests.domain.builders.heart_rate_builder import HeartRateBuilder
from services.base.tests.domain.builders.note_builder import NoteBuilder
from services.base.tests.domain.builders.nutrition.drink_builder import DrinkBuilder
from services.base.tests.domain.builders.nutrition.food_builder import FoodBuilder
from services.base.tests.domain.builders.nutrition.supplement_builder import SupplementBuilder
from services.base.tests.domain.builders.resting_hear_rate_builder import RestingHeartRateBuilder
from services.base.tests.domain.builders.sleep_builder import SleepBuilder
from services.base.tests.domain.builders.steps_builder import StepsBuilder
from services.base.tests.domain.builders.stress_builder import StressBuilder
from services.serverless.apps.trend_insights.tests.conftest import _delete_user_documents


def group_documents_into_buckets(
    documents: Sequence[DeprIdentifiableMetadataModel | Event],
    fields: List[str],
    interval: str,
    aggregation: str = "sum",
) -> pd.DataFrame:
    """
    Groups a list of documents into time intervals and aggregates specific fields.

    Parameters:
    - documents (List[IdentifiableMetadataModel]): A list of documents containing timestamped data.
    - fields (List[str]): The fields to be aggregated.
    - interval (str): The time interval for aggregation (e.g., 'D' for daily, 'H' for hourly).
    - aggregation (str): The type of aggregation to perform (e.g., 'sum', 'mean', 'max', 'min').

    Returns:
    - pd.Series: A time-series containing aggregated values of the specified fields over the given intervals.
    """
    data = []
    for document in documents:
        timestamp = getattr(document, DocumentLabels.TIMESTAMP, None)
        if timestamp is None:
            raise AttributeError(f"{DocumentLabels.TIMESTAMP} attribute not found in {type(document).__name__}")
        for field in fields:
            data.append(
                {
                    field.split(".")[-1]: HashMapUtils.get_nested_field_value(document, field),
                    DocumentLabels.TIMESTAMP: timestamp,
                }
            )
    df = pd.DataFrame(data)
    df[DocumentLabels.TIMESTAMP] = pd.to_datetime(df[DocumentLabels.TIMESTAMP])
    df.set_index(DocumentLabels.TIMESTAMP, inplace=True)

    split_fields = [field.split(".")[-1] for field in fields]
    if aggregation == "mean":
        buckets = df[split_fields].resample(interval).mean()
    elif aggregation == "max":
        buckets = df[split_fields].resample(interval).max()
    elif aggregation == "min":
        buckets = df[split_fields].resample(interval).min()
    else:
        buckets = df[split_fields].resample(interval).sum()

    return buckets.sort_values(by=DocumentLabels.TIMESTAMP, ascending=False)


def aggregate_series_into_single_bucket(list_of_series: List[Series], aggregation_method: str = "max") -> Series:
    """
    Aggregate multiple series into a single bucket using a specified aggregation method.

    Parameters:
    - list_of_series: List of pandas Series.
    - aggregation_method: Aggregation method, e.g., 'max', 'mean', 'sum', etc. Default is 'max'.

    Returns:
    - Resulting Series after aggregation.
    """
    df: pd.DataFrame = pd.DataFrame({f"series{i}": series for i, series in enumerate(list_of_series)})

    if aggregation_method == "max":
        result = pd.Series(df.max(axis=1))
    elif aggregation_method == "mean":
        result = pd.Series(df.mean(axis=1))
    elif aggregation_method == "sum":
        result = pd.Series(df.sum(axis=1))
    else:
        raise ValueError(f"Unsupported aggregation method: {aggregation_method}")

    return result


def combine_and_sort_two_lists_of_documents(
    list_of_documents: Sequence[Document], field: str, reverse=True
) -> list[dict]:
    """
    Combines and sorts two lists of documents based on a specified field.

    Parameters:
    - list_of_documents (List[Document]): List of Document instances.
    - field (str): The field to extract from each document.
    - reverse (bool, optional): If True, sort in descending order; if False, sort in ascending order. Default is True.

    Returns:
    - List[dict]: A list of dictionaries containing "timestamp" and the specified field, sorted by timestamp.
    """
    document_list = []
    for document in list_of_documents:
        timestamp = getattr(document, DocumentLabels.TIMESTAMP, None)
        if timestamp is None:
            raise AttributeError(f"Timestamp attribute not found in {type(document).__name__}")

        data = {
            DocumentLabels.TIMESTAMP: timestamp,
            field: getattr(document, field),
        }
        document_list.append(data)

    return sorted(document_list, key=lambda x: x[DocumentLabels.TIMESTAMP], reverse=reverse)


async def _generate_blood_pressure_data(
    user: MemberUser,
    origin: Origin,
    event_repo: EventRepository,
    days_to_load_back: int = 28,
) -> Sequence[BloodPressure]:
    now = datetime.now(tz=ZoneInfo("UTC")).replace(hour=0, minute=0, second=0, microsecond=0)
    current_datetime = now - timedelta(days=days_to_load_back)
    events = []

    while now > current_datetime:
        if 7 < current_datetime.hour < 23:
            blood_pressure = (
                BloodPressureBuilder()
                .with_timestamp(timestamp=current_datetime)
                .with_owner_id(owner_id=user.user_uuid)
                .with_origin(origin=origin)
                .build()
            )
            events.append(blood_pressure)
        current_datetime += timedelta(hours=random.randint(10, 20))

    await event_repo.insert(events=events, force_strong_consistency=True)

    return events


async def _generate_notes_data(
    user: MemberUser,
    event_repo: EventRepository,
    days_to_load_back: int = 28,
) -> list[Note]:
    now = datetime.now(tz=ZoneInfo("UTC")).replace(hour=0, minute=0, second=0, microsecond=0)
    current_datetime = now - timedelta(days=days_to_load_back)
    model_list = []

    while now > current_datetime:
        if 7 < current_datetime.hour < 23:
            note = (
                NoteBuilder().with_timestamp(timestamp=current_datetime).with_owner_id(owner_id=user.user_uuid).build()
            )
            model_list.append(note)
        current_datetime += timedelta(hours=random.randint(6, 10))

    await event_repo.insert(events=model_list, force_strong_consistency=True)

    return model_list


async def _generate_nutrition_data(
    user: MemberUser,
    event_repo: EventRepository,
    days_to_load_back: int = 28,
) -> list[Event]:
    now = datetime.now(tz=ZoneInfo("UTC")).replace(hour=0, minute=0, second=0, microsecond=0)
    current_datetime = now - timedelta(days=days_to_load_back)
    model_list = []

    while now > current_datetime:
        if 7 < current_datetime.hour < 23:
            nutrition_builder = random.choice([FoodBuilder, DrinkBuilder, SupplementBuilder])
            nutrition = (
                nutrition_builder()
                .with_timestamp(timestamp=current_datetime)
                .with_owner_id(owner_id=user.user_uuid)
                .build()
            )
            model_list.append(nutrition)
        current_datetime += timedelta(hours=random.randint(3, 10))

    await event_repo.insert(events=model_list, force_strong_consistency=True)
    return model_list


async def _generate_body_temperature_data(
    user: MemberUser,
    event_repo: EventRepository,
    days_to_load_back: int = 28,
) -> Sequence[BodyMetric]:
    now = datetime.now(tz=ZoneInfo("UTC")).replace(hour=0, minute=0, second=0, microsecond=0)
    current_datetime = now - timedelta(days=days_to_load_back)
    events = []

    while now > current_datetime:
        if 7 < current_datetime.hour < 23:
            body_temperature = (
                BodyMetricBuilder()
                .with_name(name="body temperature")
                .with_timestamp(timestamp=current_datetime)
                .with_owner_id(owner_id=user.user_uuid)
                .build()
            )
            events.append(body_temperature)
        current_datetime += timedelta(hours=random.randint(10, 20))

    await event_repo.insert(events=events, force_strong_consistency=True)

    return events


async def _generate_pulse_oxygen_data(
    user: MemberUser,
    event_repo: EventRepository,
    days_to_load_back: int = 28,
) -> Sequence[BodyMetric]:
    now = datetime.now(tz=ZoneInfo("UTC")).replace(hour=0, minute=0, second=0, microsecond=0)
    current_datetime = now - timedelta(days=days_to_load_back)
    events = []

    while now > current_datetime:
        if 7 < current_datetime.hour < 23:
            pulse_oxygen = (
                BodyMetricBuilder()
                .with_name(name="pulse oxygen saturation")
                .with_timestamp(timestamp=current_datetime)
                .with_owner_id(owner_id=user.user_uuid)
                .build()
            )
            events.append(pulse_oxygen)
        current_datetime += timedelta(hours=random.randint(10, 20))

    await event_repo.insert(events=events, force_strong_consistency=True)

    return events


async def _generate_sleep_data(
    user: MemberUser,
    event_repo: DeprEventRepository,
    days_to_load_back: int = 28,
) -> list[Sleep]:
    now = datetime.now(tz=ZoneInfo("UTC")).replace(hour=18, minute=0, second=0, microsecond=0)
    current_datetime = now - timedelta(days=days_to_load_back)
    model_list = []

    while now > current_datetime:
        sleep = (
            SleepBuilder().with_timestamp(timestamp=current_datetime).with_user_uuid(user_uuid=user.user_uuid).build()
        )
        model_list.append(sleep)
        current_datetime += timedelta(days=1)

    await event_repo.insert(models=model_list, force_strong_consistency=True)

    return model_list


async def _generate_resting_heartrate_data(
    user: MemberUser,
    event_repo: DeprEventRepository,
    days_to_load_back: int = 28,
) -> list[RestingHeartRate]:
    now = datetime.now(tz=ZoneInfo("UTC")).replace(hour=0, minute=0, second=0, microsecond=0)
    current_datetime = now - timedelta(days=days_to_load_back)
    model_list = []

    while now > current_datetime:
        resting_heartrate = (
            RestingHeartRateBuilder()
            .with_timestamp(timestamp=current_datetime)
            .with_user_uuid(user_uuid=user.user_uuid)
            .build()
        )
        model_list.append(resting_heartrate)
        current_datetime += timedelta(hours=1)

    await event_repo.insert(models=model_list, force_strong_consistency=True)

    return model_list


async def _generate_heartrate_data(
    user: MemberUser,
    event_repo: DeprEventRepository,
    days_to_load_back: int = 28,
) -> list[HeartRate]:
    now = datetime.now(tz=ZoneInfo("UTC")).replace(hour=0, minute=0, second=0, microsecond=0)
    current_datetime = now - timedelta(days=days_to_load_back)
    model_list = []

    while now > current_datetime:
        heartrate = (
            HeartRateBuilder()
            .with_timestamp(timestamp=current_datetime)
            .with_user_uuid(user_uuid=user.user_uuid)
            .build()
        )
        model_list.append(heartrate)
        current_datetime += timedelta(hours=1)

    await event_repo.insert(models=model_list, force_strong_consistency=True)

    return model_list


async def _generate_steps_data(
    user: MemberUser,
    organization: Organization,
    event_repo: DeprEventRepository,
    days_to_load_back: int = 28,
    skip_randomly=False,
) -> list[Steps]:
    now = datetime.now(tz=ZoneInfo("UTC")).replace(hour=0, minute=0, second=0, microsecond=0)
    current_datetime = now - timedelta(days=days_to_load_back)
    model_list = []

    while now > current_datetime:
        if 7 < current_datetime.hour < 23:
            if skip_randomly and random.randint(1, 5) == 1:
                current_datetime += timedelta(hours=1)
                continue
            step = (
                StepsBuilder()
                .with_timestamp(timestamp=current_datetime)
                .with_user_uuid(user_uuid=user.user_uuid)
                .with_organization(organization)
                .build()
            )
            model_list.append(step)
        current_datetime += timedelta(hours=1)

    await event_repo.insert(models=model_list, force_strong_consistency=True)

    return model_list


async def _generate_emotion_data(
    user_id: UUID,
    event_repo: EventRepository,
    days_to_load_back: int = 28,
) -> Sequence[Emotion]:
    today_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(
        seconds=1
    )
    emotion_list = []

    for i in range(days_to_load_back):
        timestamp = today_midnight - timedelta(days=i + 1)
        emotion_list.extend(
            [
                EmotionBuilder()
                .with_timestamp(timestamp)
                .with_owner_id(user_id)
                .with_category(EmotionCategory.MOOD)
                .build(),
            ]
        )
        emotion_list.extend(
            [
                EmotionBuilder()
                .with_timestamp(timestamp)
                .with_owner_id(user_id)
                .with_category(EmotionCategory.TRUST)
                .with_name("PartnerTrust")
                .build(),
            ]
        )

    _ = await event_repo.insert(events=emotion_list, force_strong_consistency=True)

    return emotion_list


async def _generate_stress_data(
    user_id: UUID,
    event_repo: EventRepository,
    days_to_load_back: int = 28,
) -> Sequence[Stress]:
    today_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(
        seconds=1
    )
    stress_list = []

    for i in range(days_to_load_back):
        timestamp = today_midnight - timedelta(days=i + 1)
        stress_list.extend(
            [
                StressBuilder()
                .with_timestamp(timestamp)
                .with_owner_id(user_id)
                .with_category(StressCategory.PHYSICAL_ACTIVITY)
                .build(),
                StressBuilder()
                .with_timestamp(timestamp)
                .with_owner_id(user_id)
                .with_category(StressCategory.SOCIAL_ACTIVITY)
                .build(),
                StressBuilder()
                .with_timestamp(timestamp)
                .with_owner_id(user_id)
                .with_category(StressCategory.MENTAL_ACTIVITY)
                .build(),
            ]
        )

    _ = await event_repo.insert(events=stress_list, force_strong_consistency=True)

    return stress_list


async def _generate_trending_stress_data(
    user: MemberUser,
    event_repo: EventRepository,
) -> Sequence[Stress]:
    today_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(
        seconds=1
    )
    stress_list = []
    phys_decreasing = [-5, -4, -4, -3, -3, -3, -2, -2, -2, -1, -1, 0, 0, 0, 0, 0, 0, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 5]
    int_increasing = [5, 4, 4, 3, 3, 3, 2, 2, 2, 1, 1, 0, 0, 0, 0, 0, 0, -1, -1, -2, -2, -2, -3, -3, -3, -4, -4, -5]
    social_no_trend = [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5]
    for i in range(28):
        timestamp = today_midnight - timedelta(days=i + 1)
        stress_list.extend(
            [
                StressBuilder()
                .with_timestamp(timestamp)
                .with_owner_id(user.user_uuid)
                .with_category(StressCategory.PHYSICAL_ACTIVITY)
                .with_rating(phys_decreasing[i])
                .build(),
                StressBuilder()
                .with_timestamp(timestamp)
                .with_owner_id(user.user_uuid)
                .with_category(StressCategory.SOCIAL_ACTIVITY)
                .with_rating(social_no_trend[i])
                .build(),
                StressBuilder()
                .with_timestamp(timestamp)
                .with_owner_id(user.user_uuid)
                .with_category(StressCategory.MENTAL_ACTIVITY)
                .with_rating(int_increasing[i])
                .build(),
            ]
        )

    _ = await event_repo.insert(events=stress_list, force_strong_consistency=True)

    return stress_list


async def _generate_blood_glucose_data(
    user: MemberUser,
    origin: Origin,
    event_repo: EventRepository,
    days_to_load_back: int = 28,
) -> Sequence[BloodGlucose]:
    now = datetime.now(tz=ZoneInfo("UTC")).replace(hour=0, minute=0, second=0, microsecond=0)
    current_datetime = now - timedelta(days=days_to_load_back)
    events = []

    while now > current_datetime:
        if 7 < current_datetime.hour < 23:
            blood_sugar_level = (
                BloodGlucoseBuilder()
                .with_timestamp(timestamp=current_datetime)
                .with_owner_id(owner_id=user.user_uuid)
                .with_origin(origin=origin)
                .build()
            )
            events.append(blood_sugar_level)
        current_datetime += timedelta(hours=random.randint(10, 20))

    _ = await event_repo.insert(events=events, force_strong_consistency=True)

    return events


@pytest.fixture
async def user_with_blood_sugar_data(
    event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
) -> AsyncGenerator[tuple[MemberUser, list[dict]], None]:
    user = await user_factory()
    blood_sugar_levels_1 = await _generate_blood_glucose_data(user=user, origin=Origin.GOOGLE, event_repo=event_repo)
    blood_sugar_levels_2 = await _generate_blood_glucose_data(user=user, origin=Origin.LLIF, event_repo=event_repo)

    sorted_data = combine_and_sort_two_lists_of_documents(
        list_of_documents=[*blood_sugar_levels_1, *blood_sugar_levels_2], field="blood_sugar_level"
    )

    yield user, sorted_data

    # Teardown
    await event_repo.delete_by_id(ids=[e.id for e in [*blood_sugar_levels_1, *blood_sugar_levels_2]])


@pytest.fixture
async def user_with_steps_data(
    depr_event_repo: DeprEventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
) -> AsyncGenerator[tuple[MemberUser, list[int]], None]:
    user = await user_factory()
    steps = await _generate_steps_data(user=user, organization=Organization.FITBIT, event_repo=depr_event_repo)

    bucket_values = group_documents_into_buckets(documents=steps, fields=["steps"], interval="D")

    steps_list = bucket_values["steps"].tolist()
    steps_list = [int(value) for value in steps_list]

    yield user, steps_list

    # Teardown
    await _delete_user_documents(user_uuid=user.user_uuid, data_schemas=[Steps], event_repo=depr_event_repo)


@pytest.fixture
async def user_with_events_nutrition_data(
    event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
) -> AsyncGenerator[tuple[MemberUser, DataFrame], None]:
    user = await user_factory()
    events = await _generate_nutrition_data(user=user, event_repo=event_repo)

    bucket_values = group_documents_into_buckets(
        documents=events,
        fields=[
            "calories",
            "nutrients.fat",
            "nutrients.protein",
            "nutrients.carbohydrates",
            "nutrients.sodium",
        ],
        interval="D",
        aggregation="sum",
    )

    yield user, bucket_values

    # Teardown
    await event_repo.delete_by_id(ids=[e.id for e in events])


@pytest.fixture
async def user_with_emotion_data(
    user_factory: Callable[[], Awaitable[MemberUser]], event_repo: EventRepository
) -> AsyncGenerator[tuple[MemberUser, Sequence[Emotion]], None]:
    user = await user_factory()

    inserted_emotion = await _generate_emotion_data(user_id=user.user_uuid, event_repo=event_repo, days_to_load_back=28)

    yield user, inserted_emotion

    # Teardown
    await event_repo.delete_by_id(ids=[e.id for e in inserted_emotion])


@pytest.fixture
async def user_with_stress_data(
    user_factory: Callable[[], Awaitable[MemberUser]], event_repo: EventRepository
) -> AsyncGenerator[tuple[MemberUser, Sequence[Stress]], None]:
    user = await user_factory()

    inserted_stress = await _generate_stress_data(user_id=user.user_uuid, event_repo=event_repo, days_to_load_back=28)

    yield user, inserted_stress

    # Teardown
    await event_repo.delete_by_id(ids=[e.id for e in inserted_stress])


@pytest.fixture
async def user_with_body_metric_v3_data(
    event_repo: EventRepository,
    user_factory: Callable[[], Awaitable[MemberUser]],
) -> AsyncGenerator[tuple[MemberUser, Sequence[Event]], Any]:
    user = await user_factory()
    now = datetime.now(tz=ZoneInfo("UTC")).replace(hour=0, minute=0, second=0, microsecond=0)
    gte = now - timedelta(days=28)
    body_metric_list = []
    for i in range(random.randint(1, 5)):
        track_name = PrimitiveTypesGenerator.generate_random_string(min_length=5, max_length=10)
        body_metric = (
            BodyMetricBuilder()
            .with_name(name=track_name)
            .with_timestamp(timestamp=PrimitiveTypesGenerator.generate_random_aware_datetime(gte=gte, lte=now))
            .with_owner_id(owner_id=user.user_uuid)
            .build_n(random.randint(10, 20))
        )
        body_metric_list.extend(body_metric)

    inserted_events = await event_repo.insert(events=body_metric_list, force_strong_consistency=True)

    yield user, inserted_events

    await event_repo.delete_by_id(ids=[e.id for e in inserted_events])


@pytest.fixture
async def user_with_steps_data_three_orgs(
    depr_event_repo: DeprEventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
) -> AsyncGenerator[tuple[MemberUser, list[float]], None]:
    user = await user_factory()
    steps_fitbit = await _generate_steps_data(
        user=user, organization=Organization.FITBIT, event_repo=depr_event_repo, skip_randomly=True
    )
    steps_apple = await _generate_steps_data(
        user=user, organization=Organization.APPLE, event_repo=depr_event_repo, skip_randomly=True
    )
    steps_google = await _generate_steps_data(
        user=user, organization=Organization.GOOGLE, event_repo=depr_event_repo, skip_randomly=True
    )
    series_fitbit = group_documents_into_buckets(documents=steps_fitbit, fields=["steps"], interval="D")
    series_apple = group_documents_into_buckets(documents=steps_apple, fields=["steps"], interval="D")
    series_google = group_documents_into_buckets(documents=steps_google, fields=["steps"], interval="D")

    bucket_values = aggregate_series_into_single_bucket(
        list_of_series=[
            pd.Series(series_apple["steps"]),
            pd.Series(series_fitbit["steps"]),
            pd.Series(series_google["steps"]),
        ],
        aggregation_method="max",
    ).tolist()
    bucket_values = [float(value) for value in bucket_values]

    yield user, bucket_values

    # Teardown
    await _delete_user_documents(user_uuid=user.user_uuid, data_schemas=[Steps], event_repo=depr_event_repo)


@pytest.fixture
async def user_with_multiple_data(
    depr_event_repo: DeprEventRepository, user_factory: Callable[[], Awaitable[MemberUser]], event_repo: EventRepository
) -> AsyncGenerator[MemberUser, Any]:
    user = await user_factory()
    nutrition = await _generate_nutrition_data(user=user, event_repo=event_repo, days_to_load_back=28)
    emotions = await _generate_emotion_data(user_id=user.user_uuid, event_repo=event_repo, days_to_load_back=28)
    stress = await _generate_stress_data(user_id=user.user_uuid, event_repo=event_repo, days_to_load_back=28)
    blood_glucose_1 = await _generate_blood_glucose_data(
        user=user, origin=Origin.GOOGLE, event_repo=event_repo, days_to_load_back=28
    )
    blood_glucose_2 = await _generate_blood_glucose_data(
        user=user, origin=Origin.LLIF, event_repo=event_repo, days_to_load_back=28
    )
    blood_pressure = await _generate_blood_pressure_data(
        user=user, origin=Origin.LLIF, event_repo=event_repo, days_to_load_back=28
    )
    pulse_ox = await _generate_pulse_oxygen_data(user=user, event_repo=event_repo, days_to_load_back=28)
    body_temp = await _generate_body_temperature_data(user=user, event_repo=event_repo, days_to_load_back=28)
    _ = await _generate_resting_heartrate_data(user=user, event_repo=depr_event_repo, days_to_load_back=28)
    _ = await _generate_heartrate_data(user=user, event_repo=depr_event_repo, days_to_load_back=28)
    _ = await _generate_sleep_data(user=user, event_repo=depr_event_repo, days_to_load_back=28)
    _ = await _generate_notes_data(user=user, event_repo=event_repo, days_to_load_back=28)
    yield user

    # Teardown
    await _delete_user_documents(
        user_uuid=user.user_uuid,
        data_schemas=[
            Steps,
            Sleep,
            RestingHeartRate,
        ],
        event_repo=depr_event_repo,
    )
    await event_repo.delete_by_id(
        ids=[
            e.id
            for e in [
                *emotions,
                *stress,
                *blood_glucose_1,
                *blood_glucose_2,
                *blood_pressure,
                *pulse_ox,
                *body_temp,
                *nutrition,
            ]
        ]
    )


@pytest.fixture
async def user_with_short_term_trending_stress_data(
    user_factory: Callable[[], Awaitable[MemberUser]], event_repo: EventRepository
) -> AsyncGenerator[MemberUser, Any]:
    user = await user_factory()
    stress = await _generate_trending_stress_data(user=user, event_repo=event_repo)

    yield user

    await event_repo.delete_by_id(ids=[e.id for e in list(stress)])


@pytest.fixture
async def user_with_yearly_emotion_data(
    user_factory: Callable[[], Awaitable[MemberUser]], event_repo: EventRepository
) -> AsyncGenerator[MemberUser, Any]:
    user = await user_factory()
    emotions = await _generate_emotion_data(user_id=user.user_uuid, event_repo=event_repo, days_to_load_back=365)

    yield user

    await event_repo.delete_by_id(ids=[e.id for e in list(emotions)])


@pytest.fixture
async def user_with_no_data(user_factory: Callable[[], Awaitable[MemberUser]]) -> AsyncGenerator[MemberUser, Any]:
    user = await user_factory()

    yield user
