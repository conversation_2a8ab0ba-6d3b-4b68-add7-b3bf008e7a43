import logging
import os
from uuid import UUID

import sentry_sdk
from azure.monitor.opentelemetry import configure_azure_monitor
from logfire import Log<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, configure, instrument_pydantic, instrument_pydantic_ai, metric_counter
from opentelemetry import trace
from opentelemetry.metrics import Counter
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from sentry_sdk.integrations.logging import LoggingIntegration

from settings.app_config import AppSettings
from settings.app_constants import RUN_ENV_LOCAL
from settings.app_secrets import AppSecrets


class TelemetryInstrumentor:
    is_initialized = False

    @classmethod
    def initialize(cls, service_name: str, settings: AppSettings, secrets: AppSecrets):
        if settings.RUN_ENV == RUN_ENV_LOCAL or not settings.IS_CONTAINERIZED:
            logging.info("skipping telemetry initialisation for local")
            return
        if cls.is_initialized:
            logging.debug("Telemetry already initialized; skipping.")
            return

        logging.info("initializing telemetry")
        cls._initialize_open_telemetry(
            settings=settings,
            secrets=secrets,
            service_name=service_name,
        )

        cls._initialize_sentry(
            dsn=secrets.MY_LLIF_SENTRY_DSN,
            settings=settings,
            secrets=secrets,
            service_name=service_name,
            integrations=[LoggingIntegration(level=logging.INFO, event_level=logging.WARNING)],
        )
        cls.is_initialized = True

    @classmethod
    def add_telemetry_user(cls, user_id: UUID):
        if not cls.is_initialized:
            logging.debug("telemetryInstrumentor is not initialized")
            return

        sentry_sdk.set_user({"id": str(user_id)})
        # OTEL
        span = trace.get_current_span()
        if span:
            span.set_attribute("user_id", str(user_id))

    @classmethod
    def _initialize_sentry(
        cls, dsn: str, service_name: str, settings: AppSettings, secrets: AppSecrets, integrations: list
    ):
        logging.info("initializing sentry")

        sentry_sdk.init(
            dsn=dsn,
            release=settings.APP_VERSION,
            debug=False,
            environment=settings.RUN_ENV,
            send_default_pii=False,
            traces_sample_rate=settings.TELEMETRY_TRACES_SAMPLE_RATE,
            profiles_sample_rate=settings.TELEMETRY_PROFILE_SAMPLE_RATE,
            max_request_body_size=settings.TELEMETRY_REQUEST_BODIES,
            integrations=integrations,
            server_name=service_name,
            enable_logs=True,  # pyright: ignore
        )

    @classmethod
    def _initialize_open_telemetry(cls, service_name: str, settings: AppSettings, secrets: AppSecrets):
        logging.info("initializing opentelemetry")
        # increase timeout to 60 seconds
        os.environ["OTEL_BSP_EXPORT_TIMEOUT"] = "60000"
        # OTEL
        resource = Resource.create(
            {
                "service.name": service_name,
                "service.version": settings.APP_VERSION,
                "deployment.environment": settings.RUN_ENV,
            }
        )

        tracer_provider = TracerProvider(resource=resource)
        trace.set_tracer_provider(tracer_provider)

        # Sentry (requires otel instrumenter)
        # if secrets.MY_LLIF_SENTRY_DSN:
        #     tracer_provider.add_span_processor(SentrySpanProcessor())
        #     set_global_textmap(SentryPropagator())

        # Azure
        if secrets.AZURE_APP_INSIGHTS_CONNECTION_STRING:
            configure_azure_monitor(
                resource=resource,
                connection_string=secrets.AZURE_APP_INSIGHTS_CONNECTION_STRING,
                instrumentation_options={
                    "flask": {"enabled": False},
                    "django": {"enabled": False},
                },
            )

        # Logfire
        if secrets.MY_LLIF_LOGFIRE_WRITE_TOKEN:
            configure(
                send_to_logfire="if-token-present",
                console=False,
                token=secrets.MY_LLIF_LOGFIRE_WRITE_TOKEN,
                environment=settings.RUN_ENV,
                service_name=service_name,
                service_version=settings.APP_VERSION,
            )
            instrument_pydantic(record="metrics")
            instrument_pydantic_ai()
            logger = logging.getLogger()
            logger.addHandler(LogfireLoggingHandler())

    @classmethod
    def get_counter(cls, name: str, unit: str = "1") -> Counter:
        return metric_counter(name=name, unit=unit)
