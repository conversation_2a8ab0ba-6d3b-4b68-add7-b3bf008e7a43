from services.base.api.output.contact.contact_api_output import ContactAPIOutput
from services.base.api.output.events.body_metric_api_outputs import (
    BloodGlucoseAPIOutput,
    BloodPressureAPIOutput,
    BodyMetricAPIOutput,
)
from services.base.api.output.events.content_api_outputs import ContentAPIOutput
from services.base.api.output.events.event_v3_api_output import (
    ActivityAPIOutput,
    EventGroupAPIOutput,
    NoteAPIOutput,
    PersonAPIOutput,
    PlaceVisitAPIOutput,
    SymptomAPIOutput,
)
from services.base.api.output.events.exercise_api_outputs import CardioAPIOutput, ExerciseAPIOutput, StrengthAPIOutput
from services.base.api.output.events.feeling_api_outputs import EmotionAPIOutput, StressAPIOutput
from services.base.api.output.events.medication_api_output import MedicationAPIOutput
from services.base.api.output.events.nutrition_api_outputs import DrinkAPIOutput, FoodAPIOutput, SupplementAPIOutput
from services.base.api.output.events.sleep_v3_api_output import SleepV3APIOutput
from services.base.api.output.place.place_api_output import PlaceAPIOutput
from services.base.api.output.plan.plan_api_outputs import GoalAPIOutput, PlanAPIOutput
from services.base.api.output.records.body_metric_record_api_output import BodyMetricRecordAPIOutput
from services.base.api.output.records.sleep_record_api_output import SleepRecordAPIOutput
from services.base.api.output.records.steps_record_api_output import StepsRecordAPIOutput
from services.base.api.output.template.template_api_outputs import EventTemplateAPIOutput, GroupTemplateAPIOutput
from services.base.api.output.use_case.use_case_api_output import UseCaseAPIOutput
from services.base.api.output.v1.heart_rate_api_output import HeartRateAPIOutput
from services.base.api.output.v1.location_api_output import LocationAPIOutput
from services.base.api.output.v1.resting_heart_rate_api_output import RestingHeartRateAPIOutput
from services.base.api.output.v1.shopping_activity_api_output import ShoppingActivityAPIOutput
from services.base.api.output.v1.sleep_api_output import SleepAPIOutput
from services.base.api.output.v1.steps_api_output import StepsAPIOutput
from services.base.domain.enums.place_category import PlaceCategory
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.air_quality import AirQuality
from services.base.domain.schemas.contact import Contact
from services.base.domain.schemas.events.activity import Activity, ActivityCategory
from services.base.domain.schemas.events.body_metric.blood_glucose import BloodGlucose
from services.base.domain.schemas.events.body_metric.blood_pressure import BloodPressure
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetric, BodyMetricCategory
from services.base.domain.schemas.events.content.content import Content, ContentCategory
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.domain.schemas.events.exercise.cardio import Cardio, CardioCategory
from services.base.domain.schemas.events.exercise.exercise import Exercise, ExerciseCategory
from services.base.domain.schemas.events.exercise.strength import Strength, StrengthCategory
from services.base.domain.schemas.events.feeling.emotion import Emotion, EmotionCategory
from services.base.domain.schemas.events.feeling.stress import Stress, StressCategory
from services.base.domain.schemas.events.medication.medication import Medication, MedicationCategory
from services.base.domain.schemas.events.note import Note, NoteCategory
from services.base.domain.schemas.events.nutrition.drink import Drink, DrinkCategory
from services.base.domain.schemas.events.nutrition.food import Food, FoodCategory
from services.base.domain.schemas.events.nutrition.supplement import Supplement, SupplementCategory
from services.base.domain.schemas.events.person import Person
from services.base.domain.schemas.events.place_visit import PlaceVisit
from services.base.domain.schemas.events.sleep_v3 import SleepV3
from services.base.domain.schemas.events.symptom import Symptom, SymptomCategory
from services.base.domain.schemas.events.use_case import UseCase
from services.base.domain.schemas.extension_output import ExtensionResult, ExtensionRun
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.inbox.inbox_message import InboxMessage
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.place import Place
from services.base.domain.schemas.plan.plan import Plan
from services.base.domain.schemas.pollen import Pollen
from services.base.domain.schemas.records.body_metric_record import BodyMetricRecord
from services.base.domain.schemas.records.sleep_record import SleepRecord
from services.base.domain.schemas.records.steps_record import StepsRecord
from services.base.domain.schemas.resting_heart_rate import RestingHeartRate
from services.base.domain.schemas.shopping_activity import ShoppingActivity
from services.base.domain.schemas.sleep import Sleep
from services.base.domain.schemas.steps import Steps
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.templates.payload.activity_template_payload import ActivityTemplatePayload
from services.base.domain.schemas.templates.payload.body_metric_template_payloads import (
    BloodGlucoseTemplatePayload,
    BloodPressureTemplatePayload,
    BodyMetricTemplatePayload,
)
from services.base.domain.schemas.templates.payload.content_template_payloads import ContentTemplatePayload
from services.base.domain.schemas.templates.payload.exercise_template_payloads import (
    CardioTemplatePayload,
    ExerciseTemplatePayload,
    StrengthTemplatePayload,
)
from services.base.domain.schemas.templates.payload.feeling_template_payloads import (
    EmotionTemplatePayload,
    StressTemplatePayload,
)
from services.base.domain.schemas.templates.payload.medication_template_payload import MedicationTemplatePayload
from services.base.domain.schemas.templates.payload.note_template_payload import NoteTemplatePayload
from services.base.domain.schemas.templates.payload.nutrition_collection_template_payloads import (
    DrinkTemplatePayload,
    FoodTemplatePayload,
    SupplementTemplatePayload,
)
from services.base.domain.schemas.templates.payload.person_template_payload import PersonTemplatePayload
from services.base.domain.schemas.templates.payload.place_visit_template_payload import PlaceVisitTemplatePayload
from services.base.domain.schemas.templates.payload.sleep_v3_template_payload import SleepV3TemplatePayload
from services.base.domain.schemas.templates.payload.symptom_template_payload import SymptomTemplatePayload
from services.base.domain.schemas.usage_statistics_output import UsageStatistics
from services.base.domain.schemas.user_action_log import UserActionLog
from services.base.domain.schemas.weather import Weather
from services.base.tests.domain.builders.activity_builder import ActivityBuilder
from services.base.tests.domain.builders.blood_glucose_builder import BloodGlucoseBuilder
from services.base.tests.domain.builders.blood_pressure_builder import BloodPressureBuilder
from services.base.tests.domain.builders.body_metric_builder import BodyMetricBuilder
from services.base.tests.domain.builders.content.content_builder import ContentBuilder
from services.base.tests.domain.builders.emotion_builder import EmotionBuilder
from services.base.tests.domain.builders.event_group_builder import EventGroupBuilder
from services.base.tests.domain.builders.exercise.cardio_builder import CardioBuilder
from services.base.tests.domain.builders.exercise.exercise_builder import ExerciseBuilder
from services.base.tests.domain.builders.exercise.strength_builder import StrengthBuilder
from services.base.tests.domain.builders.medication_builder import MedicationBuilder
from services.base.tests.domain.builders.note_builder import NoteBuilder
from services.base.tests.domain.builders.nutrition.drink_builder import DrinkBuilder
from services.base.tests.domain.builders.nutrition.food_builder import FoodBuilder
from services.base.tests.domain.builders.nutrition.supplement_builder import SupplementBuilder
from services.base.tests.domain.builders.person_builder import PersonBuilder
from services.base.tests.domain.builders.place_visit_builder import PlaceVisitBuilder
from services.base.tests.domain.builders.records.body_metric_record_builder import BodyMetricRecordBuilder
from services.base.tests.domain.builders.records.sleep_record_builder import SleepRecordBuilder
from services.base.tests.domain.builders.records.steps_record_builder import StepsRecordBuilder
from services.base.tests.domain.builders.sleep_builder_v3 import SleepV3Builder
from services.base.tests.domain.builders.stress_builder import StressBuilder
from services.base.tests.domain.builders.symptom_builder import SymptomBuilder
from services.base.tests.domain.builders.template.payload.activity_payload_builder import ActivityPayloadBuilder
from services.base.tests.domain.builders.template.payload.body_metric_payload_builders import (
    BloodGlucosePayloadBuilder,
    BloodPressurePayloadBuilder,
    BodyMetricPayloadBuilder,
)
from services.base.tests.domain.builders.template.payload.content_payload_builders import (
    ContentPayloadBuilder,
)
from services.base.tests.domain.builders.template.payload.exercise_payload_builders import (
    CardioPayloadBuilder,
    ExercisePayloadBuilder,
    StrengthPayloadBuilder,
)
from services.base.tests.domain.builders.template.payload.feeling_payload_builders import (
    EmotionPayloadBuilder,
    StressPayloadBuilder,
)
from services.base.tests.domain.builders.template.payload.medication_payload_builder import MedicationPayloadBuilder
from services.base.tests.domain.builders.template.payload.note_payload_builder import NotePayloadBuilder
from services.base.tests.domain.builders.template.payload.nutrition_payload_builders import (
    DrinkPayloadBuilder,
    FoodPayloadBuilder,
    SupplementPayloadBuilder,
)
from services.base.tests.domain.builders.template.payload.person_payload_builder import PersonPayloadBuilder
from services.base.tests.domain.builders.template.payload.place_visit_payload_builder import PlaceVisitPayloadBuilder
from services.base.tests.domain.builders.template.payload.sleep_v3_payload_builder import SleepV3PayloadBuilder
from services.base.tests.domain.builders.template.payload.symptom_payload_builder import SymptomPayloadBuilder


class TypeResolver:
    CATEGORY_UNION = (
        BodyMetricCategory
        | ContentCategory
        | ExerciseCategory
        | CardioCategory
        | StrengthCategory
        | EmotionCategory
        | StressCategory
        | FoodCategory
        | DrinkCategory
        | SupplementCategory
        | ActivityCategory
        | NoteCategory
        | SymptomCategory
        | MedicationCategory
        | PlaceCategory
    )

    TEMPLATE_PAYLOADS_UNION = (
        # Body Metric
        BloodGlucoseTemplatePayload
        | BloodPressureTemplatePayload
        | BodyMetricTemplatePayload
        # Content
        | ContentTemplatePayload
        # Exercise
        | CardioTemplatePayload
        | ExerciseTemplatePayload
        | StrengthTemplatePayload
        # Feeling
        | EmotionTemplatePayload
        | StressTemplatePayload
        # Nutrition
        | DrinkTemplatePayload
        | FoodTemplatePayload
        | SupplementTemplatePayload
        # Other
        | ActivityTemplatePayload
        | SleepV3TemplatePayload
        | NoteTemplatePayload
        | SymptomTemplatePayload
        | MedicationTemplatePayload
        | PersonTemplatePayload
        | PlaceVisitTemplatePayload
    )

    GROUP_UNION = EventGroup

    # Collection Unions
    BODY_METRIC_COLLECTION_UNION = BloodGlucose | BloodPressure | BodyMetric

    EXERCISE_COLLECTION_UNION = Cardio | Exercise | Strength

    FEELINGS_COLLECTION_UNION = Emotion | Stress

    NUTRITION_COLLECTION_UNION = Drink | Food | Supplement

    EVENT_V3_UNION = (
        BODY_METRIC_COLLECTION_UNION
        | EXERCISE_COLLECTION_UNION
        | FEELINGS_COLLECTION_UNION
        | NUTRITION_COLLECTION_UNION
        | GROUP_UNION
        | Activity
        | Content
        | Note
        | Medication
        | Symptom
        | SleepV3
        | Person
        | PlaceVisit
    )

    RECORD_UNION = BodyMetricRecord | SleepRecord | StepsRecord

    EVENT_V2_UNION = HeartRate | Location | RestingHeartRate | Sleep | Steps

    ENVIRONMENT_UNION = AirQuality | Pollen | Weather

    DOCUMENT_V3_UNION = EVENT_V3_UNION | RECORD_UNION | EventTemplate | GroupTemplate | Plan | UseCase | Contact | Place

    DOCUMENT_UNION = (
        DOCUMENT_V3_UNION
        | EVENT_V2_UNION
        | ENVIRONMENT_UNION
        # Other
        | ExtensionResult
        | ExtensionRun
        | InboxMessage
        | ShoppingActivity
        | UsageStatistics
        | UserActionLog
    )

    EVENT_API_OUTPUTS_V3_UNION = (
        # Body Metrics
        BloodGlucoseAPIOutput
        | BloodPressureAPIOutput
        | BodyMetricAPIOutput
        # Content
        | ContentAPIOutput
        # Exercise
        | ExerciseAPIOutput
        | CardioAPIOutput
        | StrengthAPIOutput
        # Feeling
        | EmotionAPIOutput
        | StressAPIOutput
        # Nutrition
        | DrinkAPIOutput
        | FoodAPIOutput
        | SupplementAPIOutput
        # Other Events
        | ActivityAPIOutput
        | SleepV3APIOutput
        | EventGroupAPIOutput
        | NoteAPIOutput
        | SymptomAPIOutput
        | MedicationAPIOutput
        | PersonAPIOutput
        | PlaceVisitAPIOutput
    )

    EVENT_API_OUTPUTS_V2_UNION = (
        HeartRateAPIOutput
        | RestingHeartRateAPIOutput
        | SleepAPIOutput
        | StepsAPIOutput
        | LocationAPIOutput
        | ShoppingActivityAPIOutput
    )

    EVENT_API_OUTPUTS_UNION = EVENT_API_OUTPUTS_V2_UNION | EVENT_API_OUTPUTS_V3_UNION
    RECORD_API_OUTPUTS_UNION = SleepRecordAPIOutput | BodyMetricRecordAPIOutput | StepsRecordAPIOutput
    TEMPLATE_API_OUTPUTS_UNION = EventTemplateAPIOutput | GroupTemplateAPIOutput
    PLAN_API_OUTPUTS_UNION = PlanAPIOutput | GoalAPIOutput

    DOCUMENT_API_OUTPUTS_UNION = (
        EVENT_API_OUTPUTS_UNION
        | RECORD_API_OUTPUTS_UNION
        | TEMPLATE_API_OUTPUTS_UNION
        | ContactAPIOutput
        | PLAN_API_OUTPUTS_UNION
        | UseCaseAPIOutput
        | PlaceAPIOutput
    )

    GROUP_BUILDERS_UNION = EventGroupBuilder

    EVENT_BUILDERS_UNION = (
        # Body Metric
        BloodGlucoseBuilder
        | BloodPressureBuilder
        | BodyMetricBuilder
        # Content
        | ContentBuilder
        # Exercise
        | CardioBuilder
        | ExerciseBuilder
        | StrengthBuilder
        # Feeling
        | EmotionBuilder
        | StressBuilder
        # Nutrition
        | DrinkBuilder
        | FoodBuilder
        | SupplementBuilder
        # Other
        | ActivityBuilder
        | SleepV3Builder
        | NoteBuilder
        | SymptomBuilder
        | MedicationBuilder
        | PersonBuilder
        | PlaceVisitBuilder
    )

    TEMPLATE_PAYLOAD_BUILDERS_UNION = (
        # Body Metric
        BloodGlucosePayloadBuilder
        | BloodPressurePayloadBuilder
        | BodyMetricPayloadBuilder
        # Content
        | ContentPayloadBuilder
        # Exercise
        | CardioPayloadBuilder
        | ExercisePayloadBuilder
        | StrengthPayloadBuilder
        # Feeling
        | EmotionPayloadBuilder
        | StressPayloadBuilder
        # Nutrition
        | DrinkPayloadBuilder
        | FoodPayloadBuilder
        | SupplementPayloadBuilder
        # Other
        | ActivityPayloadBuilder
        | SleepV3PayloadBuilder
        | NotePayloadBuilder
        | SymptomPayloadBuilder
        | MedicationPayloadBuilder
        | PersonPayloadBuilder
        | PlaceVisitPayloadBuilder
    )

    TEMPLATE_PAYLOAD_BUILDERS: list[type[TEMPLATE_PAYLOAD_BUILDERS_UNION]] = [
        payload_builder for payload_builder in TEMPLATE_PAYLOAD_BUILDERS_UNION.__args__
    ]

    EVENT_BUILDERS: list[type[EVENT_BUILDERS_UNION]] = [
        event_builder for event_builder in EVENT_BUILDERS_UNION.__args__
    ]

    GROUP_BUILDERS: list[type[GROUP_BUILDERS_UNION]] = [
        EventGroupBuilder,  # TODO when we have more groups replace with <Union>.__args__
    ]

    FEELINGS_COLLECTION: list[type[FEELINGS_COLLECTION_UNION]] = [
        feeling_type for feeling_type in FEELINGS_COLLECTION_UNION.__args__
    ]
    BODY_METRIC_COLLECTION: list[type[BODY_METRIC_COLLECTION_UNION]] = [
        body_metric_type for body_metric_type in BODY_METRIC_COLLECTION_UNION.__args__
    ]
    NUTRITION_COLLECTION: list[type[NUTRITION_COLLECTION_UNION]] = [
        nutrition_type for nutrition_type in NUTRITION_COLLECTION_UNION.__args__
    ]

    EVENTS_V3: list[type[EVENT_V3_UNION]] = [event_type for event_type in EVENT_V3_UNION.__args__]

    EVENTS_V2: list[type[EVENT_V2_UNION]] = [event_type for event_type in EVENT_V2_UNION.__args__]

    ENVIRONMENTS: list[type[ENVIRONMENT_UNION]] = [environment_type for environment_type in ENVIRONMENT_UNION.__args__]

    DOCUMENTS_V3: list[type[DOCUMENT_V3_UNION]] = [document_type for document_type in DOCUMENT_V3_UNION.__args__]

    DOCUMENTS: list[type[DOCUMENT_UNION]] = [document_type for document_type in DOCUMENT_UNION.__args__]

    # GROUPS: list[type[GROUP_UNION]] = [EventGroup]  # TODO when we have more groups replace with <Union>.__args__

    TEMPLATE_PAYLOADS: list[type[TEMPLATE_PAYLOADS_UNION]] = [
        template_payload for template_payload in TEMPLATE_PAYLOADS_UNION.__args__
    ]

    RECORDS: list[type[RECORD_UNION]] = [record_type for record_type in RECORD_UNION.__args__]

    RECORD_BUILDERS_UNION = SleepRecordBuilder | StepsRecordBuilder | BodyMetricRecordBuilder

    RECORD_BUILDERS: list[type[RECORD_BUILDERS_UNION]] = [
        record_builder for record_builder in RECORD_BUILDERS_UNION.__args__
    ]

    @staticmethod
    def get_template_payload_builder(type_id: str) -> type[TEMPLATE_PAYLOAD_BUILDERS_UNION]:
        for t in TypeResolver.TEMPLATE_PAYLOAD_BUILDERS:
            if t.type_id() == type_id:
                return t

        raise ShouldNotReachHereException(f"Template payload builder not found for type id: {type_id}")

    @staticmethod
    def get_template_payload(type_id: str) -> type[TEMPLATE_PAYLOADS_UNION]:
        for t in TypeResolver.TEMPLATE_PAYLOADS:
            if t.type_id() == type_id:
                return t

        raise ShouldNotReachHereException(f"Template payload not found for type id: {type_id}")

    @staticmethod
    def get_document(type_id: str) -> type[DOCUMENT_UNION]:
        for t in TypeResolver.DOCUMENTS:
            if type_id == t.type_id():
                return t

        raise ShouldNotReachHereException(f"Document type not found for type id: {type_id}")

    @staticmethod
    def get_record(type_id: str) -> type[RECORD_UNION]:
        for t in TypeResolver.RECORDS:
            if type_id == t.type_id():
                return t
        raise ShouldNotReachHereException(f"Record type not found for type id: {type_id}")

    @staticmethod
    def get_event(type_id: str) -> type[EVENT_V3_UNION]:
        for t in TypeResolver.EVENTS_V3:
            if type_id == t.type_id():
                return t

        raise ShouldNotReachHereException(f"Event type not found for type id: {type_id}")

    @staticmethod
    def get_event_v2(type_id: str) -> type[EVENT_V2_UNION]:
        for t in TypeResolver.EVENTS_V2:
            if type_id == t.type_id():
                return t

        raise ShouldNotReachHereException(f"Event v2 type not found for type id: {type_id}")

    @staticmethod
    def get_environment(type_id: str) -> type[ENVIRONMENT_UNION]:
        for t in TypeResolver.ENVIRONMENTS:
            if type_id == t.type_id():
                return t

        raise ShouldNotReachHereException(f"Environment type not found for type id: {type_id}")

    @staticmethod
    def get_record_builder(type_id: str) -> type[RECORD_BUILDERS_UNION]:
        for t in TypeResolver.RECORD_BUILDERS:
            if t.type_id() == type_id:
                return t

        raise ShouldNotReachHereException(f"Record builder not found for type id: {type_id}")

    @staticmethod
    def get_event_api_output(type_id: str) -> type[EVENT_API_OUTPUTS_UNION]:
        for t in TypeResolver.EVENT_API_OUTPUTS_UNION.__args__:
            if t.type_id() == type_id:
                return t

        raise ShouldNotReachHereException(f"Event api output not found for type id: {type_id}")

    @staticmethod
    def get_document_api_output(type_id: str) -> type[DOCUMENT_API_OUTPUTS_UNION]:
        for t in TypeResolver.DOCUMENT_API_OUTPUTS_UNION.__args__:
            if t.type_id() == type_id:
                return t

        raise ShouldNotReachHereException(f"document api output not found for type id: {type_id}")

    @staticmethod
    def get_record_api_output(type_id: str) -> type[RECORD_API_OUTPUTS_UNION]:
        for t in TypeResolver.RECORD_API_OUTPUTS_UNION.__args__:
            if t.type_id() == type_id:
                return t

        raise ShouldNotReachHereException(f"Record api output not found for type id: {type_id}")

    @staticmethod
    def get_event_api_output_v3(type_id: str) -> type[EVENT_API_OUTPUTS_V3_UNION]:
        for t in TypeResolver.EVENT_API_OUTPUTS_V3_UNION.__args__:
            if t.type_id() == type_id:
                return t

        raise ShouldNotReachHereException(f"Event api output v3 not found for type id: {type_id}")
