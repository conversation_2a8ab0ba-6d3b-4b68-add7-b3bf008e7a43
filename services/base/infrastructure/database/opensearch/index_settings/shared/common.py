from opensearchpy import Date, Double, Keyword, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import EventMetadataFields
from services.base.domain.schemas.events.event import EventFields, EventPlanExtensionFields, EventValueLimits
from services.base.domain.schemas.metadata import MetadataFields
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL


def get_common_mapping() -> dict:
    return {DocumentLabels.DOC_ID: Keyword()}


def get_system_properties_mapping() -> dict:
    return {
        DocumentLabels.SYSTEM_PROPERTIES: Object(
            properties={
                DocumentLabels.CREATED_AT: Date(),
                DocumentLabels.UPDATED_AT: Date(),
                DocumentLabels.DELETED_AT: Date(),
            }
        )
    }


def get_document_mapping() -> dict:
    return {
        OS_LABEL_CATCH_ALL: Text(),
        DocumentLabels.TYPE: Keyword(),
    } | get_system_properties_mapping()


def get_base_record_mapping() -> dict:
    return (
        get_document_mapping()
        | {
            DocumentLabels.SUBMISSION_ID: Keyword(),
            DocumentLabels.RBAC: Object(properties={DocumentLabels.OWNER_ID: Keyword()}),
            DocumentLabels.TIMESTAMP: Date(),
            DocumentLabels.DURATION: Double(),
            DocumentLabels.END_TIME: Date(),
            DocumentLabels.METADATA: Object(
                properties={
                    MetadataFields.SERVICE: Keyword(),
                    EventMetadataFields.ORIGIN: Keyword(),
                    EventMetadataFields.ORIGIN_DEVICE: Text(),
                    EventMetadataFields.SOURCE_SERVICE: Keyword(),
                    EventMetadataFields.SOURCE_OS: Keyword(),
                }
            ),
            DocumentLabels.TYPE: Keyword(copy_to=OS_LABEL_CATCH_ALL),
            DocumentLabels.CATEGORY: Keyword(copy_to=OS_LABEL_CATCH_ALL),
        }
        | get_system_properties_mapping()
    )


def get_node_mapping():
    return {
        EventFields.NODE: Keyword(copy_to=OS_LABEL_CATCH_ALL),
    }


def get_base_event_mapping() -> dict:
    return (
        get_document_mapping()
        | get_system_properties_mapping()
        | {
            EventFields.TIMESTAMP: Date(),
            EventFields.DURATION: Double(),
            EventFields.END_TIME: Date(),
            EventFields.NAME: Text(
                fields={"keyword": Keyword(ignore_above=EventValueLimits.MAX_NAME_LENGTH)}, copy_to=OS_LABEL_CATCH_ALL
            ),
            EventFields.CATEGORY: Keyword(copy_to=OS_LABEL_CATCH_ALL),
            DocumentLabels.TAGS: Object(
                properties={
                    DocumentLabels.TAG: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL)
                }
            ),
            EventFields.NOTE: Text(copy_to=OS_LABEL_CATCH_ALL),
            EventFields.TEMPLATE_ID: Keyword(),
            DocumentLabels.RBAC: Object(properties={DocumentLabels.OWNER_ID: Keyword()}),
            EventFields.GROUP_ID: Keyword(),
            EventFields.SUBMISSION_ID: Keyword(),
            EventFields.PLAN_EXTENSION: Object(properties={EventPlanExtensionFields.PLAN_ID: Keyword()}),
            EventFields.ASSET_REFERENCES: Object(
                properties={
                    DocumentLabels.ASSET_TYPE: Text(
                        fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL
                    ),
                    DocumentLabels.ASSET_ID: Text(
                        fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL
                    ),
                }
            ),
            DocumentLabels.METADATA: Object(
                properties={
                    MetadataFields.ORGANIZATION: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    MetadataFields.SERVICE: Keyword(),
                    MetadataFields.DATA_PROXY: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    EventMetadataFields.ORIGIN: Keyword(),
                    EventMetadataFields.ORIGIN_DEVICE: Text(),
                    EventMetadataFields.SOURCE_SERVICE: Keyword(),
                    EventMetadataFields.SOURCE_OS: Keyword(),
                }
            ),
        }
    )
