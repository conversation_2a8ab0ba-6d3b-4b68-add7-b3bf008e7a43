from typing import Any, Dict

from opensearchpy import Integer

from services.base.domain.schemas.events.activity import ActivityFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_event_mapping,
    get_node_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    ACTIVITY_INDEX,
    OpenSearchIndex,
)

activity_mapping = {
    ActivityFields.RATING: Integer(),
}


def get_activity_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        activity_mapping | get_node_mapping() | get_base_event_mapping(), strict_mapping=True
    )


def get_event_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": ACTIVITY_INDEX,
    }


ActivityIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=ACTIVITY_INDEX,
    mappings=get_activity_mapping(),
    settings=get_event_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[ACTIVITY_INDEX],
)
