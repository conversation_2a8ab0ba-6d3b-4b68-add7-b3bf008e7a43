# -*- coding: utf-8 -*-
import json
import logging
import os

from opensearchpy import OpenSearch

from services.base.infrastructure.database.opensearch.opensearch_mappings import DataTypeToIndexModelMapping
from settings.app_config import settings

INDICES_LABEL = "indices"
INDEX_TEMPLATES_LABEL = "index_templates"


def get_schema() -> dict:
    """Fetches schema of the indices in opensearch"""
    output_records = {INDICES_LABEL: {}, INDEX_TEMPLATES_LABEL: {}}
    for index_name in [model.name for model in DataTypeToIndexModelMapping.values()]:
        if os_client.indices.exists(index=index_name):
            try:
                output_records[INDICES_LABEL].update({index_name: os_client.indices.get(index=index_name)})
            except Exception as error:
                logging.exception(error)
            continue
        if os_client.indices.exists(index=f"{index_name}*"):
            try:
                output_records[INDEX_TEMPLATES_LABEL].update(
                    {
                        index_name: os_client.cluster.get_component_template(name=f"{index_name}_component_template")[
                            "component_templates"
                        ][0]
                    }
                )
            except Exception as error:
                logging.exception(error)

    return output_records


# Creates connection to OpenSearch cluster
os_client = OpenSearch(settings.OS_HOSTS, verify_certs=True, sniff_on_start=False)
# check if connection is valid
if not os_client.ping():
    raise ValueError("Connection failed")

# Get Indices data
data = get_schema()
path = os.path.dirname(os.path.realpath(__file__)) + "/os_schema.json"
print("Writing to path: ", path)
with open(path, "w", encoding="utf-8") as file:
    file.write(json.dumps(data, ensure_ascii=False, indent=2))
