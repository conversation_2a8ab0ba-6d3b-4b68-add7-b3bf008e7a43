import asyncio
import logging
from typing import get_args

from opensearchpy import Async<PERSON><PERSON>Search

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.user_document_type import UserDocumentType
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import update_by_query_async
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client

logger = logging.getLogger()
logger.setLevel(logging.INFO)

TARGET_DATA_TYPES = [DataType(doc_type.value) for doc_type in UserDocumentType]


async def run_migrations():
    for data_type in TARGET_DATA_TYPES:
        await run_migration(data_type=data_type)


async def run_migration(data_type: DataType):
    client = get_async_default_os_client()
    await add_nullable_fields(client=client, data_type=data_type)


async def add_nullable_fields(client: AsyncOpenSearch, data_type: DataType):
    logging.info(f"Adding nullable fields to {data_type.value}")

    domain_schema = data_type.to_domain_model()

    optional_fields = []
    for field_name, field in domain_schema.model_fields.items():
        if type(None) in get_args(field.annotation):
            optional_fields.append(field_name)
    query = {
        "script": {
            "source": """
                for (field in params.optional_fields) {
                    if (!ctx._source.containsKey(field)) {
                        ctx._source[field] = null;
                    }
                }
            """,
            "lang": "painless",
            "params": {"optional_fields": optional_fields},
        },
        "query": {
            "bool": {
                "must": [{"term": {"type": domain_schema.type_id()}}],
                "should": [{"bool": {"must_not": {"exists": {"field": field_name}}}} for field_name in optional_fields],
                "minimum_should_match": 1,
            }
        },
    }
    await update_by_query_async(client=client, data_type=data_type, query=query)
    logging.info(f"Added nullable fields to {data_type.value}")


if __name__ == "__main__":
    asyncio.run(run_migrations())
