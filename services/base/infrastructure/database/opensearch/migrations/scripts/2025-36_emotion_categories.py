import asyncio
import logging

from opensearchpy import AsyncOpenSearch

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.dependency_bootstrapper import bootstrapper
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.feeling.emotion import Emotion
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.infrastructure.database.opensearch.index_settings.feeling import FeelingIndexModel
from services.base.infrastructure.database.opensearch.index_settings.template import TemplateIndexModel
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import update_by_query_async

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)


async def run_migration(dry_run: bool):
    client = bootstrapper.get(interface=AsyncOpenSearch)
    search_service = bootstrapper.get(interface=DocumentSearchService)

    await migrate_emotions(client=client, search_service=search_service, dry_run=dry_run)
    await migrate_templates(client=client, search_service=search_service, dry_run=dry_run)
    await client.close()


async def migrate_templates(client: AsyncOpenSearch, search_service: DocumentSearchService, dry_run: bool):
    index_name = TemplateIndexModel.name

    query_to_migrate = {
        "bool": {
            "filter": [
                {"term": {"document.type": "emotion"}},
                {"term": {"document.category": "sad"}},
            ]
        }
    }
    count_to_migrate = (await client.count(index=index_name, body={"query": query_to_migrate}))["count"]
    if not count_to_migrate:
        logger.info("No emotion templates to migrate")
    elif dry_run:
        logger.info(f"[DRY RUN] would update {count_to_migrate} emotion templates")
    else:
        body = {
            "script": {
                "source": """
                    def cat = ctx._source.document.category;
    
                    if (cat == 'sad') {
                        ctx._source.document.category = 'sadness';
                    }
                """,
                "lang": "painless",
            },
            "query": query_to_migrate,
        }
        await update_by_query_async(client=client, data_type=DataType.EventTemplate, query=body)
        query_to_validate = {
            "bool": {
                "filter": [
                    {"term": {"document.type": "emotion"}},
                    {"term": {"document.category": "sadness"}},
                ]
            }
        }
        count_to_validate = (await client.count(index=index_name, body={"query": query_to_validate}))["count"]
        assert count_to_validate == count_to_migrate, f"Expected {count_to_migrate} documents, got {count_to_validate}"
        logger.info(f"Successfully migrated {count_to_migrate} emotion templates")

    continuation_token = None
    while True:
        search_result = await search_service.search_documents_by_query(
            query=Query(
                type_queries=[
                    TypeQuery(
                        domain_types=[EventTemplate],
                        query=ValuesQuery(field_name="document_type", values=["emotion"]),
                    )
                ]
            ),
            size=10000,
            continuation_token=continuation_token,
        )
        continuation_token = search_result.continuation_token
        if not continuation_token:
            break


async def migrate_emotions(client: AsyncOpenSearch, search_service: DocumentSearchService, dry_run: bool):
    index_name = FeelingIndexModel.name

    query_to_migrate = {
        "bool": {
            "filter": [
                {"term": {"type": "emotion"}},
                {"term": {"category": "sad"}},
            ]
        }
    }
    count_to_migrate = (await client.count(index=index_name, body={"query": query_to_migrate}))["count"]
    if not count_to_migrate:
        logger.info("No emotions to migrate")
    elif dry_run:
        logger.info(f"[DRY RUN] would update {count_to_migrate} emotions")
    else:
        body = {
            "script": {
                "source": """
                    def cat = ctx._source.category;
    
                    if (cat == 'sad') {
                        ctx._source.category = 'sadness';
                    }
                """,
                "lang": "painless",
            },
            "query": query_to_migrate,
        }
        await update_by_query_async(client=client, data_type=DataType.Emotion, query=body)
        query_to_validate = {
            "bool": {
                "filter": [
                    {"term": {"type": "emotion"}},
                    {"term": {"category": "sadness"}},
                ]
            }
        }
        count_to_validate = (await client.count(index=index_name, body={"query": query_to_validate}))["count"]
        assert count_to_validate == count_to_migrate, f"Expected {count_to_migrate} documents, got {count_to_validate}"
        logger.info(f"Successfully migrated {count_to_migrate} emotions")

    continuation_token = None
    while True:
        search_result = await search_service.search_documents_by_query(
            query=Query(
                type_queries=[
                    TypeQuery(
                        domain_types=[Emotion],
                        query=None,
                    )
                ]
            ),
            size=10000,
            continuation_token=continuation_token,
        )
        continuation_token = search_result.continuation_token
        if not continuation_token:
            break


if __name__ == "__main__":
    asyncio.run(run_migration(dry_run=True))
