from typing import Dict

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.air_quality import AirQuality
from services.base.domain.schemas.contact import Contact
from services.base.domain.schemas.events.activity import Activity
from services.base.domain.schemas.events.body_metric.blood_glucose import BloodGlucose
from services.base.domain.schemas.events.body_metric.blood_pressure import BloodPressure
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetric
from services.base.domain.schemas.events.content.content import Content
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.domain.schemas.events.exercise.cardio import Cardio
from services.base.domain.schemas.events.exercise.exercise import Exercise
from services.base.domain.schemas.events.exercise.strength import Strength
from services.base.domain.schemas.events.feeling.emotion import Emotion
from services.base.domain.schemas.events.feeling.stress import Stress
from services.base.domain.schemas.events.medication.medication import Medication
from services.base.domain.schemas.events.note import Note
from services.base.domain.schemas.events.nutrition.drink import Drink
from services.base.domain.schemas.events.nutrition.food import Food
from services.base.domain.schemas.events.nutrition.supplement import Supplement
from services.base.domain.schemas.events.person import Person
from services.base.domain.schemas.events.place_visit import PlaceVisit
from services.base.domain.schemas.events.sleep_v3 import SleepV3
from services.base.domain.schemas.events.symptom import Symptom
from services.base.domain.schemas.events.use_case import UseCase
from services.base.domain.schemas.extension_output import ExtensionResult, ExtensionRun
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.inbox.inbox_message import InboxMessage
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.place import Place
from services.base.domain.schemas.plan.plan import Plan
from services.base.domain.schemas.pollen import Pollen
from services.base.domain.schemas.records.body_metric_record import BodyMetricRecord
from services.base.domain.schemas.records.sleep_record import SleepRecord
from services.base.domain.schemas.records.steps_record import StepsRecord
from services.base.domain.schemas.resting_heart_rate import RestingHeartRate
from services.base.domain.schemas.shopping_activity import ShoppingActivity
from services.base.domain.schemas.sleep import Sleep
from services.base.domain.schemas.steps import Steps
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.usage_statistics_output import UsageStatistics
from services.base.domain.schemas.user_action_log import UserActionLog
from services.base.domain.schemas.weather import Weather
from services.base.infrastructure.database.opensearch.index_settings.activity import ActivityIndexModel
from services.base.infrastructure.database.opensearch.index_settings.air_quality import AirQualityIndexModel
from services.base.infrastructure.database.opensearch.index_settings.body_metric import BodyMetricIndexModel
from services.base.infrastructure.database.opensearch.index_settings.contact import ContactIndexModel
from services.base.infrastructure.database.opensearch.index_settings.content import ContentIndexModel
from services.base.infrastructure.database.opensearch.index_settings.event_group import EventGroupIndexModel
from services.base.infrastructure.database.opensearch.index_settings.exercise import ExerciseIndexModel
from services.base.infrastructure.database.opensearch.index_settings.extension import (
    ExtensionResultsIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.feeling import FeelingIndexModel
from services.base.infrastructure.database.opensearch.index_settings.heart_rate import HeartRateIndexModel
from services.base.infrastructure.database.opensearch.index_settings.inbox_message_index import InboxMessageIndexModel
from services.base.infrastructure.database.opensearch.index_settings.location import LocationIndexModel
from services.base.infrastructure.database.opensearch.index_settings.medication import MedicationIndexModel
from services.base.infrastructure.database.opensearch.index_settings.note import NoteIndexModel
from services.base.infrastructure.database.opensearch.index_settings.nutrition import NutritionIndexModel
from services.base.infrastructure.database.opensearch.index_settings.person import PersonIndexModel
from services.base.infrastructure.database.opensearch.index_settings.place import PlaceIndexModel
from services.base.infrastructure.database.opensearch.index_settings.place_visit import PlaceVisitIndexModel
from services.base.infrastructure.database.opensearch.index_settings.plan import PlanIndexModel
from services.base.infrastructure.database.opensearch.index_settings.pollen import PollenIndexModel
from services.base.infrastructure.database.opensearch.index_settings.records.numerical_record import (
    NumericalRecordIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.records.sleep_record import SleepRecordIndexModel
from services.base.infrastructure.database.opensearch.index_settings.resting_heart_rate import (
    RestingHeartRateIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.shopping_activity import ShoppingActivityIndexModel
from services.base.infrastructure.database.opensearch.index_settings.sleep import SleepIndexModel
from services.base.infrastructure.database.opensearch.index_settings.sleep_v3 import SleepV3IndexModel
from services.base.infrastructure.database.opensearch.index_settings.steps import StepsIndexModel
from services.base.infrastructure.database.opensearch.index_settings.symptom import SymptomIndexModel
from services.base.infrastructure.database.opensearch.index_settings.template import TemplateIndexModel
from services.base.infrastructure.database.opensearch.index_settings.usage_statistics_output import (
    UsageStatisticsIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.use_case import UseCaseIndexModel
from services.base.infrastructure.database.opensearch.index_settings.user_action_log import UserLogsIndexModel
from services.base.infrastructure.database.opensearch.index_settings.weather import WeatherIndexModel
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    AIR_QUALITY_INDEX,
    EVENT_PREFIX,
    FEELING_INDEX,
    HEART_RATE_INDEX,
    INBOX_MESSAGE_INDEX,
    LOCATION_HISTORY_INDEX,
    NUTRITION_INDEX,
    POLLEN_INDEX,
    RECORD_PREFIX,
    RESTING_HEART_RATE_INDEX,
    SHOPPING_ACTIVITY_INDEX,
    SLEEP_INDEX,
    STEPS_INDEX,
    USER_LOGS_INDEX,
    WEATHER_INDEX,
    OpenSearchIndex,
)

DataSchemaToIndexModelMapping: Dict[type[Document], OpenSearchIndex] = {
    # Content
    Content: ContentIndexModel,
    # Exercise
    Cardio: ExerciseIndexModel,
    Exercise: ExerciseIndexModel,
    Strength: ExerciseIndexModel,
    # Body Metrics
    BloodPressure: BodyMetricIndexModel,
    BloodGlucose: BodyMetricIndexModel,
    BodyMetric: BodyMetricIndexModel,
    # Feelings
    Emotion: FeelingIndexModel,
    Stress: FeelingIndexModel,
    # Nutrition
    Drink: NutritionIndexModel,
    Food: NutritionIndexModel,
    Supplement: NutritionIndexModel,
    # Core
    Activity: ActivityIndexModel,
    SleepV3: SleepV3IndexModel,
    EventGroup: EventGroupIndexModel,
    Note: NoteIndexModel,
    Symptom: SymptomIndexModel,
    Medication: MedicationIndexModel,
    Person: PersonIndexModel,
    PlaceVisit: PlaceVisitIndexModel,
    # Documents
    EventTemplate: TemplateIndexModel,
    GroupTemplate: TemplateIndexModel,
    Plan: PlanIndexModel,
    UseCase: UseCaseIndexModel,
    Contact: ContactIndexModel,
    Place: PlaceIndexModel,
    # Events
    HeartRate: HeartRateIndexModel,
    Location: LocationIndexModel,
    RestingHeartRate: RestingHeartRateIndexModel,
    ShoppingActivity: ShoppingActivityIndexModel,
    Sleep: SleepIndexModel,
    Steps: StepsIndexModel,
    # Records
    SleepRecord: SleepRecordIndexModel,
    StepsRecord: NumericalRecordIndexModel,
    BodyMetricRecord: NumericalRecordIndexModel,
    # Environment
    AirQuality: AirQualityIndexModel,
    Pollen: PollenIndexModel,
    Weather: WeatherIndexModel,
    # Other
    ExtensionRun: ExtensionResultsIndexModel,
    ExtensionResult: ExtensionResultsIndexModel,
    InboxMessage: InboxMessageIndexModel,
    UserActionLog: UserLogsIndexModel,
    UsageStatistics: UsageStatisticsIndexModel,
}

DataTypeToIndexModelMapping = {k.type_id(): v for k, v in DataSchemaToIndexModelMapping.items()}
NodeToIndexPatternMapping = {k: v.name for k, v in DataTypeToIndexModelMapping.items()} | {
    "nutrition": NUTRITION_INDEX,
    "feeling": FEELING_INDEX,
    "event": EVENT_PREFIX,
    "doc": "",
    "record": RECORD_PREFIX,
}

# TODO: Should be eventually removed entirely and replaced by type field
OpenSearchIndexToDocumentModelMapping: Dict[str, type[Document]] = {
    # EventsV2
    LOCATION_HISTORY_INDEX: Location,
    HEART_RATE_INDEX: HeartRate,
    RESTING_HEART_RATE_INDEX: RestingHeartRate,
    SHOPPING_ACTIVITY_INDEX: ShoppingActivity,
    SLEEP_INDEX: Sleep,
    STEPS_INDEX: Steps,
    # Environment
    AIR_QUALITY_INDEX: AirQuality,
    WEATHER_INDEX: Weather,
    POLLEN_INDEX: Pollen,
    # Other
    # ExtensionIndexModel: ExtensionRun,
    # ExtensionIndexModel: ExtensionResult,
    INBOX_MESSAGE_INDEX: InboxMessage,
    USER_LOGS_INDEX: UserActionLog,
}


def data_type_to_index_mapping_get(data_type: DataType) -> str:
    return DataTypeToIndexModelMapping[data_type].name
