import logging
from datetime import datetime, timezone
from typing import Optional, Sequence, cast
from uuid import UUID

from opensearchpy import Async<PERSON>penSearch, OpenSearchException
from pydantic import ValidationError

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.database.models.sorts import CommonSorts, Sort
from services.base.application.retry import retry
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.event import EventFields
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.templates.template import Template, TemplateFields
from services.base.domain.type_tree.type_tree import type_tree
from services.base.infrastructure.database.opensearch.opensearch_index_constants import TEMPLATE_INDEX
from services.base.infrastructure.database.opensearch.repository.os_response_parser import OSResponseParser


class OSTemplateRepository(TemplateRepository):

    def __init__(self, client: AsyncOpenSearch, search_service: DocumentSearchService):
        self._os_client = client
        self._search_service: DocumentSearchService = search_service

    async def insert(self, templates: Sequence[Template], force_strong_consistency: bool = False) -> Sequence[Template]:
        insert_requests: list[dict] = []

        for template in templates:
            action = {BulkOperation.Create.value: {"_index": TEMPLATE_INDEX, "_id": str(template.id)}}
            insert_requests.append(action)
            serialized = template.model_dump(exclude={DocumentLabels.ID}, by_alias=True) | {
                DocumentLabels.TAGS: [{DocumentLabels.TAG: tag} for tag in template.tags]
            }
            if isinstance(template, EventTemplate):
                if node := getattr(template.document, EventFields.NODE, None):
                    serialized[TemplateFields.DOCUMENT][EventFields.NODE] = node.split(".")
            insert_requests.append(serialized)

        refresh = "wait_for" if force_strong_consistency else "false"
        bulk_response = await self._os_client.bulk(body=insert_requests, refresh=refresh)  # pyright: ignore
        if bulk_response["errors"]:
            logging.error(f"Error inserting templates Response: {bulk_response}")
        ids = await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response,
            action=BulkOperation.Create,
        )

        return await self.search_by_id(ids=ids)

    async def update(self, templates: Sequence[Template]) -> Sequence[Template]:
        update_requests: list[dict] = []
        if not templates:
            return []
        for template in templates:
            template.system_properties.updated_at = datetime.now(timezone.utc)
            action = {
                BulkOperation.Update.value: {"_index": TEMPLATE_INDEX, "_id": str(template.id), "retry_on_conflict": 2}
            }
            update_requests.append(action)
            if isinstance(template, EventTemplate):
                serialized = template.model_dump(
                    exclude={
                        DocumentLabels.ID: True,
                        DocumentLabels.RBAC: {DocumentLabels.OWNER_ID},
                        DocumentLabels.SYSTEM_PROPERTIES: {DocumentLabels.CREATED_AT},
                    },
                    by_alias=True,
                )
                if node := getattr(template.document, EventFields.NODE, None):
                    serialized[TemplateFields.DOCUMENT][EventFields.NODE] = node.split(".")
            elif isinstance(template, GroupTemplate):
                serialized = template.model_dump(
                    exclude={
                        DocumentLabels.ID: True,
                        DocumentLabels.RBAC: {DocumentLabels.OWNER_ID},
                        DocumentLabels.SYSTEM_PROPERTIES: {DocumentLabels.CREATED_AT},
                    },
                    by_alias=True,
                )
            else:
                raise ShouldNotReachHereException(f"Unsupported template type: {type(template)}")

            request = {"doc": serialized | {DocumentLabels.TAGS: [{DocumentLabels.TAG: tag} for tag in template.tags]}}
            update_requests.append(request)

        bulk_response = await self._os_client.bulk(body=update_requests)
        if bulk_response["errors"]:
            logging.error(f"Error updating templates Response: {bulk_response}")
        ids = await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response, action=BulkOperation.Update
        )
        return await self.search_by_id(ids=ids)

    @retry(exceptions=OpenSearchException)
    async def search_by_id(self, ids: Sequence[UUID]) -> Sequence[Template]:
        response = await self._os_client.mget(body={"ids": ids}, index=TEMPLATE_INDEX)
        results: list[Template] = []
        for doc in response["docs"]:
            if err := doc.get("error"):
                logging.error(f"Mget search error: {err["reason"]}, id: {doc["_id"]}")
            if doc["found"]:
                source = doc["_source"]
                tags = source[DocumentLabels.TAGS]
                source |= {DocumentLabels.TAGS: [tag[DocumentLabels.TAG] for tag in tags]}
                try:
                    if source.get(TemplateFields.TYPE) == DataType.EventTemplate:
                        if source[TemplateFields.DOCUMENT].get(EventFields.NODE):
                            if node := source[TemplateFields.DOCUMENT].get(EventFields.NODE):
                                source[TemplateFields.DOCUMENT][EventFields.NODE] = ".".join(node)
                            else:
                                source[TemplateFields.DOCUMENT][EventFields.NODE] = (
                                    type_tree.data_type_category_to_enum(
                                        DataType(source[TemplateFields.DOCUMENT][DocumentLabels.TYPE]),
                                        source[EventFields.CATEGORY],
                                    )
                                )
                        model = EventTemplate(**source, id=doc["_id"])
                    elif source.get(TemplateFields.TYPE) == DataType.GroupTemplate:
                        model = GroupTemplate(**source, id=doc["_id"])
                    else:
                        raise ShouldNotReachHereException(
                            f"Unsupported template type: {source.get(TemplateFields.TYPE)}"
                        )
                    results.append(model)
                except ValidationError as err:
                    logging.error(
                        "failed to deserialize template",
                        extra={
                            "document_id": doc["_id"],
                            "index": doc["_index"],
                            "source": source,
                            "error": str(err),
                        },
                    )
        return results

    @retry(exceptions=OpenSearchException)
    async def search_by_query(
        self,
        query: Query,
        size: int,
        sorts: Optional[Sequence[Sort]] = None,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[Template]:
        sorts = sorts if sorts else CommonSorts.created_at_and_internal_id()
        results: SearchResults[Document] = await self._search_service.search_documents_by_query(
            query=query, sorts=sorts, size=size, continuation_token=continuation_token
        )
        return cast(SearchResults[Template], results)  # Does not cast anything at runtime, it's only a type check hint

    @retry(exceptions=OpenSearchException)
    async def delete_by_id(self, ids: Sequence[UUID]) -> Sequence[UUID]:
        delete_requests: list[dict] = []
        for template_uuid in ids:
            request = {BulkOperation.Delete.value: {"_index": TEMPLATE_INDEX, "_id": str(template_uuid)}}

            delete_requests.append(request)

        delete_result = await self._os_client.bulk(body=delete_requests)
        return await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=delete_result, action=BulkOperation.Delete
        )
