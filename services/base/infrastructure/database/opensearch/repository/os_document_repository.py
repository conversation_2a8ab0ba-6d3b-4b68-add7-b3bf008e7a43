import json
import logging

from opensearchpy import Async<PERSON>penSearch

from services.base.domain.repository.document_repository import DocumentRepository
from services.base.domain.schemas.query.query import Query
from services.base.infrastructure.database.opensearch.query_translator.query_translator import QueryTranslator


class OSDocumentRepository(DocumentRepository):
    def __init__(self, client: AsyncOpenSearch):
        self._os_client = client

    async def delete_by_query(self, query: Query) -> str:
        q = QueryTranslator.translate(query=query)
        logging.info(f"deleting with query: {json.dumps(q.query_as_dict)}")
        result = await self._os_client.delete_by_query(
            index=q.indices,
            body=json.dumps(q.query_as_dict),
            wait_for_completion=False,  # type: ignore
            wait_for_active_shards="all",  # type: ignore
        )
        return result["task"]
