from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.type_tree.activity_node import ActivityNode
from services.base.domain.schemas.events.activity import ActivityCategory, ActivityFields, ActivityIdentifier
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.templates.payload.template_payload_base import TemplatePayloadBase


class ActivityTemplatePayload(TemplatePayloadBase, ActivityIdentifier):
    type: Literal[DataType.Activity] = Field(alias=ActivityFields.TYPE)
    node: ActivityNode = Field(alias=ActivityFields.NODE)
    category: ActivityCategory = Field(alias=ActivityFields.CATEGORY)
    rating: int | None = Field(
        alias=ActivityFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
