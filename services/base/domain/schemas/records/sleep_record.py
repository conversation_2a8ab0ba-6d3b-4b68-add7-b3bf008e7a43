from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.records.record import Record


class SleepRecordCategory(StrEnum):
    OTHER = auto()


class SleepRecordFields:
    STAGE = "stage"
    TYPE = DocumentLabels.TYPE
    METADATA = DocumentLabels.METADATA
    CATEGORY = DocumentLabels.CATEGORY


class SleepStageType(StrEnum):
    UNKNOWN = auto()
    AWAKE = auto()
    SLEEPING = auto()  # The user is asleep but the particular stage of sleep (light, deep or REM) is unknown.
    AWAKE_OUT_OF_BED = auto()
    N1N2 = auto()  # Light stage, Apple label this as Core stage
    N3 = auto()  # Deep stage
    REM = auto()
    AWAKE_IN_BED = auto()


class SleepRecordIdentifier(TypeIdentifier):
    @classmethod
    def type_id(cls) -> DataType:
        return DataType.SleepRecord


class SleepRecord(Record, SleepRecordIdentifier):
    type: Literal[DataType.SleepRecord] = Field(alias=SleepRecordFields.TYPE)
    stage: SleepStageType = Field(alias=SleepRecordFields.STAGE)
    category: SleepRecordCategory = Field(alias=SleepRecordFields.CATEGORY)
