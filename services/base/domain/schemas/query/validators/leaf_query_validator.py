from typing import Sequence

from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.leaf_query import (
    ExistsQuery,
    <PERSON><PERSON><PERSON>y,
    <PERSON><PERSON><PERSON>uery,
    Radius<PERSON>uery,
    RangeQuery,
    ValuesQuery,
)
from services.base.domain.schemas.query.validators.field_validator import FieldValidator
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException
from services.base.domain.type_tree.type_tree import TreeNode


class LeafQueryValidator:

    @staticmethod
    def validate(leaf_query: LeafQuery, domain_types: Sequence[type[Document]]):
        if isinstance(leaf_query, (ValuesQuery, RangeQuery, ExistsQuery, RadiusQuery)):
            field_names = [leaf_query.field_name]
        elif isinstance(leaf_query, PatternQuery):
            field_names = leaf_query.field_names
        else:
            raise QueryValidationException(f"Unsupported leaf query type: {type(leaf_query)}")
        for domain_type in domain_types:
            FieldValidator.validate_fields(field_names=field_names, domain_type=domain_type)

    @staticmethod
    def validate_nodes(leaf_query: LeafQuery, nodes: Sequence[TreeNode]):
        if isinstance(leaf_query, (ValuesQuery, RangeQuery, ExistsQuery, RadiusQuery)):
            field_names = [leaf_query.field_name]
        elif isinstance(leaf_query, PatternQuery):
            field_names = leaf_query.field_names
        else:
            raise QueryValidationException(f"Unsupported leaf query type: {type(leaf_query)}")
        FieldValidator.validate_nodes_fields(field_names=field_names, nodes=nodes)
