from collections.abc import Sequence as ColSequence
from decimal import <PERSON>ima<PERSON>
from types import NoneType, UnionType
from typing import Sequence, Union, get_args, get_origin, get_type_hints

from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNot<PERSON><PERSON>HereException
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.type_tree.type_tree import TreeNode, type_tree


class FieldValidator:
    @staticmethod
    def validate_fields(field_names: Sequence[str], domain_type: type[Document]):
        type_mapping = FieldValidator.get_type_properties(type=domain_type)
        for field_name in field_names:
            FieldValidator.validate_field(field_name=field_name, type_mapping=type_mapping)

    @staticmethod
    def validate_nodes_fields(field_names: Sequence[str], nodes: Sequence[TreeNode]):
        dts = set.union(*(type_tree.get_node_data_types(node) for node in nodes))

        type_mappings = [FieldValidator.get_type_properties(data_type.to_domain_model()) for data_type in dts]

        common_keys = set.intersection(*(set(m.keys()) for m in type_mappings))
        combined_type_mapping = {k: type_mappings[0][k] for k in common_keys}

        for field_name in field_names:
            FieldValidator.validate_field(field_name=field_name, type_mapping=combined_type_mapping)

    @staticmethod
    def validate_field(field_name: str, type_mapping: dict, must_be_numeric: bool = False):
        parsed_field_names = field_name.split(".")
        current_field_name = parsed_field_names[0]

        if current_field_name not in type_mapping.keys():
            raise QueryValidationException(
                f"Field name {current_field_name} does not exist. Known values are: {list(type_mapping)}"
            )

        if len(parsed_field_names) > 1:
            new_field_name = ".".join(parsed_field_names[1:])
            new_field_type = FieldValidator._resolve_nested_field_type(
                field_type=type_mapping[current_field_name], field_name=current_field_name
            )
            new_type_mapping = FieldValidator.get_type_properties(type=new_field_type)
            FieldValidator.validate_field(field_name=new_field_name, type_mapping=new_type_mapping)
        else:
            field_type = type_mapping[current_field_name]
            if must_be_numeric:
                field_type = (
                    FieldValidator._get_field_type_from_union(field_types=get_args(field_type))
                    if get_origin(field_type) in (Union, UnionType)
                    else field_type
                )
                if not FieldValidator._is_numeric_type(field_type=field_type):
                    raise QueryValidationException(f"Field {field_name} is not numeric. Type given: {field_type}")

    # TODO: move elsewhere
    @staticmethod
    def get_type_properties(type: type[BaseDataModel]) -> dict:
        type_hints = get_type_hints(type)
        computed_props = {k: v.return_type for k, v in type.model_computed_fields.items()}
        return type_hints | computed_props

    @staticmethod
    def _resolve_nested_field_type(field_type: type[BaseDataModel], field_name: str) -> type[BaseDataModel]:
        # If the field type contains more than one type (is Optional for example) we need to retrieve subclass
        # of BaseDataModel. We expect the field types to be either NoneType or BaseDataModel.
        # Additionally, we expect that there should be only two field types, therefore [NoneType | BaseDataType]
        if get_origin(field_type) in (Union, UnionType):
            field_types = get_args(field_type)
            FieldValidator._validate_union_field_types(field_types=field_types)
            return FieldValidator._get_field_type_from_union(field_types=field_types)
        if get_origin(field_type) is list:
            return FieldValidator._resolve_nested_field_type(field_type=get_args(field_type)[0], field_name=field_name)

        # If the field type is subclass of BaseDataModel then we can directly return it
        if issubclass(field_type, BaseDataModel):
            return field_type

        raise ShouldNotReachHereException(f"Field {field_name} is not nestable type. Type given: {field_type}")

    @staticmethod
    def _validate_union_field_types(field_types) -> None:
        # We expect here only NoneType, List or any subclass of BaseDataModel
        for field_type in field_types:
            if (
                field_type is not NoneType
                and not FieldValidator._is_list_type(field_type)
                and not issubclass(field_type, BaseDataModel)
            ):
                raise ShouldNotReachHereException(f"Unexpected field type. Type given: {field_type}")

    @staticmethod
    def _get_field_type_from_union(field_types) -> type[BaseDataModel]:
        filtered_field_types = [ft for ft in field_types if ft is not NoneType]
        if len(filtered_field_types) == 1:
            field_type = filtered_field_types[0]
            if FieldValidator._is_list_type(field_type=field_type):
                return get_args(field_type)[0]
            else:
                return field_type
        raise ShouldNotReachHereException(f"Unexpected field types in union. Types given: {field_types}")

    @staticmethod
    def _is_list_type(field_type: type) -> bool:
        origin = get_origin(field_type)
        return origin in (list, ColSequence)

    @staticmethod
    def _is_numeric_type(field_type: type) -> bool:
        return field_type in (int, float, Decimal)
