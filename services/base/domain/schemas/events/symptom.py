from dataclasses import dataclass
from enum import StrEnum, auto
from typing import Literal, Sequence

from pydantic import Field

from services.base.domain.enums.body_location import BodyParts
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class SymptomValueLimits:
    SYMPTOM_RATING_MINIMUM_VALUE = 0
    SYMPTOM_RATING_MAXIMUM_VALUE = 10


@dataclass(frozen=True)
class SymptomFields(EventFields):
    RATING = "rating"
    BODY_PARTS = "body_parts"


class SymptomCategory(StrEnum):
    PAIN_AND_SENSATIONS_PAIN = auto()
    PAIN_AND_SENSATIONS_ITCHING = auto()
    PAIN_AND_SENSATIONS_TINGLING = auto()
    PAIN_AND_SENSATIONS_NUMBNESS = auto()
    PAIN_AND_SENSATIONS_WARMTH = auto()
    PAIN_AND_SENSATIONS_TENDERNESS = auto()
    PAIN_AND_SENSATIONS_CRAWLING = auto()
    PAIN_AND_SENSATIONS_HEADACHE = auto()
    PAIN_AND_SENSATIONS_GENITAL_ITCHING = auto()
    PAIN_AND_SENSATIONS_BURNING_SENSATION = auto()
    PAIN_AND_SENSATIONS_MIGRAINE = auto()
    PAIN_AND_SENSATIONS_OTHER = auto()
    MOTOR_AND_MUSCLE_FINE_MOTOR_DIFFICULTY = auto()
    MOTOR_AND_MUSCLE_JERKING = auto()
    MOTOR_AND_MUSCLE_CRAMPING = auto()
    MOTOR_AND_MUSCLE_MUSCLE_FATIGUE = auto()
    MOTOR_AND_MUSCLE_MUSCLE_SPASM = auto()
    MOTOR_AND_MUSCLE_MUSCLE_TENSION = auto()
    MOTOR_AND_MUSCLE_PARALYSIS = auto()
    MOTOR_AND_MUSCLE_SHAKING = auto()
    MOTOR_AND_MUSCLE_TREMORS = auto()
    MOTOR_AND_MUSCLE_WEAKNESS = auto()
    MOTOR_AND_MUSCLE_CLICKING = auto()
    MOTOR_AND_MUSCLE_LOCKING = auto()
    MOTOR_AND_MUSCLE_SORENESS = auto()
    MOTOR_AND_MUSCLE_STIFFNESS = auto()
    MOTOR_AND_MUSCLE_OTHER = auto()
    SKIN_AND_TISSUE_LUMP = auto()
    SKIN_AND_TISSUE_RASH = auto()
    SKIN_AND_TISSUE_REDNESS = auto()
    SKIN_AND_TISSUE_SWELLING = auto()
    SKIN_AND_TISSUE_EXCESSIVE_SWEATING = auto()
    SKIN_AND_TISSUE_WET = auto()
    SKIN_AND_TISSUE_ACNE = auto()
    SKIN_AND_TISSUE_BLISTER = auto()
    SKIN_AND_TISSUE_BRUISING = auto()
    SKIN_AND_TISSUE_HAIR_GROWTH = auto()
    SKIN_AND_TISSUE_HAIR_LOSS = auto()
    SKIN_AND_TISSUE_HIVES = auto()
    SKIN_AND_TISSUE_ROUGH = auto()
    SKIN_AND_TISSUE_YELLOWING = auto()
    SKIN_AND_TISSUE_DISCHARGE = auto()
    SKIN_AND_TISSUE_DRY_SKIN = auto()
    SKIN_AND_TISSUE_BLEEDING = auto()
    SKIN_AND_TISSUE_NIGHT_SWEATS = auto()
    SKIN_AND_TISSUE_PALE_SKIN = auto()
    SKIN_AND_TISSUE_SKIN_PEELING = auto()
    SKIN_AND_TISSUE_SWOLLEN_GLANDS = auto()
    SKIN_AND_TISSUE_OTHER = auto()
    SYSTEMIC_FATIGUE = auto()
    SYSTEMIC_FEVER = auto()
    SYSTEMIC_CHILLS = auto()
    SYSTEMIC_LOSS_OF_APPETITE = auto()
    SYSTEMIC_OTHER = auto()
    RESPIRATORY_SHORTNESS_OF_BREATH = auto()
    RESPIRATORY_COUGH = auto()
    RESPIRATORY_WHEEZING = auto()
    RESPIRATORY_RUNNY_NOSE = auto()
    RESPIRATORY_STUFFY_NOSE = auto()
    RESPIRATORY_LOSS_OF_SMELL = auto()
    RESPIRATORY_APNEA = auto()
    RESPIRATORY_SNEEZING = auto()
    RESPIRATORY_CHEST_TIGHTNESS = auto()
    RESPIRATORY_OTHER = auto()
    CIRCULATORY_FAST_HEART_RHYTHM = auto()
    CIRCULATORY_SLOW_HEART_RHYTHM = auto()
    CIRCULATORY_IRREGULAR_HEART_RHYTHM = auto()
    CIRCULATORY_POUNDING_HEART = auto()
    CIRCULATORY_PALPITATIONS = auto()
    CIRCULATORY_FEELING_FAINT = auto()
    CIRCULATORY_COLD_HANDS_FEET = auto()
    CIRCULATORY_OTHER = auto()
    DIGESTIVE_BLOATING = auto()
    DIGESTIVE_CONSTIPATION = auto()
    DIGESTIVE_DIARRHEA = auto()
    DIGESTIVE_FLATULENCE = auto()
    DIGESTIVE_HEARTBURN = auto()
    DIGESTIVE_VOMITING = auto()
    DIGESTIVE_ABDOMINAL_PAIN = auto()
    DIGESTIVE_DIFFICULTY_SWALLOWING = auto()
    DIGESTIVE_NAUSEA = auto()
    DIGESTIVE_LOSS_OF_TASTE = auto()
    DIGESTIVE_EXCESSIVE_THIRST = auto()
    DIGESTIVE_DRY_MOUTH = auto()
    DIGESTIVE_BAD_BREATH = auto()
    DIGESTIVE_OTHER = auto()
    NEUROLOGICAL_SEIZURES = auto()
    NEUROLOGICAL_FAINTING = auto()
    NEUROLOGICAL_DIZZINESS = auto()
    NEUROLOGICAL_VERTIGO = auto()
    NEUROLOGICAL_CLUMSINESS = auto()
    NEUROLOGICAL_BALANCE_ISSUES = auto()
    NEUROLOGICAL_OTHER = auto()
    VISION_BLURRY_VISION = auto()
    VISION_DOUBLE_VISION = auto()
    VISION_FLOATERS = auto()
    VISION_HALOS = auto()
    VISION_LIGHT_SENSITIVITY = auto()
    VISION_SPOTS = auto()
    VISION_WATERY_EYES = auto()
    VISION_DRYNESS = auto()
    VISION_OTHER = auto()
    HEARING_MUFFLED = auto()
    HEARING_LOSS = auto()
    HEARING_RINGING_IN_EARS = auto()
    HEARING_SENSITIVITY_TO_SOUND = auto()
    HEARING_OTHER = auto()
    SPEECH_AND_VOICE_SLURRED_SPEECH = auto()
    SPEECH_AND_VOICE_HOARSE_VOICE = auto()
    SPEECH_AND_VOICE_LOSS_OF_VOICE = auto()
    SPEECH_AND_VOICE_OTHER = auto()
    SLEEP_VIVID_DREAMING = auto()
    SLEEP_DIFFICULTY_SLEEPING = auto()
    SLEEP_SLEEPWALKING = auto()
    SLEEP_SNORING = auto()
    SLEEP_INSOMNIA = auto()
    SLEEP_EXCESSIVE_SLEEPINESS = auto()
    SLEEP_OTHER = auto()
    URINARY_HESITANCY = auto()
    URINARY_INTERMITTENT_FLOW = auto()
    URINARY_LEAKING = auto()
    URINARY_SPRAYING = auto()
    URINARY_URGENCY = auto()
    URINARY_WEAK_STREAM = auto()
    URINARY_DISCOLORED_URINE = auto()
    URINARY_PAINFUL_URINATION = auto()
    URINARY_FREQUENT_URINATION = auto()
    URINARY_BLOOD_IN_URINE = auto()
    URINARY_INCONTINENCE = auto()
    URINARY_OTHER = auto()
    VAGINAL_BLEEDING = auto()
    VAGINAL_CLEAR_DISCHARGE = auto()
    VAGINAL_CLOUDY_DISCHARGE = auto()
    VAGINAL_STICKY_DISCHARGE = auto()
    VAGINAL_DRYNESS = auto()
    VAGINAL_IRRITATION = auto()
    VAGINAL_ITCHING = auto()
    VAGINAL_LOSS_OF_SENSATION = auto()
    VAGINAL_ODOR = auto()
    VAGINAL_VAGINAL_DISCHARGE = auto()
    VAGINAL_OTHER = auto()
    MENSTRUAL_CRAMP = auto()
    MENSTRUAL_CLOTS = auto()
    MENSTRUAL_HEAVY_FLOW = auto()
    MENSTRUAL_SPOTTING = auto()
    MENSTRUAL_OVULATION_PAIN = auto()
    MENSTRUAL_MENSTRUAL_PAIN = auto()
    MENSTRUAL_HOT_FLASHES = auto()
    MENSTRUAL_OTHER = auto()
    PREGNANCY_CONTRACTIONS = auto()
    PREGNANCY_WATER_BREAKING = auto()
    PREGNANCY_BRAXTON_HICKS_CONTRACTIONS = auto()
    PREGNANCY_BLOODY_SHOW = auto()
    PREGNANCY_CERVICAL_EFFACEMENT = auto()
    PREGNANCY_OTHER = auto()
    SEXUAL_FUNCTION_DELAYED_ORGASM = auto()
    SEXUAL_FUNCTION_INABILITY_TO_ORGASM = auto()
    SEXUAL_FUNCTION_PAINFUL_ORGASM = auto()
    SEXUAL_FUNCTION_LACK_OF_AROUSAL = auto()
    SEXUAL_FUNCTION_PAINFUL_INTERCOURSE = auto()
    SEXUAL_FUNCTION_PREMATURE_ORGASM = auto()
    SEXUAL_FUNCTION_OTHER = auto()
    MENTAL_BEHAVIORAL_AVOIDANCE = auto()
    MENTAL_BEHAVIORAL_CRAVING = auto()
    MENTAL_BEHAVIORAL_LOSS_OF_INTEREST = auto()
    MENTAL_BEHAVIORAL_FIDGETING = auto()
    MENTAL_BEHAVIORAL_PACING = auto()
    MENTAL_BEHAVIORAL_ANXIETY = auto()
    MENTAL_BEHAVIORAL_DEPRESSION = auto()
    MENTAL_BEHAVIORAL_IRRITABILITY = auto()
    MENTAL_BEHAVIORAL_OTHER = auto()
    MENTAL_COGNITIVE_BRAIN_FOG = auto()
    MENTAL_COGNITIVE_DIFFICULTY_CONCENTRATING = auto()
    MENTAL_COGNITIVE_CONFUSION = auto()
    MENTAL_COGNITIVE_FLASHBACKS = auto()
    MENTAL_COGNITIVE_RACING_THOUGHTS = auto()
    MENTAL_COGNITIVE_RUMINATING_THOUGHTS = auto()
    MENTAL_COGNITIVE_INTRUSIVE_THOUGHTS = auto()
    MENTAL_COGNITIVE_HALLUCINATIONS = auto()
    MENTAL_COGNITIVE_MEMORY_LOSS = auto()
    MENTAL_COGNITIVE_OTHER = auto()


class SymptomIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Symptom


class Symptom(Event, SymptomIdentifier):
    type: Literal[DataType.Symptom] = Field(alias=SymptomFields.TYPE)
    category: SymptomCategory = Field(alias=SymptomFields.CATEGORY)
    rating: int | None = Field(
        alias=SymptomFields.RATING,
        ge=SymptomValueLimits.SYMPTOM_RATING_MINIMUM_VALUE,
        le=SymptomValueLimits.SYMPTOM_RATING_MAXIMUM_VALUE,
    )
    body_parts: Sequence[BodyParts] = Field(alias=SymptomFields.BODY_PARTS)
