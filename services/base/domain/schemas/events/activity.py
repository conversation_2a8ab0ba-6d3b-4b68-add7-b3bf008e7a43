from dataclasses import dataclass
from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.type_tree.activity_node import ActivityNode
from services.base.domain.schemas.events.event import Event, EventFields, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class ActivityFields(EventFields):
    RATING = "rating"


class ActivityCategory(StrEnum):
    ACTIVITY = auto()
    APP = auto()
    APP_PRODUCTIVITY = auto()
    APP_SOCIAL = auto()
    APP_GAME = auto()
    APP_FITNESS = auto()
    APP_MUSIC = auto()
    APP_EDUCATION = auto()
    APP_PHOTOGRAPHY = auto()
    APP_ENTERTAINMENT = auto()
    APP_SHOPPING = auto()
    APP_UTILITY = auto()
    APP_COMMUNICATION = auto()
    APP_LIFESTYLE = auto()
    APP_FINANCE = auto()
    APP_TRAVEL = auto()
    APP_NEWS = auto()
    APP_BUSINESS = auto()
    APP_INTERACTIVE_AUGMENTED_REALITY = auto()
    APP_INTERACTIVE_QUIZ = auto()
    APP_INTERACTIVE_VIRTUAL_REALITY = auto()
    APP_INTERACTIVE_GAME = auto()
    BABY_CARE = auto()
    CLEAN = auto()
    CREATE = auto()
    FINANCE = auto()
    GARDEN = auto()
    LAUNDRY = auto()
    LAWN_MAINTENANCE = auto()
    MEAL = auto()
    MEAL_PREPARATION = auto()
    PARENT = auto()
    PET_CARE = auto()
    PLAN = auto()
    HOME_CARE = auto()
    CAR_CARE = auto()
    INTERACTIVE_BOARD_GAME = auto()
    INTERACTIVE_CARD_GAME = auto()
    INTERACTIVE_PUZZLE = auto()
    INTERACTIVE = auto()
    STUDY = auto()
    TECHNOLOGY = auto()
    VOLUNTEER = auto()
    IDLE_DOWNTIME = auto()
    IDLE_NAP = auto()
    IDLE_WAIT = auto()
    IDLE = auto()
    INTIMACY_PARTNER = auto()
    INTIMACY_SELF = auto()
    INTIMACY_SPIRITUAL = auto()
    INTIMACY = auto()
    SERVICE_BEAUTY = auto()
    SERVICE_DOCTOR = auto()
    SERVICE_EDUCATION = auto()
    SERVICE_FINANCE = auto()
    SERVICE_MENTAL_HEALTH = auto()
    SERVICE_PHYSICAL_HEALTH = auto()
    SERVICE_PROPERTY = auto()
    SERVICE_VETERINARIAN = auto()
    SERVICE = auto()
    SOCIAL_ASSIST = auto()
    SOCIAL_GROUP = auto()
    SOCIAL_INDIVIDUAL = auto()
    SOCIAL_REMOTE = auto()
    SOCIAL = auto()
    TRAVEL_BIKE = auto()
    TRAVEL_BOAT = auto()
    TRAVEL_DRIVE = auto()
    TRAVEL_FLY = auto()
    TRAVEL_WALK = auto()
    TRAVEL = auto()
    WORK_MENTOR = auto()
    WORK_PRIVATE_MEETING = auto()
    WORK_GROUP_MEETING = auto()
    WORK_NETWORK = auto()
    WORK_PRIMARY_WORK = auto()
    WORK_PROFESSIONAL_DEVELOPMENT = auto()
    WORK_SUPPLEMENTAL_WORK = auto()
    WORK = auto()


class ActivityIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Activity


class Activity(Event, ActivityIdentifier):
    type: Literal[DataType.Activity] = Field(alias=ActivityFields.TYPE)
    category: ActivityCategory = Field(
        alias=ActivityFields.CATEGORY,
    )
    node: ActivityNode = Field(alias=ActivityFields.NODE)
    rating: int | None = Field(
        alias=ActivityFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
