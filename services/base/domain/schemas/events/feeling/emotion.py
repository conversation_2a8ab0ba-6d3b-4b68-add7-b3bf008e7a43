from dataclasses import dataclass
from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class EmotionFields(EventFields):
    RATING = "rating"


class EmotionCategory(StrEnum):
    EMOTION = DataType.Emotion
    JOY = auto()
    SADNESS = auto()
    FEAR = auto()
    DISGUST = auto()
    ANGER = auto()
    SURPRISE = auto()
    ANTICIPATION = auto()
    TRUST = auto()
    ANXIETY = auto()
    MOOD = auto()
    CALMNESS = auto()
    CONTENTMENT = auto()
    EXCITEMENT = auto()
    GRATITUDE = auto()
    APATHY = auto()
    FRUSTRATION = auto()
    GUILT = auto()
    HOPELESSNESS = auto()
    LONELINESS = auto()
    OVERWHELMED = auto()
    SHAME = auto()
    DETACHMENT = auto()


class EmotionIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Emotion


class Emotion(Event, EmotionIdentifier):
    type: Literal[DataType.Emotion] = Field(alias=EmotionFields.TYPE)
    category: EmotionCategory = Field(alias=EmotionFields.CATEGORY)
    rating: int = Field(
        alias=EmotionFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
