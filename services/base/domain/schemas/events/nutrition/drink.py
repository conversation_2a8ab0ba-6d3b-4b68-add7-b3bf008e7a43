from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionCollection, NutritionFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class DrinkCategory(StrEnum):
    DRINK = auto()
    TEA = auto()
    MILK = auto()
    WINE = auto()
    BEER = auto()
    WATER = auto()
    COFFEE = auto()
    SMOOTHIE = auto()
    SPIRITS = auto()
    PLANT_BASED = auto()
    SOFT_DRINKS = auto()
    ENERGY_DRINKS = auto()
    PROTEIN_SHAKE = auto()
    JUICES_FROM_FRUIT = auto()
    JUICES_FROM_VEGETABLE = auto()


class DrinkIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Drink


class Drink(NutritionCollection, Event, DrinkIdentifier):
    type: Literal[DataType.Drink] = Field(alias=NutritionFields.TYPE)
    category: DrinkCategory = Field(alias=NutritionFields.CATEGORY)
    consumed_type: VolumeUnit | Literal["item", "serving"] = Field(alias=NutritionFields.CONSUMED_TYPE)
