from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionCollection, NutritionFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class SupplementCategory(StrEnum):
    Supplement = auto()
    SLEEP = auto()
    HEART_HEALTH = auto()
    JOINT_HEALTH = auto()
    MENTAL_HEALTH = auto()
    IMMUNE_SUPPORT = auto()
    DIGESTIVE_HEALTH = auto()
    DAILY_ESSENTIALS = auto()
    WEIGHT_MANAGEMENT = auto()
    COGNITIVE_FUNCTION = auto()
    BONE_AND_MUSCLE_HEALTH = auto()
    ATHLETIC_PERFORMANCE = auto()


class SupplementIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Supplement


class Supplement(NutritionCollection, Event, SupplementIdentifier):
    type: Literal[DataType.Supplement] = Field(alias=NutritionFields.TYPE)
    category: SupplementCategory = Field(alias=NutritionFields.CATEGORY)
    consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"] = Field(alias=NutritionFields.CONSUMED_TYPE)
