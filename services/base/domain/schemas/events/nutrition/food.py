from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionCollection, NutritionFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class FoodCategory(StrEnum):
    FOOD = auto()
    MEAT = auto()
    FISH = auto()
    EGGS = auto()
    DAIRY = auto()
    FRUITS = auto()
    POULTRY = auto()
    SEAFOOD = auto()
    LEGUMES = auto()
    READY_MEAL = auto()
    VEGETABLES = auto()
    WHOLE_GRAIN = auto()
    FATS_AND_OILS = auto()
    HOMEMADE_MEAL = auto()
    FAST_FOOD_MEAL = auto()
    REFINED_GRAINS = auto()
    RESTAURANT_MEAL = auto()
    SWEETS_AND_DESSERTS = auto()
    CONDIMENTS_SEASONINGS = auto()
    PLANT_BASED_ALTERNATIVES = auto()
    SNACKS = auto()


class FoodIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Food


class Food(NutritionCollection, Event, FoodIdentifier):
    type: Literal[DataType.Food] = Field(alias=NutritionFields.TYPE)
    category: FoodCategory = Field(alias=NutritionFields.CATEGORY)
    consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"] = Field(alias=NutritionFields.CONSUMED_TYPE)
