from dataclasses import dataclass
from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import Rounded6Float
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.event import Event, EventFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.shared import BaseDataModel


class ConsumeUnit(StrEnum):
    DOSES = auto()
    ITEMS = auto()


@dataclass(frozen=True)
class MedicationFields(EventFields):
    CONSUMED_AMOUNT = "consumed_amount"
    FORM = "form"
    AMOUNT = "amount"
    AMOUNT_UNIT = "amount_unit"
    ADMINISTRATION = "administration"
    BRAND = "brand"
    GENERIC_NAME = "generic_name"
    RX_CUID = "rx_cuid"
    ITEMS_QUANTITY = "items_quantity"
    SINGLE_DOSE_INFORMATION = "single_dose_information"
    MEDICATION_DETAILS = "medication_details"
    CONSUME_UNIT = "consume_unit"


@dataclass(frozen=True)
class MedicationValueLimits:
    MAX_CONSUMED_QUANTITY = 10000
    MIN_CONSUMED_QUANTITY = 0
    MAX_BRAND_NAME_LENGTH = 256
    MAX_GENERIC_NAME_LENGTH = 256


class MedicationForm(StrEnum):
    PILL = auto()
    CAPSULE = auto()
    SOLUTION = auto()
    INJECTION = auto()
    INHALER = auto()
    CREAM = auto()
    FOAM = auto()
    SPRAY = auto()


class MedicationCategory(StrEnum):
    OTHER = auto()
    ANALGESICS = auto()
    ANTIBIOTICS = auto()
    ANTIHISTAMINES = auto()
    ANTIVIRALS = auto()
    ANTIFUNGALS = auto()
    ANTIPARASITICS = auto()
    CARDIOVASCULAR = auto()
    RESPIRATORY = auto()
    GASTROINTESTINAL = auto()
    ENDOCRINE = auto()
    NEUROLOGICAL = auto()
    MENTAL_HEALTH = auto()
    IMMUNOSUPPRESSANTS = auto()
    ONCOLOGY = auto()
    VACCINES = auto()
    DERMATOLOGICAL = auto()
    OPHTHALMIC = auto()
    OTIC = auto()
    MUSCULOSKELETAL = auto()
    HEMATOLOGICAL = auto()
    REPRODUCTIVE = auto()
    URINARY = auto()
    EMERGENCY = auto()


class SingleDoseInformation(BaseDataModel):
    amount: Rounded6Float = Field(alias=MedicationFields.AMOUNT)
    amount_unit: VolumeUnit | WeightUnit = Field(alias=MedicationFields.AMOUNT_UNIT)
    items_quantity: Rounded6Float = Field(alias=MedicationFields.ITEMS_QUANTITY)


class MedicationIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Medication


class Medication(Event, MedicationIdentifier):
    type: Literal[DataType.Medication] = Field(alias=MedicationFields.TYPE)
    category: MedicationCategory = Field(alias=MedicationFields.CATEGORY)
    single_dose_information: SingleDoseInformation = Field(alias=MedicationFields.SINGLE_DOSE_INFORMATION)

    consumed_amount: Rounded6Float = Field(
        alias=MedicationFields.CONSUMED_AMOUNT,
        ge=MedicationValueLimits.MIN_CONSUMED_QUANTITY,
        le=MedicationValueLimits.MAX_CONSUMED_QUANTITY,
    )
    consume_unit: VolumeUnit | WeightUnit | ConsumeUnit = Field(alias=MedicationFields.CONSUME_UNIT)
