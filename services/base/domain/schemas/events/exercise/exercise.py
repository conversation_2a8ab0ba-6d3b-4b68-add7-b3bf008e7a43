from dataclasses import dataclass
from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class ExerciseFields(EventFields):
    RATING = "rating"


class ExerciseCategory(StrEnum):
    EXERCISE = auto()
    STATIC_BALANCE = auto()
    DYNAMIC_BALANCE = auto()
    STABILITY = auto()
    DAILY_ACTIVITIES = auto()
    REHABILITATION = auto()
    INJURY_PREVENTION = auto()
    STATIC_STRETCHING = auto()
    DYNAMIC_STRETCHING = auto()
    JOINT_MOBILITY = auto()
    MUSCLE_ACTIVATION = auto()
    SELF_MASSAGE = auto()
    SWORD_ARTS = auto()
    BASKETBALL = auto()
    VOLLEYBALL = auto()
    TENNIS = auto()
    PICKLEBALL = auto()
    BADMINTON = auto()
    SQUASH = auto()
    SOCCER = auto()
    ULTIMATE_FRISBEE = auto()
    RUGBY = auto()
    AMERICAN_FOOTBALL = auto()
    FLAG_FOOTBALL = auto()
    ARCHERY = auto()
    BOWLING = auto()
    TABLE_TENNIS = auto()
    GOLF = auto()
    CRICKET = auto()
    LACROSSE = auto()
    FIELD_HOCKEY = auto()
    HANDBALL = auto()
    BOXING = auto()
    KICKBOXING = auto()
    WRESTLING = auto()
    JUDO = auto()
    SPORT = auto()


class ExerciseIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Exercise


class Exercise(Event, ExerciseIdentifier):
    type: Literal[DataType.Exercise] = Field(alias=ExerciseFields.TYPE)
    category: ExerciseCategory = Field(
        alias=ExerciseFields.CATEGORY,
    )
    rating: int | None = Field(
        alias=ExerciseFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
