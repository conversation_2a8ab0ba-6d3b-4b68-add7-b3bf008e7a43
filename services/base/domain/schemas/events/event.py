from abc import ABC
from dataclasses import dataclass
from typing import Sequence
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import (
    AssetReference,
    Document,
    EventMetadata,
    RBACDocument,
    SystemPropertiesDocument,
    TagsDocument,
    TimeIntervalDocument,
)
from services.base.domain.schemas.shared import BaseDataModel


@dataclass(frozen=True)
class EventValueLimits:
    RATING_MAXIMUM_VALUE = 10
    RATING_MINIMUM_VALUE = 0

    MAX_TAGS_COUNT = 64
    MAX_NOTE_LENGTH = 8196

    MAX_NAME_LENGTH = 256


@dataclass(frozen=True)
class EventPlanExtensionFields:
    PLAN_ID = "plan_id"


class EventPlanExtension(BaseDataModel):
    plan_id: UUID = Field(alias=EventPlanExtensionFields.PLAN_ID)


@dataclass(frozen=True)
class EventFields:
    ID = DocumentLabels.ID
    RBAC = DocumentLabels.RBAC
    TAGS = DocumentLabels.TAGS
    TYPE = DocumentLabels.TYPE
    TIMESTAMP = DocumentLabels.TIMESTAMP
    END_TIME = DocumentLabels.END_TIME
    DURATION = DocumentLabels.DURATION
    METADATA = DocumentLabels.METADATA
    TEMPLATE_ID = DocumentLabels.TEMPLATE_ID
    SUBMISSION_ID = DocumentLabels.SUBMISSION_ID
    SYSTEM_PROPERTIES = DocumentLabels.SYSTEM_PROPERTIES
    GROUP_ID = DocumentLabels.GROUP_ID
    NAME = "name"
    PLAN_EXTENSION = "plan_extension"
    ASSET_REFERENCES = DocumentLabels.ASSET_REFERENCES
    CATEGORY = DocumentLabels.CATEGORY
    NOTE = "note"
    NODE = "node"


class Event(Document, SystemPropertiesDocument, RBACDocument, TimeIntervalDocument, TagsDocument, ABC):
    name: NonEmptyStr = Field(alias=EventFields.NAME, max_length=EventValueLimits.MAX_NAME_LENGTH)
    submission_id: UUID = Field(alias=EventFields.SUBMISSION_ID)
    asset_references: Sequence[AssetReference] = Field(alias=EventFields.ASSET_REFERENCES)
    metadata: EventMetadata
    template_id: UUID | None = Field(alias=EventFields.TEMPLATE_ID)
    plan_extension: EventPlanExtension | None = Field(alias=EventFields.PLAN_EXTENSION)
    group_id: UUID | None = Field(alias=EventFields.GROUP_ID)
    note: NonEmptyStr | None = Field(
        alias=EventFields.NOTE,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )
