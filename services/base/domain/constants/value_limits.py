from dataclasses import dataclass


@dataclass(frozen=True)
class HeartRateValueLimit:
    MINIMUM = 0
    MAXIMUM = 400


@dataclass(frozen=True)
class RestingHeartRateValueLimit:
    MINIMUM = 0
    MAXIMUM = 400


@dataclass(frozen=True)
class CoordinatesValueLimit:
    LON_MAX = 180
    LON_MIN = -180
    LAT_MAX = 90
    LAT_MIN = -90


@dataclass(frozen=True)
class StepsValueLimit:
    MINIMUM = 1


@dataclass(frozen=True)
class MetadataTagValueLimit:
    MINIMUM = 1
    MAXIMUM = 64


@dataclass(frozen=True)
class LocationPlaceNameValueLimit:
    MINIMUM = 1
    MAXIMUM = 128


@dataclass(frozen=True)
class LocationPlaceAddressValueLimit:
    MINIMUM = 1
    MAXIMUM = 256


@dataclass(frozen=True)
class AnalyticModelsLimits:
    MILLION = 10**6
    BILLION = 10**9
