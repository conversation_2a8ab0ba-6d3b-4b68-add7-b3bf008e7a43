from zoneinfo import ZoneInfo

SECONDS_IN_HOUR = 3_600
SECONDS_IN_DAY = 86_400
SECONDS_IN_14_DAYS = 1_209_600
SECONDS_IN_60_DAYS = 5_184_000
SECONDS_IN_365_DAYS = 31_622_400
FALLBACK_TIMEZONE_STR = "UTC"
DEFAULT_TIMEZONE_UTC = ZoneInfo(FALLBACK_TIMEZONE_STR)


class TimeConstants:
    SECONDS_IN_MINUTE = 60
    SECONDS_IN_HOUR = SECONDS_IN_HOUR
    SECONDS_IN_DAY = SECONDS_IN_DAY
    SECONDS_IN_WEEK = 7 * SECONDS_IN_DAY

    SECONDS_IN_14_DAYS = SECONDS_IN_14_DAYS
    SECONDS_IN_60_DAYS = SECONDS_IN_60_DAYS
    SECONDS_IN_365_DAYS = SECONDS_IN_365_DAYS
    FALLBACK_TIMEZONE_STR = FALLBACK_TIMEZONE_STR
