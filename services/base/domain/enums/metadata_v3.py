from enum import StrEnum


class InsertableOrigin(StrEnum):
    AMAZON = "amazon"
    APPLE = "apple"
    FITBIT = "fitbit"
    GOOGLE = "google"
    NETFLIX = "netflix"
    OURA = "oura"
    LLIF = "llif"
    BEST_LIFE = "best_life"


class Origin(StrEnum):
    AMAZON = "amazon"
    APPLE = "apple"
    FITBIT = "fitbit"
    GOOGLE = "google"
    NETFLIX = "netflix"
    OURA = "oura"
    WALMART = "walmart"
    LLIF = "llif"
    BEST_LIFE = "best_life"
    UNKNOWN = "unknown"


class SourceService(StrEnum):
    APPLE_HEALTH_KIT = "apple_health_kit"
    GOOGLE_FIT = "google_fit"
    GOOGLE_HEALTH_CONNECT = "google_health_connect"
    AMAZON_ALEXA = "amazon_alexa"
    TAKEOUT = "takeout"
    FITBIT = "fitbit"
    BEST_LIFE_APP = "best_life_app"  # TODO this is basically manual entry. Change with refactor
    USDA_FOOD_DB = "usda_food_db"
    AI_PHOTO = "ai_photo"
    AI_TEXT = "ai_text"
    OPEN_FOOD_FACTS = "open_food_facts"


class SourceOS(StrEnum):
    MACOS = "macos"
    IOS = "ios"
    ANDROID = "android"
    IPADOS = "ipados"
    WINDOWS = "windows"
    LINUX = "linux"
    UNKNOWN = "unknown"


class Service(StrEnum):
    DATA = "data"
    VOICE = "voice"
    FILE = "file"
    EXPORT = "export"
    USER = "user"
