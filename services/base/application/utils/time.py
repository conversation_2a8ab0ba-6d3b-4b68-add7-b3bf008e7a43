import re
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo, available_timezones

from dateutil.relativedelta import relativedelta

from services.base.domain.constants.time_constants import TimeConstants


class TimeUtils:
    @staticmethod
    def get_relativedelta_from_aggregation_interval(aggregation_interval: str) -> relativedelta:
        """Get matching duration of opensearch aggregation interval as relative delta object"""
        search = re.search(r"\d*", aggregation_interval)
        if not search:
            raise ValueError
        number_part = int(search.group())
        if not number_part or number_part == 0:
            raise ValueError
        aggregation_intervals = {
            "m": relativedelta(minutes=number_part),
            "h": relativedelta(hours=number_part),
            "d": relativedelta(days=number_part),
            "D": relativedelta(days=number_part),
            "w": relativedelta(weeks=number_part),
            "M": relativedelta(months=number_part),
            "q": relativedelta(months=3 * number_part),
            "y": relativedelta(years=number_part),
        }
        return aggregation_intervals[aggregation_interval[-1]]

    @staticmethod
    def get_timedelta_from_aggregation_interval(aggregation_interval: str) -> timedelta:
        """Get matching duration of opensearch aggregation interval as relative delta object"""
        search = re.search(r"\d*", aggregation_interval)
        if not search:
            raise ValueError
        number_part = int(search.group())
        if not number_part or number_part == 0:
            raise ValueError
        aggregation_intervals = {
            "m": timedelta(minutes=number_part),
            "h": timedelta(hours=number_part),
            "d": timedelta(days=number_part),
            "D": timedelta(days=number_part),
            "w": timedelta(weeks=number_part),
        }
        return aggregation_intervals[aggregation_interval[-1]]

    @staticmethod
    def get_aggregation_interval_from_timedelta(td: timedelta) -> str:
        total_seconds = int(td.total_seconds())

        if total_seconds <= 0:
            raise ValueError("Timedelta must be positive")

        # Try to find the largest unit that divides evenly
        if total_seconds % TimeConstants.SECONDS_IN_WEEK == 0:
            weeks = total_seconds // TimeConstants.SECONDS_IN_WEEK
            return f"{weeks}w"
        elif total_seconds % TimeConstants.SECONDS_IN_DAY == 0:
            days = total_seconds // TimeConstants.SECONDS_IN_DAY
            return f"{days}d"
        elif total_seconds % TimeConstants.SECONDS_IN_HOUR == 0:
            hours = total_seconds // TimeConstants.SECONDS_IN_HOUR
            return f"{hours}h"
        elif total_seconds % TimeConstants.SECONDS_IN_MINUTE == 0:
            minutes = total_seconds // TimeConstants.SECONDS_IN_MINUTE
            return f"{minutes}m"
        else:
            raise ValueError(f"Timedelta {td} cannot be represented as a clean aggregation interval")


def is_timezone_aware(_datetime: datetime) -> bool:
    # https://docs.python.org/3/library/datetime.html#determining-if-an-object-is-aware-or-naive
    return (_datetime.tzinfo is not None) and (_datetime.tzinfo.utcoffset(_datetime) is not None)


def get_datetime_difference(input_a: datetime, input_b: datetime) -> timedelta:
    """Returns ABSOLUTE datetime difference (timedelta) regardless of input order
    Raises TypeError if inputs are not instances of datetimel
    Note: you can get the difference in seconds by .seconds, see datetime.timedelta for more info
    Note2: BOTH should have same timezone awareness/naiveness - either both tz aware or both with no tz (naive)
    otherwise Python throws TypeError
    """
    if (not isinstance(input_a, datetime)) or (not isinstance(input_b, datetime)):
        raise TypeError(f"Expecting 2 datetime values got {type(input_a)} and {type(input_b)}")

    return (input_a - input_b) if (input_a > input_b) else (input_b - input_a)


def gen_tzinfos():
    for zone in available_timezones():
        tz_info = ZoneInfo(zone)
        yield zone, tz_info


tz_infos = dict(gen_tzinfos())
