from uuid import UUID

from pydantic import Field

from services.base.application.database.models.filter_types import TimestampRangeFilter
from services.base.domain.annotated_types import AssetId, NonEmptyStr
from services.base.domain.schemas.shared import TimestampModel
from services.file_service.api.request_models.export_data_request_models import EnvironmentAggregationInterval
from services.file_service.application.enums.exportable_data_type import ExportableType


class TakeoutExportScheduledModel(TimestampModel):
    user_uuid: UUID
    task_id: UUID
    takeout_name: AssetId
    data_types: list[ExportableType] = Field(min_length=1)
    user_timezone: NonEmptyStr
    range_filter: TimestampRangeFilter | None = None
    export_csv: bool = Field(default=True)
    environment_aggregation_interval: EnvironmentAggregationInterval = Field(
        default=EnvironmentAggregationInterval.ONE_HOUR
    )
    system_run: bool = Field(
        description="System run does not send a notification and saves in a non user specific folder."
    )
    storage_path: str = Field(description="If provided the export will be saved in the specified path.")
