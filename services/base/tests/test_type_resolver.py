from services.base.domain.enums.event_type import EventType
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.domain.schemas.templates.event_template import TemplatePayloads
from services.base.type_resolver import TypeResolver


class TestTypeResolver:
    def test_amount_of_template_payloads_should_same_as_amount_of_events(self):
        for event_type in TypeResolver.EVENTS_V3:
            if event_type.type_id() == EventGroup.type_id():
                # We want to ignore EventGroup since it doesn't have payload
                continue
            TestTypeResolver._get_event_template(type_id=event_type.type_id())

    def test_group_event_payload_has_all_event_types(self):
        for event in TypeResolver.EVENTS_V3:
            if event.type_id() == EventGroup.type_id():
                # We want to ignore EventGroup since it doesn't have payload
                continue
            found = False
            for template_type in TemplatePayloads.__args__:
                if template_type.type_id() == event.type_id():
                    found = True
                    break
            if not found:
                assert False, f"Template type not found: {event.type_id()}"

    def test_all_event_builders_are_existing_for_all_events_v3(self):
        for event_type in TypeResolver.EVENTS_V3:
            if event_type.type_id() == EventGroup.type_id():
                # EventGroup or Group at all are not part of the EVENT_V3 union. If added tests such as
                # test_delete_recursive_group_endpoint_passes starts to failing
                continue
            TestTypeResolver._get_event_builder(type_id=event_type.type_id())

    def test_all_event_template_builders_are_existing_for_all_events_v3(self):
        for event_type in TypeResolver.EVENTS_V3:
            if event_type.type_id() == EventGroup.type_id():
                # We want to ignore EventGroup since it doesn't have payload
                continue
            TestTypeResolver._get_event_template_builder(type_id=event_type.type_id())

    def test_all_events_v3_should_be_in_event_type_enum(self):
        for event_type in TypeResolver.EVENTS_V3:
            EventType(event_type.type_id())

    def test_amount_of_event_api_outputs_should_be_same_as_amount_of_events(self):
        for event in TypeResolver.EVENTS_V3:
            TypeResolver.get_event_api_output_v3(type_id=event.type_id())

    @staticmethod
    def _get_event_builder(type_id: str):
        for event_builder in TypeResolver.EVENT_BUILDERS:
            if event_builder.type_id() == type_id:
                return event_builder

        raise ShouldNotReachHereException(f"Event builder not found with type_id: {type_id}")

    @staticmethod
    def _get_event_template_builder(type_id: str):
        for event_builder in TypeResolver.TEMPLATE_PAYLOAD_BUILDERS:
            if event_builder.type_id() == type_id:
                return event_builder

        raise ShouldNotReachHereException(f"Event template payload builder not found with type_id: {type_id}")

    @staticmethod
    def _get_event_template(type_id: str):
        for event_builder in TypeResolver.TEMPLATE_PAYLOADS:
            if event_builder.type_id() == type_id:
                return event_builder

        raise ShouldNotReachHereException(f"Event template payload builder not found with type_id: {type_id}")
