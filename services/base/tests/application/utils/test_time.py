from datetime import timedelta

import pytest
from dateutil.relativedelta import relativedelta

from services.base.application.utils.time import TimeUtils


@pytest.mark.parametrize(
    ("timestring", "expected_output"),
    [
        ("1h", relativedelta(hours=1)),
        ("10h", relativedelta(hours=10)),
        ("1d", relativedelta(days=1)),
        ("1D", relativedelta(days=1)),
        ("10d", relativedelta(days=10)),
        ("10D", relativedelta(days=10)),
        ("1w", relativedelta(weeks=1)),
        ("10w", relativedelta(weeks=10)),
        ("1M", relativedelta(months=1)),
        ("10M", relativedelta(months=10)),
        ("1q", relativedelta(months=3)),
        ("10q", relativedelta(months=30)),
        ("1y", relativedelta(years=1)),
        ("10y", relativedelta(years=10)),
    ],
)
def test_get_relativedelta_from_aggregation_interval(timestring: str, expected_output: relativedelta):
    assert TimeUtils.get_relativedelta_from_aggregation_interval(timestring) == expected_output


@pytest.mark.parametrize(
    "timestring",
    ["1B", "1BBBBBB"],
)
def test_get_relativedelta_from_aggregation_interval_should_raise_key_error(timestring: str):
    with pytest.raises(KeyError):
        TimeUtils.get_relativedelta_from_aggregation_interval(timestring)
    with pytest.raises(KeyError):
        TimeUtils.get_timedelta_from_aggregation_interval(timestring)


@pytest.mark.parametrize(
    "timestring",
    ["", "0M", "test", "m"],
)
def test_get_relativedelta_from_aggregation_interval_should_raise_value_error(timestring: str):
    with pytest.raises(ValueError):
        TimeUtils.get_relativedelta_from_aggregation_interval(timestring)
    with pytest.raises(ValueError):
        TimeUtils.get_timedelta_from_aggregation_interval(timestring)


@pytest.mark.parametrize(
    ("timestring", "expected_output"),
    [
        ("1h", timedelta(hours=1)),
        ("10h", timedelta(hours=10)),
        ("1d", timedelta(days=1)),
        ("1D", timedelta(days=1)),
        ("10d", timedelta(days=10)),
        ("10D", timedelta(days=10)),
        ("1w", timedelta(weeks=1)),
        ("10w", timedelta(weeks=10)),
    ],
)
def test_get_timedelta_from_aggregation_interval(timestring: str, expected_output: relativedelta):
    assert TimeUtils.get_timedelta_from_aggregation_interval(timestring) == expected_output


@pytest.mark.parametrize(
    ("timedelta_input", "expected_output"),
    [
        pytest.param(timedelta(minutes=1), "1m", id="1_minute"),
        pytest.param(timedelta(minutes=5), "5m", id="5_minutes"),
        pytest.param(timedelta(minutes=30), "30m", id="30_minutes"),
        pytest.param(timedelta(hours=1), "1h", id="1_hour"),
        pytest.param(timedelta(hours=6), "6h", id="6_hours"),
        pytest.param(timedelta(hours=24), "1d", id="24_hours_as_1_day"),
        pytest.param(timedelta(days=1), "1d", id="1_day"),
        pytest.param(timedelta(days=3), "3d", id="3_days"),
        pytest.param(timedelta(days=7), "1w", id="7_days_as_1_week"),
        pytest.param(timedelta(weeks=1), "1w", id="1_week"),
        pytest.param(timedelta(weeks=2), "2w", id="2_weeks"),
        pytest.param(timedelta(days=14), "2w", id="14_days_as_2_weeks"),
    ],
)
def test_get_aggregation_interval_from_timedelta(timedelta_input: timedelta, expected_output: str):
    assert TimeUtils.get_aggregation_interval_from_timedelta(timedelta_input) == expected_output


@pytest.mark.parametrize(
    "timedelta_input",
    [
        pytest.param(timedelta(seconds=30), id="30_seconds"),
        pytest.param(timedelta(seconds=90), id="90_seconds"),
        pytest.param(timedelta(minutes=1, seconds=30), id="1_minute_30_seconds"),
        pytest.param(timedelta(hours=1, seconds=30), id="1_hour_30_seconds"),
        pytest.param(timedelta(days=1, seconds=30), id="1_day_30_seconds"),
    ],
)
def test_get_aggregation_interval_from_timedelta_should_raise_value_error_for_non_clean_intervals(
    timedelta_input: timedelta,
):
    with pytest.raises(ValueError, match="cannot be represented as a clean aggregation interval"):
        TimeUtils.get_aggregation_interval_from_timedelta(timedelta_input)
