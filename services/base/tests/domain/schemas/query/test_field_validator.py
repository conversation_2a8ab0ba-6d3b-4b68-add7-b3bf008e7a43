from decimal import Decimal
from typing import List, Sequence

import pytest

from services.base.domain.schemas.events.exercise.cardio import Cardio
from services.base.domain.schemas.events.exercise.strength import Strength
from services.base.domain.schemas.query.validators.field_validator import FieldValidator
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException


class TestFieldValidator:
    @pytest.mark.parametrize(
        "field_type,expected",
        [(List[int], True), (list[int], True), (Sequence[int], True), (int, False), (str, False)],
    )
    def test_is_list_type(self, field_type, expected):
        assert FieldValidator._is_list_type(field_type=field_type) == expected

    @pytest.mark.parametrize(
        "field_type,expected",
        [(int, True), (float, True), (Decimal, True), (bool, False), (str, False)],
    )
    def test_is_numeric_type(self, field_type, expected):
        assert FieldValidator._is_numeric_type(field_type=field_type) == expected

    @pytest.mark.parametrize(
        "field_type, field_name",
        [
            (Cardio, "distance"),
            (Strength, "count"),
            (Cardio, "rating"),
        ],
    )
    def test_validate_field_must_be_numeric_passes(self, field_type, field_name):
        FieldValidator.validate_field(
            field_name=field_name, type_mapping=FieldValidator.get_type_properties(field_type), must_be_numeric=True
        )

    @pytest.mark.parametrize(
        "field_type, field_name",
        [
            (Cardio, "name"),
            (Cardio, "timestamp"),
        ],
    )
    def test_validate_field_must_be_numeric_raises(self, field_type, field_name):
        with pytest.raises(QueryValidationException):
            FieldValidator.validate_field(
                field_name=field_name, type_mapping=FieldValidator.get_type_properties(field_type), must_be_numeric=True
            )
