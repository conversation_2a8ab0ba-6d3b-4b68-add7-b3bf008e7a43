from typing import Self, Sequence
from uuid import uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.feeling.emotion import Emotion, EmotionCategory, EmotionIdentifier
from services.base.tests.domain.builders.event_builder_base import EventBuilderBase
from services.base.tests.domain.builders.event_metadata_builder import EventMetadataBuilder


class EmotionBuilder(EventBuilderBase, EmotionIdentifier):
    def __init__(self):
        super().__init__()
        self._category: EmotionCategory | None = None
        self._rating: int | None = None

    def build(self) -> Emotion:
        return Emotion(
            type=DataType.Emotion,
            template_id=None,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=EmotionCategory),
            name=self._name or self._category or PrimitiveTypesGenerator.generate_random_string(max_length=32),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            metadata=EventMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
            submission_id=self._submission_id or uuid4(),
            rating=(
                self._rating
                if self._rating is not None
                else PrimitiveTypesGenerator.generate_random_int(
                    min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                    max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
                )
            ),
            group_id=self._group_id,
            id=uuid4(),
            asset_references=self._asset_references,
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            plan_extension=None,
        )

    def build_n(self, n: int | None = None) -> Sequence[Emotion]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_category(self, category: EmotionCategory) -> Self:
        self._category = category
        return self

    def with_rating(self, rating: int | None) -> Self:
        self._rating = rating
        return self
