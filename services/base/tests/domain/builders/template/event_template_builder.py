import random
from datetime import datetime
from typing import Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_type import EventType
from services.base.domain.enums.metadata_v3 import InsertableOrigin
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.templates.event_template import EventTemplate, TypedTemplatePayloads
from services.base.tests.domain.builders.builder_base import BuilderBase
from services.base.tests.domain.builders.template.payload.template_payload_builder import TemplatePayloadBuilder
from services.base.type_resolver import TypeResolver


class EventTemplateBuilder(BuilderBase):
    def __init__(self):
        self._name: str | None = None
        self._document_name: str | None = None
        self._id: UUID | None = None
        self._owner_id: UUID | None = None
        self._archived_at: datetime | bool | None = None
        self._document: TypedTemplatePayloads | None = None
        self._tags: list[str] | None = None
        self._origin: InsertableOrigin | None = None

    def build(self) -> EventTemplate:
        archived_at = (
            self._archived_at
            if isinstance(self._archived_at, datetime)
            else (
                None
                if self._archived_at is False
                else random.choice((PrimitiveTypesGenerator.generate_random_aware_datetime(), None))
            )
        )
        origin = self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)
        document = self._document or TemplatePayloadBuilder().with_origin(origin=origin).build()
        return EventTemplate(
            id=self._id or uuid4(),
            name=self._name or PrimitiveTypesGenerator.generate_random_string(),
            document_name=self._document_name or PrimitiveTypesGenerator.generate_random_string(),
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            document=document,
            document_type=EventType(document.type_id()),
            type=DataType.EventTemplate,
            archived_at=archived_at,
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
        )

    def build_n(self, n: int | None = None) -> Sequence[EventTemplate]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def build_all(self, n: int | None = None) -> Sequence[EventTemplate]:
        out = []
        for payload_builder_type in TypeResolver.TEMPLATE_PAYLOAD_BUILDERS:
            payload_builder = payload_builder_type()
            out.extend(
                [
                    self.with_document(
                        document=payload_builder.with_origin(
                            origin=self._origin
                            or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)
                        ).build()
                    ).build()
                    for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))
                ]
            )
        return out

    def with_document(self, document: TypedTemplatePayloads) -> Self:
        self._document = document
        return self

    def with_name(self, name: str) -> Self:
        self._name = name
        return self

    def with_document_name(self, document_name: str) -> Self:
        self._document_name = document_name
        return self

    def with_owner_id(self, owner_id: UUID) -> Self:
        self._owner_id = owner_id
        return self

    def with_id(self, id: UUID) -> Self:
        self._id = id
        return self

    def with_archived_at(self, archived_at: datetime | bool) -> Self:
        self._archived_at = archived_at
        return self

    def with_origin(self, origin: InsertableOrigin) -> Self:
        self._origin = origin
        return self

    def with_tags(self, tags: list[str]) -> Self:
        self._tags = tags
        return self
