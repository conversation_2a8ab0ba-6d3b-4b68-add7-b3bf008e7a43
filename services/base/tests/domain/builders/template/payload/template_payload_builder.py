import random
from typing import Self

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import InsertableOrigin
from services.base.domain.schemas.templates.event_template import TypedTemplatePayloads
from services.base.tests.domain.builders.builder_base import BuilderBase
from services.base.type_resolver import TypeResolver


class TemplatePayloadBuilder(BuilderBase):

    def __init__(self):
        self._origin: InsertableOrigin | None = None

    def build(self) -> TypedTemplatePayloads:
        template_builder_type = random.choice(TypeResolver.TEMPLATE_PAYLOAD_BUILDERS)
        origin = self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)
        return template_builder_type().with_origin(origin=origin).build()

    def with_origin(self, origin: InsertableOrigin) -> Self:
        self._origin = origin
        return self
