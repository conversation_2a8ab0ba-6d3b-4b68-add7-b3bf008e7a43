from typing import Self, Sequence
from uuid import uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.enums.type_tree.activity_node import ActivityNode
from services.base.domain.schemas.events.activity import Activity, ActivityCategory, ActivityIdentifier
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.tests.domain.builders.event_builder_base import EventBuilderBase
from services.base.tests.domain.builders.event_metadata_builder import EventMetadataBuilder


class ActivityBuilder(EventBuilderBase, ActivityIdentifier):
    def __init__(self):
        super().__init__()
        self._note: str | None = None

    def build(self) -> Activity:
        return Activity(
            type=DataType.Activity,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=ActivityCategory),
            node=PrimitiveTypesGenerator.generate_random_enum(enum_type=ActivityNode),
            template_id=None,
            name=self._name or PrimitiveTypesGenerator.generate_random_string(max_length=32),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            metadata=EventMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
            submission_id=self._submission_id or uuid4(),
            rating=PrimitiveTypesGenerator.generate_random_int(max_value=10, min_value=0),
            id=uuid4(),
            group_id=self._group_id,
            asset_references=self._asset_references,
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            note=self._note or PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            plan_extension=None,
        )

    def build_n(self, n: int | None = None) -> Sequence[Activity]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_note(self, note: str) -> Self:
        self._note = note
        return self
