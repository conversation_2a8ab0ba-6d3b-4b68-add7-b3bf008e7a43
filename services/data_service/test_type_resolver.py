from services.base.type_resolver import TypeResolver as BaseTypeResolver
from services.data_service.application.use_cases.events.models.insert_event_group_input import (
    InsertEventInputs as GroupInsertEventInputs,
)
from services.data_service.type_resolver import TypeResolver as DataTypeResolver


class TestTypeResolver:
    def test_amount_of_insert_event_inputs_should_be_same_as_amount_of_events(self):
        for event in BaseTypeResolver.EVENTS_V3:
            DataTypeResolver.get_insert_event_input(type_id=event.type_id())

    def test_amount_of_insert_event_input_builders_should_be_same_as_amount_of_events(self):
        for event in BaseTypeResolver.EVENTS_V3:
            DataTypeResolver.get_insert_event_input_builder(type_id=event.type_id())

    def test_amount_of_update_event_inputs_should_be_same_as_amount_of_events(self):
        for event in BaseTypeResolver.EVENTS_V3:
            DataTypeResolver.get_update_event_input(type_id=event.type_id())

    def test_amount_of_update_event_input_builders_should_be_same_as_amount_of_events(self):
        for event in BaseTypeResolver.EVENTS_V3:
            DataTypeResolver.get_update_event_input_builder(type_id=event.type_id())

    def test_group_have_all_event_types(self):
        for event_type in DataTypeResolver.INSERT_EVENT_INPUTS_UNION.__args__:
            found = False
            for group_type in GroupInsertEventInputs.__args__:
                if group_type.type_id() == event_type.type_id():
                    found = True
                    break
            if not found:
                assert False, f"Insert group event type not found: {event_type.type_id()}"
