from dataclasses import dataclass

from services.data_service.api.aggregate_endpoints import aggregate_router
from services.data_service.api.ai_endpoints import ai_router
from services.data_service.api.analyze_endpoints import analyze_router
from services.data_service.api.assets_endpoints import assets_router
from services.data_service.api.constants import (
    AggregationEndpointRoutes,
    AIEndpointRoutes,
    AnalyzeEndpointRoutes,
    AssetEndpointRoutes,
    ContactEndpointRoutes,
    DocumentEndpointRoutes,
    EventEndpointRoutes,
    ExperimentalEndpointRoutes,
    LookupEndpointRoutes,
    PlaceEndpointRoutes,
    PlanEndpointRoutes,
    RecordEndpointRoutes,
    SyncRoutes,
    TemplateRoutes,
    UseCaseEndpointRoutes,
)
from services.data_service.api.contact_endpoints import contact_router
from services.data_service.api.document_endpoints import document_router
from services.data_service.api.event_endpoints.event_endpoints import event_router
from services.data_service.api.experimental_endpoints import experimental_router
from services.data_service.api.lookup_endpoints import lookup_router
from services.data_service.api.place_endpoints import place_router
from services.data_service.api.plan_endpoints import plan_router
from services.data_service.api.record_endpoints import record_router
from services.data_service.api.sync_endpoints import sync_router
from services.data_service.api.template_endpoints import template_router
from services.data_service.api.use_case_endpoints import use_case_router


@dataclass(frozen=True)
class AssetsEndpointUrls:
    BY_ID = f"{assets_router.prefix}{AssetEndpointRoutes.BY_ID}"
    URL = f"{assets_router.prefix}{AssetEndpointRoutes.URL}"


@dataclass(frozen=True)
class PlanEndpointUrls:
    BASE = f"{plan_router.prefix}{PlanEndpointRoutes.BASE}"
    SEARCH = f"{plan_router.prefix}{PlanEndpointRoutes.SEARCH}"
    ARCHIVE = f"{plan_router.prefix}{PlanEndpointRoutes.ARCHIVE}"
    COMPLETE = f"{plan_router.prefix}{PlanEndpointRoutes.COMPLETE}"


@dataclass(frozen=True)
class TemplateEndpointUrls:
    BASE = f"{template_router.prefix}{TemplateRoutes.BASE}"
    SEARCH = f"{template_router.prefix}{TemplateRoutes.SEARCH}"
    ARCHIVE = f"{template_router.prefix}{TemplateRoutes.ARCHIVE}"


class UseCaseEndpointUrls:
    BASE = f"{use_case_router.prefix}{UseCaseEndpointRoutes.BASE}"
    SEARCH = f"{use_case_router.prefix}{UseCaseEndpointRoutes.SEARCH}"
    ARCHIVE = f"{use_case_router.prefix}{UseCaseEndpointRoutes.ARCHIVE}"


@dataclass(frozen=True)
class ContactEndpointUrls:
    BASE = f"{contact_router.prefix}{ContactEndpointRoutes.BASE}"
    SEARCH = f"{contact_router.prefix}{ContactEndpointRoutes.SEARCH}"
    ARCHIVE = f"{contact_router.prefix}{ContactEndpointRoutes.ARCHIVE}"


@dataclass(frozen=True)
class EventEndpointUrls:
    BASE = f"{event_router.prefix}{EventEndpointRoutes.BASE}"
    FEED = f"{event_router.prefix}{EventEndpointRoutes.FEED}"
    MODIFY_ASSETS = f"{event_router.prefix}{EventEndpointRoutes.MODIFY_ASSETS}"
    BY_ID = f"{event_router.prefix}{EventEndpointRoutes.BY_ID}"
    GROUP_REORDER = f"{event_router.prefix}{EventEndpointRoutes.GROUP_REORDER}"


@dataclass(frozen=True)
class RecordEndpointUrls:
    BASE = f"{record_router.prefix}{RecordEndpointRoutes.BASE}"


class SyncEndpointUrls:
    HC = f"{sync_router.prefix}{SyncRoutes.HC}"
    AHK = f"{sync_router.prefix}{SyncRoutes.AHK}"


@dataclass(frozen=True)
class AggregationEndpointUrls:
    FREQUENCY_DISTRIBUTION = f"{aggregate_router.prefix}{AggregationEndpointRoutes.FREQUENCY_DISTRIBUTION}"
    TIME_INTERVAL = f"{aggregate_router.prefix}{AggregationEndpointRoutes.TIME_INTERVAL_AGGREGATION}"
    DATE_HISTOGRAM = f"{aggregate_router.prefix}{AggregationEndpointRoutes.DATE_HISTOGRAM}"
    CALENDAR_HISTOGRAM_AGGREGATION = (
        f"{aggregate_router.prefix}{AggregationEndpointRoutes.CALENDAR_HISTOGRAM_AGGREGATION}"
    )


@dataclass(frozen=True)
class AnalyzeEndpointUrls:
    TREND_DETECT = f"{analyze_router.prefix}{AnalyzeEndpointRoutes.TREND_DETECT}"
    CORRELATE_EVENT = f"{analyze_router.prefix}{AnalyzeEndpointRoutes.CORRELATE_EVENT}"
    CORRELATE_EVENT_SUGGEST_PARAMETERS = (
        f"{analyze_router.prefix}{AnalyzeEndpointRoutes.CORRELATE_EVENT_SUGGEST_PARAMETERS}"
    )
    ANALYZE_DATA_SERIES = f"{analyze_router.prefix}{AnalyzeEndpointRoutes.ANALYZE_DATA_SERIES}"


@dataclass(frozen=True)
class LookupEndpointURLs:
    BASE = lookup_router.prefix + LookupEndpointRoutes.BASE
    CONTENT_LOOKUP = lookup_router.prefix + LookupEndpointRoutes.CONTENT_LOOKUP
    NUTRITION_THIRD_PARTY_LOOKUP = lookup_router.prefix + LookupEndpointRoutes.NUTRITION_THIRD_PARTY_LOOKUP
    NUTRITION_AI_LOOKUP = lookup_router.prefix + LookupEndpointRoutes.NUTRITION_AI_LOOKUP
    NUTRITION_IMAGE_AI_LOOKUP = lookup_router.prefix + LookupEndpointRoutes.NUTRITION_IMAGE_AI_LOOKUP


@dataclass(frozen=True)
class DocumentEndpointURLs:
    BASE = document_router.prefix + DocumentEndpointRoutes.BASE
    FEED = document_router.prefix + DocumentEndpointRoutes.FEED
    NODE_FEED = document_router.prefix + DocumentEndpointRoutes.NODE_FEED
    BY_QUERY = document_router.prefix + DocumentEndpointRoutes.BY_QUERY
    ALL_DATA = document_router.prefix + DocumentEndpointRoutes.ALL_DATA


@dataclass(frozen=True)
class ExperimentalEndpointURLs:
    BASE = experimental_router.prefix + DocumentEndpointRoutes.BASE
    SUGGEST_DESCENDANT_NODES = experimental_router.prefix + ExperimentalEndpointRoutes.SUGGEST_DESCENDANT_NODES


@dataclass(frozen=True)
class AIEndpointUrls:
    SUGGEST_EVENT = f"{ai_router.prefix}{AIEndpointRoutes.SUGGEST_EVENT}"


@dataclass(frozen=True)
class PlaceEndpointUrls:
    BASE = f"{place_router.prefix}{PlaceEndpointRoutes.BASE}"
    SEARCH = f"{place_router.prefix}{PlaceEndpointRoutes.SEARCH}"
    ARCHIVE = f"{place_router.prefix}{PlaceEndpointRoutes.ARCHIVE}"
