import random
from collections import Counter, defaultdict
from datetime import datetime, timedelta, timezone
from random import randint
from typing import Any, AsyncGenerator, Awaitable, Callable, Sequence

import pytest
from starlette import status

from services.base.api.output.time_interval_aggregation_api_output import TimeIntervalAggregationAPIOutput
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_type import EventType
from services.base.domain.enums.user_document_type import UserDocumentType
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event import Event, EventFields
from services.base.domain.schemas.events.feeling.emotion import Emotion, EmotionFields
from services.base.domain.schemas.events.symptom import Symptom, SymptomFields
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.query.aggregations import (
    CalendarAggregationType,
    SimpleAggregationMethod,
)
from services.base.tests.api.query.leaf_query_api_helper import LeafQueryAPIHelper
from services.base.tests.domain.builders.emotion_builder import EmotionBuilder
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.base.tests.domain.builders.note_builder import NoteBuilder
from services.base.tests.domain.builders.stress_builder import StressBuilder
from services.base.tests.domain.builders.symptom_builder import SymptomBuilder
from services.data_service.api.builders.calendar_histogram_aggregation_api_request_input_builder import (
    CalendarHistogramAggregationAPIRequestInputBuilder,
)
from services.data_service.api.models.request.aggregate.frequency_distribution_api_request_input import (
    FrequencyDistributionAPIRequestInput,
)
from services.data_service.api.models.response.date_histogram_api_output import DataHistogramAPIOutput
from services.data_service.api.models.response.frequency_distribution_api_output import FrequencyDistributionAPIOutput
from services.data_service.api.queries.document_query_api import DocumentTypedQueryAPI
from services.data_service.api.queries.event_query_api import EventQueryAPI, EventTypedQueryAPI
from services.data_service.api.tests.common_rpc_calls import _call_post_endpoint
from services.data_service.api.urls import AggregationEndpointUrls
from services.data_service.application.use_cases.calendar_aggregation.calendar_histogram_aggregation_use_case import (
    CalendarHistogramAggregationUseCaseOutputBoundary,
)


class TestAggregationEndpoints:

    @pytest.fixture
    async def user_with_events_with_predefined_tags(
        self,
        event_repo: EventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[Any, tuple[Sequence[Event], dict]]:
        user, headers = await user_headers_factory()
        tags = ["morning", "evening", "midday", "social", "tired", "sick", "joyous", "work", "free_time", "trip"]

        events = [
            EventBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_tags(tags=random.sample(population=tags, k=random.randint(1, 3)))
            .build()
            for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=5, max_value=20))
        ]
        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, headers

        await event_repo.delete_by_id(ids=[e.id for e in inserted_events])

    @pytest.fixture
    async def user_with_events_with_different_types(
        self,
        event_repo: EventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[Any, tuple[Sequence[Event], dict]]:
        user, headers = await user_headers_factory()

        # Create events of different types using build_all method
        events = EventBuilder().with_owner_id(owner_id=user.user_uuid).build_all(n=2)
        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, headers

        await event_repo.delete_by_id(ids=[e.id for e in inserted_events])

    async def test_frequency_distribution_endpoint_no_query_passes(
        self, user_with_events_with_predefined_tags: tuple[Sequence[Event], dict]
    ):
        # Arrange
        events_with_tags, headers = user_with_events_with_predefined_tags
        expected_event_types = {e.type_id() for e in events_with_tags}

        # Act
        response = await _call_post_endpoint(
            request_url=AggregationEndpointUrls.FREQUENCY_DISTRIBUTION,
            json={
                "field_name": DocumentLabels.TAGS,
                "queries": [
                    {
                        "types": list(expected_event_types),
                    }
                ],
            },
            headers=headers,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        result = FrequencyDistributionAPIOutput(**response.json())
        assert result

        returned_types = [type.value for type in result.data.keys()]
        assert sorted(expected_event_types) == sorted(returned_types)

        for expected_type in expected_event_types:
            expected_events = [e for e in events_with_tags if e.type_id() == expected_type]
            assert expected_events

            flatten_tags = [tag for e in expected_events for tag in e.tags]
            expected_count_dict = dict(Counter(flatten_tags))
            returned_count_dict = result.data[UserDocumentType(expected_type)]

            assert returned_count_dict == expected_count_dict

    @pytest.fixture
    async def user_with_symptoms(
        self,
        event_repo: EventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[Any, tuple[Sequence[Event], dict]]:
        user, headers = await user_headers_factory()

        events = [
            SymptomBuilder().with_owner_id(owner_id=user.user_uuid).build()
            for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=5, max_value=20))
        ]
        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, headers

        await event_repo.delete_by_id(ids=[e.id for e in inserted_events])

    async def test_frequency_distribution_endpoint_numeric_field_passes(
        self, user_with_symptoms: tuple[Sequence[Symptom], dict]
    ):
        # Arrange
        symptoms, headers = user_with_symptoms
        expected_event_types = {e.type_id() for e in symptoms}

        # Act
        response = await _call_post_endpoint(
            request_url=AggregationEndpointUrls.FREQUENCY_DISTRIBUTION,
            json={
                "field_name": SymptomFields.RATING,
                "queries": [
                    {
                        "types": list(expected_event_types),
                    }
                ],
            },
            headers=headers,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        result = FrequencyDistributionAPIOutput(**response.json())
        assert result

        returned_types = result.data.keys()
        assert sorted(expected_event_types) == sorted(returned_types)

        for expected_type in expected_event_types:
            expected_events = [e for e in symptoms if e.type_id() == expected_type]
            assert expected_events

            ratings = [str(e.rating) for e in expected_events if e.rating is not None]
            expected_count_dict = dict(Counter(ratings))
            returned_count_dict = result.data[UserDocumentType(expected_type)]

            assert sorted(returned_count_dict) == sorted(expected_count_dict)

    async def test_frequency_distribution_endpoint_empty_queries_search_all_types(
        self, user_with_events_with_different_types: tuple[Sequence[Event], dict]
    ):
        # Arrange
        events_with_types, headers = user_with_events_with_different_types
        expected_event_types = {e.type_id() for e in events_with_types}

        # Act
        response = await _call_post_endpoint(
            request_url=AggregationEndpointUrls.FREQUENCY_DISTRIBUTION,
            json={
                "field_name": DocumentLabels.TYPE,
                "queries": [
                    {
                        "types": list(expected_event_types),  # should be removed with rest of V2
                    }
                ],
            },
            headers=headers,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        result = FrequencyDistributionAPIOutput(**response.json())

        # Get the actual event types present in our test data
        actual_event_types = {e.type_id() for e in events_with_types}

        # Check that we have data for the types that exist in our events
        for event_type in actual_event_types:
            user_doc_type = UserDocumentType(event_type)
            assert user_doc_type in result.data, f"Expected type {event_type} not found in result"

            expected_events = [e for e in events_with_types if e.type_id() == event_type]
            expected_count = len(expected_events)
            actual_count = result.data[user_doc_type].get(event_type, 0)

            assert (
                actual_count == expected_count
            ), f"Expected {expected_count} events of type {event_type}, got {actual_count}"

    async def test_frequency_distribution_endpoint_with_query_passes(
        self, user_with_events_with_predefined_tags: tuple[Sequence[Event], dict]
    ):
        # Arrange
        events_with_tags, headers = user_with_events_with_predefined_tags
        selected_events = random.sample(k=randint(1, len(events_with_tags)), population=events_with_tags)
        expected_names = list({e.name for e in selected_events})
        expected_event_types = {e.type_id() for e in selected_events if e.tags}
        # Act
        response = await _call_post_endpoint(
            request_url=AggregationEndpointUrls.FREQUENCY_DISTRIBUTION,
            json={
                "field_name": DocumentLabels.TAGS,
                "queries": [
                    {
                        "types": list(expected_event_types),
                        "query": {"type": "values", "field_name": EventFields.NAME, "values": expected_names},
                    }
                ],
            },
            headers=headers,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        result = FrequencyDistributionAPIOutput(**response.json())
        assert result

        returned_types = [type.value for type in result.data.keys()]
        assert sorted(expected_event_types) == sorted(returned_types)

        for expected_type in expected_event_types:
            expected_events = [e for e in selected_events if e.type_id() == expected_type]
            assert expected_events

            flatten_tags = [tag for e in expected_events for tag in e.tags]
            expected_count_dict = dict(Counter(flatten_tags))
            given_count_dict = result.data[UserDocumentType(expected_type)]

            assert given_count_dict == expected_count_dict

    async def test_frequency_distribution_endpoint_no_input_returns_validation_error(self, user_headers_factory):
        _, headers = await user_headers_factory()
        request_url = AggregationEndpointUrls.FREQUENCY_DISTRIBUTION

        response = await _call_post_endpoint(request_url=request_url, headers=headers)

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, f"Got response {response.content}"

    async def test_frequency_distribution_endpoint_invalid_field_returns_validation_error(self, user_headers_factory):
        _, headers = await user_headers_factory()
        request_url = AggregationEndpointUrls.FREQUENCY_DISTRIBUTION

        field_name = SymptomFields.RATING
        queries = [DocumentTypedQueryAPI(types=[UserDocumentType.Note], query=None)]
        request_input = FrequencyDistributionAPIRequestInput(
            queries=queries,
            field_name=field_name,
        )
        response = await _call_post_endpoint(request_url=request_url, headers=headers, json=request_input.model_dump())

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, f"Got response {response.content}"

    async def test_frequency_distribution_endpoint_no_documents_returns_no_content(self, user_headers_factory):
        _, headers = await user_headers_factory()
        request_url = AggregationEndpointUrls.FREQUENCY_DISTRIBUTION

        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "field_name": DocumentLabels.TAGS,
                "queries": [{"types": [DataType.Emotion.value]}],
            },
            headers=headers,
        )

        assert response.status_code == status.HTTP_204_NO_CONTENT, f"Got response {response.content}"

    @pytest.fixture
    async def user_with_emotions(
        self,
        event_repo: EventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[tuple[Sequence[Event], dict]]:
        user, headers = await user_headers_factory()
        start = PrimitiveTypesGenerator.generate_random_aware_datetime()
        end = start + timedelta(days=100)
        events = []
        for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=50, max_value=100)):
            ts = PrimitiveTypesGenerator.generate_random_aware_datetime(gte=start, lte=end)
            et = PrimitiveTypesGenerator.generate_random_aware_datetime(
                allow_none=True,
                gte=ts,
                lte=ts + PrimitiveTypesGenerator.generate_random_timedelta(max_timedelta=timedelta(hours=10)),
            )
            events.append(
                EmotionBuilder()
                .with_owner_id(owner_id=user.user_uuid)
                .with_name(name="mood")
                .with_timestamp(timestamp=ts)
                .with_end_time(end_time=et)
                .build()
            )

        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, headers

        await event_repo.delete_by_id(ids=[e.id for e in inserted_events])

    async def test_date_histogram_endpoint_with_query_passes(self, user_with_emotions):
        # Arrange
        inserted_events, headers = user_with_emotions
        request_url = AggregationEndpointUrls.DATE_HISTOGRAM
        expected_doc_count = len(inserted_events)

        def bucket_by_day(events):
            buckets = defaultdict(list)
            for event in events:
                event_date = event.timestamp.astimezone(timezone.utc).date().isoformat()
                buckets[event_date].append(event)
            return buckets

        daily_buckets = bucket_by_day(inserted_events)
        expected_buckets = {}
        for day, events in daily_buckets.items():
            rating_sum = sum(event.rating for event in events)
            rating_max = max(event.rating for event in events)
            duration_sum = sum(event.duration for event in events)
            expected_buckets[day] = {"rating_sum": rating_sum, "rating_max": rating_max, "duration_sum": duration_sum}

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "queries": [
                    {
                        "types": [DataType.Emotion.value],
                        "query": None,
                    }
                ],
                "aggregation": {
                    "histogram_field_aggregations": [
                        {"field_name": EmotionFields.RATING},
                        {"field_name": EmotionFields.RATING, "aggregation_method": "max"},
                        {"field_name": EmotionFields.DURATION},
                    ],
                    "default_aggregation_method": "sum",
                    "interval": "1d",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = DataHistogramAPIOutput(**response.json())
        assert output
        doc_count = sum([r.doc_count for r in output.results])
        assert doc_count == expected_doc_count
        for r in output.results:
            if r.doc_count != 0:
                expected_bucket = expected_buckets[r.timestamp.date().isoformat()]
                assert r.aggregates[0].value == expected_bucket["rating_sum"]
                assert r.aggregates[1].value == expected_bucket["rating_max"]
                assert r.aggregates[2].value == expected_bucket["duration_sum"]

    async def test_date_histogram_endpoint_with_stats_passes(self, user_with_emotions):
        # Arrange
        inserted_events, headers = user_with_emotions
        request_url = AggregationEndpointUrls.DATE_HISTOGRAM
        expected_doc_count = len(inserted_events)

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "queries": [
                    {
                        "types": [DataType.Emotion.value],
                        "query": None,
                    }
                ],
                "aggregation": {
                    "histogram_field_aggregations": [
                        {"field_name": EmotionFields.RATING},
                    ],
                    "default_aggregation_method": "stats",
                    "interval": "1d",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = DataHistogramAPIOutput(**response.json())
        assert output
        doc_count = sum([r.doc_count for r in output.results])
        assert doc_count == expected_doc_count
        for r in output.results:
            assert len(r.aggregates) == 12
            assert r.aggregates[0].agg_method == "count"
            assert r.aggregates[1].agg_method == "min"
            assert r.aggregates[2].agg_method == "max"
            assert r.aggregates[3].agg_method == "avg"
            assert r.aggregates[4].agg_method == "sum"
            assert r.aggregates[5].agg_method == "sum_of_squares"
            assert r.aggregates[6].agg_method == "variance"
            assert r.aggregates[7].agg_method == "variance_population"
            assert r.aggregates[8].agg_method == "variance_sampling"
            assert r.aggregates[9].agg_method == "std_deviation"
            assert r.aggregates[10].agg_method == "std_deviation_population"
            assert r.aggregates[11].agg_method == "std_deviation_sampling"

    async def test_date_histogram_endpoint_with_bucket_agg_passes(self, user_with_emotions):
        # Arrange
        inserted_events, headers = user_with_emotions
        request_url = AggregationEndpointUrls.DATE_HISTOGRAM
        expected_doc_count = len(inserted_events)

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "queries": [
                    {
                        "types": [DataType.Emotion.value],
                        "query": None,
                    }
                ],
                "aggregation": {
                    "histogram_field_aggregations": [
                        {
                            "field_name": EmotionFields.RATING,
                            "bucket_aggregation": [
                                {"aggregation_method": "moving_avg"},
                                {"aggregation_method": "derivative"},
                                {"aggregation_method": "cumulative_sum"},
                            ],
                        },
                    ],
                    "default_aggregation_method": "sum",
                    "interval": "1d",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = DataHistogramAPIOutput(**response.json())
        assert output
        doc_count = sum([r.doc_count for r in output.results])
        assert doc_count == expected_doc_count

        for r in output.results:
            assert len(r.aggregates) == 4
            assert r.aggregates[0].agg_method == "sum"
            assert r.aggregates[1].agg_method == "moving_avg"
            assert r.aggregates[2].agg_method == "derivative"

    async def test_date_histogram_endpoint_no_sub_aggregations_passes(self, user_with_emotions):
        # Arrange
        inserted_events, headers = user_with_emotions
        request_url = AggregationEndpointUrls.DATE_HISTOGRAM
        expected_doc_count = len(inserted_events)

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "queries": [
                    {
                        "types": [DataType.Emotion.value],
                        "query": None,
                    }
                ],
                "aggregation": {
                    "histogram_field_aggregations": [],
                    "default_aggregation_method": "sum",
                    "interval": "1d",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = DataHistogramAPIOutput(**response.json())
        assert output
        doc_count = sum([r.doc_count for r in output.results])
        assert doc_count == expected_doc_count

    @pytest.fixture
    async def user_with_data(
        self,
        depr_event_repository: DeprEventRepository,
        event_repo: EventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[tuple[MemberUser, dict, int], Any]:
        lte_range = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        gte_range = lte_range - timedelta(days=365)
        documents_to_generate = 5

        user, headers = await user_headers_factory()
        notes = [
            NoteBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(PrimitiveTypesGenerator.generate_random_aware_datetime(gte=gte_range, lte=lte_range))
            .build()
            for _ in range(documents_to_generate)
        ]
        _ = await event_repo.insert(events=notes, force_strong_consistency=True)

        yield user, headers, documents_to_generate

        await event_repo.delete_by_id(ids=[n.id for n in notes])

    @pytest.fixture
    async def user_with_multiple_types_data(
        self,
        event_repo: EventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[tuple[MemberUser, dict, int], Any]:
        lte_range = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        gte_range = lte_range - timedelta(days=365)
        documents_to_generate = 5
        document_generated = 0

        user, headers = await user_headers_factory()
        emotions = [
            EmotionBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(PrimitiveTypesGenerator.generate_random_aware_datetime(gte=gte_range, lte=lte_range))
            .build()
            for _ in range(documents_to_generate)
        ]
        stresses = [
            StressBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(PrimitiveTypesGenerator.generate_random_aware_datetime(gte=gte_range, lte=lte_range))
            .build()
            for _ in range(documents_to_generate)
        ]

        emotions = await event_repo.insert(events=emotions, force_strong_consistency=True)
        stresses = await event_repo.insert(events=stresses, force_strong_consistency=True)
        document_generated += len(emotions)
        document_generated += len(stresses)
        notes = [
            NoteBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(PrimitiveTypesGenerator.generate_random_aware_datetime(gte=gte_range, lte=lte_range))
            .build()
            for _ in range(documents_to_generate)
        ]
        document_generated += len(notes)
        notes = await event_repo.insert(events=notes, force_strong_consistency=True)

        yield user, headers, document_generated

        await event_repo.delete_by_id(ids=[n.id for n in notes])
        await event_repo.delete_by_id(ids=[e.id for e in [*emotions, *stresses]])

    @pytest.fixture
    async def user_with_yearly_mood_ratings_return_monday(
        self, event_repo: EventRepository, user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]]
    ) -> AsyncGenerator[tuple[MemberUser, dict, list[Emotion]], Any]:
        gte_range = datetime(year=2024, month=1, day=1, hour=21, tzinfo=timezone.utc)
        lte_range = datetime(year=2024, month=12, day=31, hour=21, tzinfo=timezone.utc)
        user, headers = await user_headers_factory()
        emotions = []
        current_day = gte_range
        monday_emotions = []
        while current_day < lte_range:
            emotions.append(
                EmotionBuilder().with_owner_id(owner_id=user.user_uuid).with_timestamp(timestamp=current_day).build()
            )
            if current_day.weekday() == 0:
                monday_emotions.append(emotions[-1])
            current_day = current_day + timedelta(days=1)

        emotions = await event_repo.insert(events=emotions, force_strong_consistency=True)

        yield user, headers, monday_emotions

        await event_repo.delete_by_id(ids=[e.id for e in [*emotions]])

    @pytest.fixture
    async def user_with_random_mood_ratings(
        self, event_repo: EventRepository, user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]]
    ) -> AsyncGenerator[tuple[MemberUser, dict, Sequence[Event]], Any]:
        user, headers = await user_headers_factory()
        emotions = []
        emotions.append(
            EmotionBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(datetime(2014, 1, 1, tzinfo=timezone.utc))
            .build()
        )
        emotions.append(
            EmotionBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(datetime(2020, 1, 1, tzinfo=timezone.utc))
            .build()
        )
        emotions.append(
            EmotionBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(datetime(2025, 1, 1, tzinfo=timezone.utc))
            .build()
        )
        emotions.append(
            EmotionBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(datetime(2027, 1, 1, tzinfo=timezone.utc))
            .build()
        )

        emotions = await event_repo.insert(events=emotions, force_strong_consistency=True)

        yield user, headers, emotions

        await event_repo.delete_by_id(ids=[e.id for e in [*emotions]])

    async def test_calendar_histogram_aggregation_endpoint_should_pass(
        self,
        user_with_yearly_mood_ratings_return_monday: tuple[MemberUser, dict, list[Emotion]],
    ):
        # Arrange
        user, headers, monday_emotions = user_with_yearly_mood_ratings_return_monday
        gte_range = datetime(year=2024, month=1, day=1, hour=21, tzinfo=timezone.utc)
        lte_range = datetime(year=2024, month=12, day=31, hour=21, tzinfo=timezone.utc)

        range_query = LeafQueryAPIHelper.create_range_query(
            field_name=DocumentLabels.TIMESTAMP, gte=gte_range, lte=lte_range
        )
        query_item = EventTypedQueryAPI(types=[EventType.Emotion], query=range_query)
        request_builder = (
            CalendarHistogramAggregationAPIRequestInputBuilder()
            .with_query(query=EventQueryAPI(queries=[query_item]))
            .with_calendar_aggregation_type(CalendarAggregationType.WEEKDAYS)
            .with_fill_null_values(True)
            .with_field_name("rating")
            .with_aggregation_method(SimpleAggregationMethod.MAX)
        )

        # Act
        response = await _call_post_endpoint(
            request_url=AggregationEndpointUrls.CALENDAR_HISTOGRAM_AGGREGATION,
            headers=headers,
            json=request_builder.build_body_as_dict(),
        )
        assert response.status_code == status.HTTP_200_OK
        result = response.json()
        response_data = CalendarHistogramAggregationUseCaseOutputBoundary(**result)

        assert len(response_data.results) == 7  # 7 days

        monday_result = None
        monday_ratings = [emotion.rating for emotion in monday_emotions]
        for agg_result in response_data.results:
            if agg_result.aggregation_key == "Monday":
                monday_result = agg_result.value
        if monday_result is None:
            raise AssertionError("Monday aggregation result not found in aggregation result")

        assert max(monday_ratings) == monday_result

    async def test_calendar_histogram_aggregation_with_count_endpoint_should_pass(
        self,
        user_with_yearly_mood_ratings_return_monday: tuple[MemberUser, dict, list[Emotion]],
    ):
        # Arrange
        user, headers, monday_emotions = user_with_yearly_mood_ratings_return_monday
        gte_range = datetime(year=2024, month=1, day=1, hour=21, tzinfo=timezone.utc)
        lte_range = datetime(year=2024, month=12, day=31, hour=21, tzinfo=timezone.utc)

        range_query = LeafQueryAPIHelper.create_range_query(
            field_name=DocumentLabels.TIMESTAMP, gte=gte_range, lte=lte_range
        )
        query_item = EventTypedQueryAPI(types=[EventType.Emotion], query=range_query)
        request_builder = (
            CalendarHistogramAggregationAPIRequestInputBuilder()
            .with_query(query=EventQueryAPI(queries=[query_item]))
            .with_calendar_aggregation_type(CalendarAggregationType.WEEKDAYS)
            .with_fill_null_values(True)
            .with_aggregation_method(SimpleAggregationMethod.COUNT)
        )

        # Act
        response = await _call_post_endpoint(
            request_url=AggregationEndpointUrls.CALENDAR_HISTOGRAM_AGGREGATION,
            headers=headers,
            json=request_builder.build_body_as_dict(),
        )
        assert response.status_code == status.HTTP_200_OK
        result = response.json()
        response_data = CalendarHistogramAggregationUseCaseOutputBoundary(**result)

        assert len(response_data.results) == 7

        assert response_data.results[0].value == 53

    @pytest.fixture
    async def user_with_time_series_emotions(
        self,
        event_repo: EventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[tuple[MemberUser, dict, Sequence[Event]], Any]:
        """
        Creates a user and a sequence of emotion events with specific timestamps
        for testing time interval aggregation.
        """
        user, headers = await user_headers_factory()
        emotions = [
            EmotionBuilder()
            .with_owner_id(user.user_uuid)
            .with_timestamp(datetime(2023, 1, 1, 10, 0, 0, tzinfo=timezone.utc))
            .build(),
            EmotionBuilder()
            .with_owner_id(user.user_uuid)
            .with_timestamp(datetime(2023, 1, 1, 10, 0, 5, tzinfo=timezone.utc))
            .build(),
            EmotionBuilder()
            .with_owner_id(user.user_uuid)
            .with_timestamp(datetime(2023, 1, 1, 10, 0, 15, tzinfo=timezone.utc))
            .build(),
            EmotionBuilder()
            .with_owner_id(user.user_uuid)
            .with_timestamp(datetime(2023, 1, 1, 10, 0, 25, tzinfo=timezone.utc))
            .build(),
            EmotionBuilder()
            .with_owner_id(user.user_uuid)
            .with_timestamp(datetime(2023, 1, 1, 10, 0, 30, tzinfo=timezone.utc))
            .build(),
        ]

        inserted_emotions = await event_repo.insert(events=emotions, force_strong_consistency=True)

        yield user, headers, inserted_emotions

        await event_repo.delete_by_id(ids=[e.id for e in inserted_emotions])

    async def test_time_interval_aggregation_endpoint_passes(
        self, user_with_time_series_emotions: tuple[MemberUser, dict, Sequence[Emotion]]
    ):
        # Arrange
        user, headers, _ = user_with_time_series_emotions
        request_url = AggregationEndpointUrls.TIME_INTERVAL

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            headers=headers,
            json={"num_bins": 3, "bin_boundaries": None, "fill_null_values": True},
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        result = TimeIntervalAggregationAPIOutput(**response.json())
        assert result
        assert len(result.results) == 3

        # Expected time differences: [5, 10, 10, 5]
        # Bins will be automatically generated, e.g., [5.0-6.67], [6.67-8.33], [8.33-10.0]
        # Counts: [2, 0, 2]
        assert result.results[0].doc_count == 2
        assert result.results[1].doc_count == 0
        assert result.results[2].doc_count == 2

        assert result.standard_deviation is not None
        assert result.coefficient_of_variation is not None

    async def test_time_interval_aggregation_with_custom_boundaries_passes(
        self, user_with_time_series_emotions: tuple[MemberUser, dict, Sequence[Emotion]]
    ):
        # Arrange
        user, headers, _ = user_with_time_series_emotions
        request_url = AggregationEndpointUrls.TIME_INTERVAL
        custom_boundaries = [0, 6, 9, 11]

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            headers=headers,
            json={"bin_boundaries": custom_boundaries, "fill_null_values": True, "num_bins": None},
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        result = TimeIntervalAggregationAPIOutput(**response.json())
        assert len(result.results) == len(custom_boundaries) - 1

        # Time differences from fixture: [5, 10, 10, 5]
        # Bins with custom boundaries: [0-6), [6-9), [9-11)
        # 5 -> [0-6): count 2
        # 10 -> [9-11): count 2
        # Counts: [2, 0, 2]
        assert result.results[0].doc_count == 2
        assert result.results[1].doc_count == 0
        assert result.results[2].doc_count == 2

    async def test_time_interval_aggregation_no_documents_returns_no_content(
        self, user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]]
    ):
        # Arrange
        _, headers = await user_headers_factory()
        request_url = AggregationEndpointUrls.TIME_INTERVAL

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            headers=headers,
            json={"num_bins": 3, "bin_boundaries": None, "fill_null_values": True},
        )

        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT, f"Got response {response.content}"

    @pytest.mark.parametrize(
        "num_bins, bin_boundaries",
        [
            (None, None),
            (1, None),
            (None, [1.0, 2.0]),
            (None, [1.0, 3.0, 2.0]),
            (None, [1.0, 2.0, 2.0]),
            (5, [1.0, 2.0, 3.0]),
        ],
    )
    async def test_time_interval_aggregation_validation_error_raised(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        num_bins,
        bin_boundaries,
    ):
        # Arrange
        _, headers = await user_headers_factory()
        request_url = AggregationEndpointUrls.TIME_INTERVAL

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            headers=headers,
            json={
                "query": {},
                "num_bins": num_bins,
                "bin_boundaries": bin_boundaries,
                "fill_null_values": True,
            },
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, f"Got response {response.content}"
