import logging
from uuid import UUID

from fastapi import APIRouter, Body, Depends, File, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import NoContentException
from services.base.domain.annotated_types import NonEmptyStr
from services.data_service.api.constants import DataServicePrefixes, LookupEndpointRoutes
from services.data_service.api.models.request.content.content_lookup_request_input import (
    PostContentLookupRequestInput,
)
from services.data_service.application.use_cases.content.content_lookup_input_boundary import ContentLookupInputBoundary
from services.data_service.application.use_cases.content.content_lookup_use_case import (
    ContentLookupOutputBoundary,
    ContentLookupUseCase,
)
from services.data_service.application.use_cases.lookup.nutrition_provider.nutrition_ai_provider import (
    NutritionAIProvider,
)
from services.data_service.application.use_cases.lookup.nutrition_provider.nutrition_provider import (
    LookupProviderEnum,
    ThirdPartyProvider,
)
from services.data_service.application.use_cases.lookup.nutrition_third_party_use_case import (
    NutritionThirdPartyProviderUseCase,
)

lookup_router = APIRouter(
    prefix=DataServicePrefixes.V3 + DataServicePrefixes.LOOKUP_PREFIX,
    tags=["lookup"],
    responses={
        404: {"description": "Not found"},
    },
)


@lookup_router.post(
    LookupEndpointRoutes.BASE,
    responses={204: {"description": "Couldn't get content from URL", "model": None}},
)
async def post_content_lookup_old(
    _: UUID = Depends(get_current_uuid),
    body: PostContentLookupRequestInput = Body(...),
    use_case: ContentLookupUseCase = Injected(ContentLookupUseCase),
) -> ContentLookupOutputBoundary:
    try:
        return await use_case.execute(input_boundary=ContentLookupInputBoundary(url=body.url))
    except Exception as error:
        # For now let's make everything 204 on error until we do request url validation
        logging.exception(f"could not lookup url: {body.url}, error: {error}")
        raise NoContentException("could not lookup url")


@lookup_router.post(
    LookupEndpointRoutes.CONTENT_LOOKUP,
    responses={204: {"description": "Couldn't get content from URL", "model": None}},
)
async def post_content_lookup(
    _: UUID = Depends(get_current_uuid),
    body: PostContentLookupRequestInput = Body(...),
    use_case: ContentLookupUseCase = Injected(ContentLookupUseCase),
) -> ContentLookupOutputBoundary:
    try:
        return await use_case.execute(input_boundary=ContentLookupInputBoundary(url=body.url))
    except Exception as error:
        # For now let's make everything 204 on error until we do request url validation
        logging.exception(f"could not lookup url: {body.url}, error: {error}")
        raise NoContentException("could not lookup url")


@lookup_router.post(
    LookupEndpointRoutes.NUTRITION_THIRD_PARTY_LOOKUP,
    responses={204: {"description": "Could not lookup nutrition", "model": None}},
)
async def post_third_party_nutrition_lookup(
    _: UUID = Depends(get_current_uuid),
    name: NonEmptyStr = Query(),
    size: int = Query(default=5),
    provider: ThirdPartyProvider | None = Query(default=None),
    use_case: NutritionThirdPartyProviderUseCase = Injected(NutritionThirdPartyProviderUseCase),
):
    try:
        result = await use_case.lookup(
            name=name,
            provider=LookupProviderEnum(provider.value) if provider else None,
            size=size,
        )
        return result.documents
    except Exception as error:
        logging.exception(f"could not lookup nutrition: {name}, error: {error}")
        raise NoContentException("could not lookup nutrition")


@lookup_router.post(
    LookupEndpointRoutes.NUTRITION_AI_LOOKUP,
    responses={204: {"description": "Could not lookup nutrition", "model": None}},
)
async def post_ai_nutrition_lookup(
    _: UUID = Depends(get_current_uuid),
    name: NonEmptyStr = Query(),
    use_case: NutritionAIProvider = Injected(NutritionAIProvider),
):
    try:
        result = await use_case.lookup(name=name)
        return result.documents
    except Exception as error:
        logging.exception(f"could not lookup nutrition: {name}, error: {error}")
        raise NoContentException("could not lookup nutrition")


@lookup_router.post(
    LookupEndpointRoutes.NUTRITION_IMAGE_AI_LOOKUP,
    responses={204: {"description": "Could not lookup nutrition", "model": None}},
)
async def post_image_ai_nutrition_lookup(
    _: UUID = Depends(get_current_uuid),
    image_data: bytes = File(..., media_type="image/jpeg"),
    name: NonEmptyStr | None = Query(default=None),
    use_case: NutritionAIProvider = Injected(NutritionAIProvider),
):
    try:
        result = await use_case.lookup_image(image=image_data, name=name)
        return result.documents
    except Exception as error:
        logging.exception(f"could not lookup nutrition from image, error: {error}")
        raise NoContentException("could not lookup nutrition from image")


@lookup_router.post(
    LookupEndpointRoutes.NUTRITION_UPC_LOOKUP,
    responses={204: {"description": "Could not lookup nutrition", "model": None}},
)
async def post_upc_nutrition_lookup(
    _: UUID = Depends(get_current_uuid),
    upc: NonEmptyStr = Query(),
    provider: LookupProviderEnum | None = Query(default=None),
    use_case: NutritionThirdPartyProviderUseCase = Injected(NutritionThirdPartyProviderUseCase),
):
    try:
        result = await use_case.lookup_upc(upc=upc, provider=provider)
        return result.documents
    except Exception as error:
        logging.exception(f"could not lookup nutrition from upc, error: {error}")
        raise NoContentException("could not lookup nutrition from upc")
