import json
import random
from typing import Any, As<PERSON><PERSON>enerator, <PERSON>wai<PERSON>, Callable, Sequence
from uuid import UUID, uuid4

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.output.annotated_api_outputs import RecordAPIOutput
from services.base.api.responses.common_document_responses import CommonDocumentsIdsResponse, CommonDocumentsResponse
from services.base.domain.enums.metadata_v3 import InsertableOrigin, Origin
from services.base.domain.repository.record_repository import RecordRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.records.record import Record
from services.base.tests.domain.builders.records.record_builder import RecordBuilder
from services.base.type_resolver import TypeResolver
from services.data_service.api.models.request.records.delete_record_api_request_input import (
    DeleteRecordAPIRequestInput,
)
from services.data_service.api.models.request.records.insert_record_api_request_input import (
    InsertRecordAPIRequestInput,
)
from services.data_service.api.tests.common_rpc_calls import (
    _call_delete_endpoint,
    _call_post_endpoint,
)
from services.data_service.api.tests.record_assert_helpers import RecordAssertHelpers
from services.data_service.api.urls import RecordEndpointUrls
from services.data_service.application.builders.event_metadata_input_builder import EventMetadataInputBuilder
from services.data_service.application.builders.record.insert_record_input_builder import (
    InsertRecordInputBuilder,
)


class TestRecordEndpoints:
    @pytest.fixture
    async def user_with_records(
        self, record_repo: RecordRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> AsyncGenerator[tuple[Sequence[Record], MemberUser], Any]:
        user: MemberUser = await user_factory()
        random_input_origin = random.choice([Origin(o.value) for o in InsertableOrigin])
        records = (
            RecordBuilder().with_owner_id(owner_id=user.user_uuid).with_origin(origin=random_input_origin).build_all()
        )

        inserted_records = await record_repo.insert(records=records, force_strong_consistency=True)

        yield inserted_records, user

        # Teardown
        await record_repo.delete_by_id(ids=[r.id for r in inserted_records])

    async def test_insert_simple_records_endpoint_passes(
        self,
        user_headers_factory,
        record_repo: RecordRepository,
    ):
        # Arrange
        _, headers = await user_headers_factory()
        input_data = InsertRecordInputBuilder().build_all()
        input_metadata = EventMetadataInputBuilder().build()
        request_input = InsertRecordAPIRequestInput(documents=input_data, metadata=input_metadata)

        # Act
        response = await _call_post_endpoint(
            request_url=RecordEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[RecordAPIOutput](**response.json())
        docs = payload.documents
        assert docs
        assert len(docs) == len(input_data)
        _ = RecordAssertHelpers.assert_records(returned_records=docs, expected_records=input_data)

        # Teardown
        await record_repo.delete_by_id(ids=[d.id for d in docs])

    # async def test_update_record_endpoint_passes(
    #     self,
    #     user_with_records: tuple[Sequence[TypeResolver.RECORD_UNION], MemberUser],
    #     record_repo: RecordRepository,
    # ):
    #     # Arrange
    #     existing_records, user = user_with_records
    #     headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
    #
    #     records_to_update = [
    #         UpdateRecordInputBuilder().with_id(id=e.id).with_type_id(type_id=e.type_id()).build()
    #         for e in existing_records
    #     ]
    #
    #     request_input = UpdateRecordAPIRequestInput(documents=records_to_update)
    #
    #     # Act
    #     response = await _call_patch_endpoint(
    #         request_url=RecordEndpointUrls.BASE,
    #         headers=headers,
    #         json=json.loads(request_input.model_dump_json(by_alias=True)),
    #         retry=False,
    #     )
    #
    #     # Assert
    #     assert response.status_code == status.HTTP_200_OK, response.json()
    #
    #     payload = CommonDocumentsResponse[RecordAPIOutput](**response.json())
    #     updated_records = payload.documents
    #     assert len(updated_records) == len(existing_records)
    #     self._assert_records(returned_records=updated_records, expected_records=records_to_update)
    #
    #     # Teardown
    #     await record_repo.delete_by_id([(e.id, TypeResolver.get_record(e.type)) for e in updated_records])
    #
    # async def test_update_record_endpoint_not_found_raises_bad_request(
    #     self,
    #     user_with_records: tuple[Sequence[TypeResolver.RECORD_UNION], MemberUser],
    #     record_repo: RecordRepository,
    # ):
    #     # Arrange
    #     existing_records, user = user_with_records
    #     headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
    #
    #     records_to_update = [
    #         UpdateRecordInputBuilder().with_id(id=e.id).with_type_id(type_id=e.type_id()).build()
    #         for e in existing_records
    #     ]
    #     # change single id
    #     records_to_update[0].id = uuid4()
    #
    #     request_input = UpdateRecordAPIRequestInput(documents=records_to_update)
    #
    #     # Act
    #     response = await _call_patch_endpoint(
    #         request_url=RecordEndpointUrls.BASE,
    #         headers=headers,
    #         json=json.loads(request_input.model_dump_json(by_alias=True)),
    #         retry=False,
    #     )
    #
    #     # Assert
    #     assert response.status_code == status.HTTP_400_BAD_REQUEST, response.json()
    #
    # async def test_update_record_endpoint_different_owner_raises_forbidden(
    #     self,
    #     user_with_records: tuple[Sequence[TypeResolver.RECORD_UNION], MemberUser],
    #     record_repo: RecordRepository,
    # ):
    #     # Arrange
    #     existing_records, user = user_with_records
    #     headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=uuid4())}"}
    #
    #     records_to_update = [
    #         UpdateRecordInputBuilder().with_id(id=e.id).with_type_id(type_id=e.type_id()).build()
    #         for e in existing_records
    #     ]
    #
    #     request_input = UpdateRecordAPIRequestInput(documents=records_to_update)
    #
    #     # Act
    #     response = await _call_patch_endpoint(
    #         request_url=RecordEndpointUrls.BASE,
    #         headers=headers,
    #         json=json.loads(request_input.model_dump_json(by_alias=True)),
    #         retry=False,
    #     )
    #
    #     # Assert
    #     assert response.status_code == status.HTTP_403_FORBIDDEN, response.json()

    async def test_delete_record_different_owner_raises_forbidden(
        self,
        user_with_records: tuple[Sequence[TypeResolver.RECORD_UNION], MemberUser],
        record_repo: RecordRepository,
    ):
        # Arrange
        existing_records, user = user_with_records
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=uuid4())}"}

        request_input = DeleteRecordAPIRequestInput(ids=[e.id for e in existing_records])

        # Act
        response = await _call_delete_endpoint(
            request_url=RecordEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN, response.json()

    async def test_delete_record_not_found_raises_bad_request(
        self,
        user_with_records: tuple[Sequence[TypeResolver.RECORD_UNION], MemberUser],
        record_repo: RecordRepository,
    ):
        # Arrange
        existing_records, user = user_with_records
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # change single id to a non-existent one
        record_ids = [e.id for e in existing_records]
        new_id = uuid4()
        new_id = UUID(record_ids[0].hex[0:4] + new_id.hex[4:])
        record_ids[0] = new_id
        request_input = DeleteRecordAPIRequestInput(ids=record_ids)

        # Act
        response = await _call_delete_endpoint(
            request_url=RecordEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.json()

    async def test_delete_record_endpoint_passes(
        self,
        user_with_records: tuple[Sequence[TypeResolver.RECORD_UNION], MemberUser],
        record_repo: RecordRepository,
    ):
        # Arrange
        existing_records, user = user_with_records
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        request_input = DeleteRecordAPIRequestInput(ids=[e.id for e in existing_records])

        # Act
        response = await _call_delete_endpoint(
            request_url=RecordEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.json()
        payload = CommonDocumentsIdsResponse(**response.json())
        assert sorted(payload.document_ids) == sorted([record.id for record in existing_records])

        assert not await record_repo.search_by_id(ids=[e.id for e in existing_records])
