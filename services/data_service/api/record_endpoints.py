from uuid import UUI<PERSON>

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.output.annotated_api_outputs import RecordAPIOutput
from services.base.api.output.mappers.record_api_output_mapper import RecordAPIOutputMapper
from services.base.api.responses.common_document_responses import CommonDocumentsIdsResponse, CommonDocumentsResponse
from services.base.api.validators import validate_input_size
from services.base.application.exceptions import (
    BadRequestException,
    DuplicateDocumentsFound,
    IncorrectOperationException,
)
from services.data_service.api.constants import DataServicePrefixes, RecordEndpointRoutes
from services.data_service.api.models.request.records.delete_record_api_request_input import (
    DeleteRecordAPIRequestInput,
)
from services.data_service.api.models.request.records.insert_record_api_request_input import (
    InsertRecordAPIRequestInput,
)
from services.data_service.application.use_cases.records.delete_record_by_id_use_case import DeleteRecordByIdUseCase
from services.data_service.application.use_cases.records.insert_record_use_case import InsertRecordUseCase
from services.data_service.application.use_cases.records.models.delete_record_input_boundary import (
    DeleteRecordInputBoundary,
)
from services.data_service.application.use_cases.records.models.insert_record_input_boundary import (
    InsertRecordInputBoundary,
)

record_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.RECORD}",
    tags=["record"],
    responses={404: {"description": "Not found"}},
    dependencies=[Depends(validate_input_size)],
)


@record_router.post(RecordEndpointRoutes.BASE)
async def insert_record_endpoint(
    request_input: InsertRecordAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: InsertRecordUseCase = Injected(InsertRecordUseCase),
) -> CommonDocumentsResponse[RecordAPIOutput]:
    try:
        records = await use_case.execute_async(
            boundary=InsertRecordInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [RecordAPIOutputMapper.map(record) for record in records]
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="record duplicates found in the input payload") from err

    return CommonDocumentsResponse[RecordAPIOutput](documents=documents_api_output)


# TODO: delete if not needed
# @record_router.patch(RecordEndpointRoutes.BASE)
# async def update_record_endpoint(
#     request_input: UpdateRecordAPIRequestInput = Body(...),
#     owner_id: UUID = Depends(get_current_uuid),
#     use_case: UpdateRecordUseCase = Injected(UpdateRecordUseCase),
# ) -> CommonDocumentsResponse[RecordAPIOutput]:
#     try:
#         records = await use_case.execute_async(
#             boundary=UpdateRecordInputBoundary.map(model=request_input), owner_id=owner_id
#         )
#         documents_api_output = [RecordAPIOutputMapper.map(record) for record in records]
#     except IncorrectOperationException as err:
#         raise BadRequestException(message=err.message) from err
#     except DuplicateDocumentsFound as err:
#         raise BadRequestException(message="record duplicates found in the input payload") from err
#
#     return CommonDocumentsResponse[RecordAPIOutput](documents=documents_api_output)


@record_router.delete(
    RecordEndpointRoutes.BASE,
    summary="Delete Records",
    description="""
    Permanently delete records from the user's personal data collection.
    """,
    response_description="Successfully deleted records with list of removed record IDs",
    responses={
        200: {
            "description": "Records successfully deleted",
            "content": {
                "application/json": {
                    "example": {
                        "document_ids": [
                            "123e4567-e89b-12d3-a456-************",
                            "456e7890-e89b-12d3-a456-************",
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - incorrect operation or validation errors",
            "content": {
                "application/json": {
                    "examples": {
                        "incorrect_operation": {
                            "summary": "Incorrect operation",
                            "value": {"detail": "Record not found"},
                        },
                        "validation_error": {
                            "summary": "Validation error",
                            "value": {"detail": "Record IDs are required"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
    },
)
async def delete_record_endpoint(
    request_input: DeleteRecordAPIRequestInput = Body(
        ...,
        description="Record deletion request containing record IDs",
        example={
            "ids": [
                "123e4567-e89b-12d3-a456-************",
                "456e7890-e89b-12d3-a456-************",
            ]
        },
    ),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: DeleteRecordByIdUseCase = Injected(DeleteRecordByIdUseCase),
) -> CommonDocumentsIdsResponse:
    try:
        deleted_uuids = await use_case.execute_async(
            boundary=DeleteRecordInputBoundary.map(model=request_input), owner_id=owner_id
        )
        return CommonDocumentsIdsResponse(document_ids=deleted_uuids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
