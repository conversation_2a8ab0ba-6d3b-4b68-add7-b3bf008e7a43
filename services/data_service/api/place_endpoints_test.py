import json
from typing import AsyncGenerator, Sequence

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.output.place.place_api_output import PlaceAPIOutput
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.utils.urls import join_as_url
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.place_repository import PlaceRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.place import Place, PlaceFields
from services.base.tests.domain.builders.place_builder import PlaceBuilder
from services.data_service.api.builders.insert_place_input_builder import InsertPlaceInputBuilder
from services.data_service.api.models.request.place.insert_place_api_request_input import (
    InsertPlaceAPIRequestInput,
)
from services.data_service.api.models.request.place.update_place_api_request_input import (
    UpdatePlaceAPIRequestInput,
)
from services.data_service.api.tests.common_rpc_calls import (
    _call_patch_endpoint,
    _call_post_endpoint,
)
from services.data_service.api.tests.utils import TestUtils
from services.data_service.api.urls import PlaceEndpointUrls
from services.data_service.application.use_cases.place.models.insert_place_input_boundary import (
    InsertPlaceInput,
)
from services.data_service.application.use_cases.place.models.update_place_input_boundary import (
    UpdatePlaceInput,
)
from services.data_service.conftest import UserFactoryCallable


class TestPlaceCRUD:
    @pytest.fixture
    async def user_with_places(
        self,
        place_repo: PlaceRepository,
        user_factory: UserFactoryCallable,
    ) -> AsyncGenerator[tuple[Sequence[Place], MemberUser]]:
        user: MemberUser = await user_factory()
        places = [
            PlaceBuilder().with_owner_id(owner_id=user.user_uuid).build()
            for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))
        ]
        inserted_places = await place_repo.insert(places=places, force_strong_consistency=True)

        yield inserted_places, user

        # Teardown
        await place_repo.delete_by_id(ids=[p.id for p in inserted_places])

    async def test_insert_places_passes(self, user_with_places, place_repo: PlaceRepository):
        # Arrange
        existing_places, user = user_with_places
        load_place_request = InsertPlaceAPIRequestInput(
            documents=[InsertPlaceInputBuilder().build() for _ in range(1, 10)]
        )
        body_dict = json.loads(load_place_request.model_dump_json(by_alias=True))
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(
            request_url=PlaceEndpointUrls.BASE, json=body_dict, headers=headers, retry=False
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        response_model = CommonDocumentsResponse[PlaceAPIOutput](**response.json())
        assert len(response_model.documents) == len(load_place_request.documents)

        for place, expected_place in zip(
            sorted(response_model.documents, key=lambda p: p.name),
            sorted(load_place_request.documents, key=lambda p: p.name),
        ):
            assert place.name == expected_place.name
            assert place.category == expected_place.category
            assert place.tags == expected_place.tags
            assert TestUtils.is_date_within_one_minute(place.system_properties.created_at)

        # Teardown
        await place_repo.delete_by_id([p.id for p in response_model.documents])

    async def test_insert_duplicated_places_raises(self, user_with_places):
        # Arrange
        existing_places, user = user_with_places
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(
            request_url=PlaceEndpointUrls.BASE,
            json=json.loads(
                InsertPlaceAPIRequestInput(
                    documents=[InsertPlaceInput(**p.model_dump()) for p in existing_places]
                ).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"response content: {response.content}"

    async def test_archive_places_passes(self, user_with_places):
        # Arrange
        existing_places, user = user_with_places
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_url = join_as_url(
            base_url=PlaceEndpointUrls.ARCHIVE, query_params={"place_ids": [p.id for p in existing_places]}
        )

        # Act
        response = await _call_patch_endpoint(request_url=request_url, headers=headers, json=None, retry=False)

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        archived_places = CommonDocumentsResponse[PlaceAPIOutput](**response.json()).documents
        for place in archived_places:
            archived_at = place.archived_at
            assert archived_at
            assert TestUtils.is_date_within_one_minute(date=archived_at)

    async def test_search_places_endpoint_passes(self, user_with_places):
        # Arrange
        existing_places, user = user_with_places
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(
            request_url=PlaceEndpointUrls.SEARCH,
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        actual_places = CommonDocumentsResponse[PlaceAPIOutput](**response.json()).documents
        assert len(actual_places) == len(existing_places)

        expected_place: Place
        place: PlaceAPIOutput
        for place, expected_place in zip(
            sorted(actual_places, key=lambda p: p.id), sorted(existing_places, key=lambda p: p.id)
        ):
            assert place.id == expected_place.id
            assert place.name == expected_place.name
            assert place.category == expected_place.category
            assert place.tags == expected_place.tags
            assert place.system_properties.created_at == expected_place.system_properties.created_at

    async def test_update_places_by_id_passes(self, user_with_places):
        # Arrange
        existing_places, user = user_with_places
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        input_places = []

        for place in existing_places:
            input_place = Place(
                **place.model_dump(by_alias=True)
                | {
                    PlaceFields.NAME: PrimitiveTypesGenerator.generate_random_string(),
                    PlaceFields.NOTE: PrimitiveTypesGenerator.generate_random_string(),
                    DocumentLabels.TAGS: [
                        PrimitiveTypesGenerator.generate_random_string()
                        for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=3))
                    ],
                }
            )

            input_places.append(input_place)

        # Act
        response = await _call_patch_endpoint(
            request_url=PlaceEndpointUrls.BASE,
            json=json.loads(
                UpdatePlaceAPIRequestInput(
                    documents=[UpdatePlaceInput(**p.model_dump()) for p in input_places]
                ).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        updated_places = CommonDocumentsResponse[PlaceAPIOutput](**response.json()).documents

        input_place: Place
        updated_place: PlaceAPIOutput
        for input_place, updated_place in zip(
            sorted(input_places, key=lambda p: p.id), sorted(updated_places, key=lambda p: p.id)
        ):
            updated_at = updated_place.system_properties.updated_at
            assert updated_at
            assert TestUtils.is_date_within_one_minute(updated_at)
            assert updated_place.name == input_place.name
            assert updated_place.category == input_place.category
            assert updated_place.tags == input_place.tags
            assert updated_place.system_properties.created_at == input_place.system_properties.created_at

    async def test_update_places_by_id_when_duplication_fields_do_not_change_passes(self, user_with_places):
        # Arrange
        existing_places, user = user_with_places
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        input_places = []

        for place in existing_places:
            input_place = Place(
                **place.model_dump(by_alias=True)
                | {DocumentLabels.TAGS: PrimitiveTypesGenerator.generate_random_tags()}
            )
            input_places.append(input_place)

        # Act
        response = await _call_patch_endpoint(
            request_url=PlaceEndpointUrls.BASE,
            json=json.loads(
                UpdatePlaceAPIRequestInput(
                    documents=[UpdatePlaceInput(**p.model_dump()) for p in input_places]
                ).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        updated_places = CommonDocumentsResponse[PlaceAPIOutput](**response.json()).documents

        input_place: Place
        updated_place: PlaceAPIOutput
        for input_place, updated_place in zip(
            sorted(input_places, key=lambda p: p.id), sorted(updated_places, key=lambda p: p.id)
        ):
            updated_at = updated_place.system_properties.updated_at
            assert updated_at
            assert TestUtils.is_date_within_one_minute(updated_at)
            assert updated_place.name == input_place.name
            assert updated_place.category == input_place.category
            assert updated_place.tags == input_place.tags
            assert updated_place.system_properties.created_at == input_place.system_properties.created_at

    async def test_update_places_by_id_when_update_makes_duplicate_raises(
        self, place_repo: PlaceRepository, user_with_places
    ):
        # Arrange
        existing_places, user = user_with_places

        places = PlaceBuilder().with_owner_id(owner_id=user.user_uuid).build_n(n=2)
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        inserted_places = await place_repo.insert(places=places, force_strong_consistency=True)

        # Switch place id and update content of one to be duplicate of the other
        updated_place = UpdatePlaceInput.map(
            model=inserted_places[0], fields={DocumentLabels.ID: inserted_places[1].id}
        )

        # Act
        response = await _call_patch_endpoint(
            request_url=PlaceEndpointUrls.BASE,
            json=json.loads(UpdatePlaceAPIRequestInput(documents=[updated_place]).model_dump_json()),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"response content: {response.content}"

        # Teardown
        await place_repo.delete_by_id(ids=[p.id for p in inserted_places])
