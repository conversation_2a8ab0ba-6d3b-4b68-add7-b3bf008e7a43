from typing import Sequence, cast
from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, <PERSON>, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.output.annotated_api_outputs import EventV3APIOutput
from services.base.api.output.mappers.event_api_output_mapper import EventAP<PERSON>utputMapper
from services.base.api.output.mappers.eventv3_api_output_mapper import EventV3<PERSON><PERSON>utputMapper
from services.base.api.responses.common_document_responses import CommonDocumentsIdsResponse, CommonDocumentsResponse
from services.base.api.validators import validate_input_size
from services.base.application.exceptions import (
    BadRequestException,
    DuplicateDocumentsFound,
    IncorrectOperationException,
)
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.shared_v2 import DeprEventModel
from services.data_service.api.constants import DataServicePrefixes, EventEndpointRoutes
from services.data_service.api.models.request.event.delete_event_api_request_input import DeleteEventAPIRequestInput
from services.data_service.api.models.request.event.fetch_event_by_id_api_request_input import (
    FetchEventByIdAPIRequestInput,
)
from services.data_service.api.models.request.event.insert_event_api_request_input import InsertEventAPIRequestInput
from services.data_service.api.models.request.event.modify_event_assets_api_request_input import (
    ModifyEventAssetsAPIRequestInput,
)
from services.data_service.api.models.request.event.reorder_event_group_api_request_input import (
    ReorderEventGroupAPIRequestInput,
)
from services.data_service.api.models.request.event.update_event_api_request_input import UpdateEventAPIRequestInput
from services.data_service.api.models.request.feed.event_feed_api_request_input import EventFeedAPIRequestInput
from services.data_service.api.models.response.feed.event_feed_api_response import EventFeedAPIResponse
from services.data_service.api.serializers.continuation_token_marshaller import ContinuationTokenMarshaller
from services.data_service.application.use_cases.events.delete_event_by_id_use_case import DeleteEventByIdUseCase
from services.data_service.application.use_cases.events.fetch_event_by_id_use_case import FetchEventByIdUseCase
from services.data_service.application.use_cases.events.insert_event_use_case import InsertEventUseCase
from services.data_service.application.use_cases.events.models.delete_event_input_boundary import (
    DeleteEventInputBoundary,
)
from services.data_service.application.use_cases.events.models.fetch_event_by_id_input_boundary import (
    FetchEventByIdInputBoundary,
)
from services.data_service.application.use_cases.events.models.insert_event_input_boundary import (
    InsertEventInputBoundary,
)
from services.data_service.application.use_cases.events.models.modify_event_assets_input_boundary import (
    ModifyEventAssetsInputBoundary,
)
from services.data_service.application.use_cases.events.models.reorder_event_group_input_boundary import (
    ReorderEventGroupInputBoundary,
)
from services.data_service.application.use_cases.events.models.update_event_input_boundary import (
    UpdateEventInputBoundary,
)
from services.data_service.application.use_cases.events.modify_event_assets_use_case import ModifyEventAssetsUseCase
from services.data_service.application.use_cases.events.reorder_event_group_use_case import ReorderEventGroupUseCase
from services.data_service.application.use_cases.events.update_event_use_case import UpdateEventUseCase
from services.data_service.application.use_cases.feed.document_feed_input_boundary import DocumentFeedInputBoundary
from services.data_service.application.use_cases.feed.document_feed_use_case import DocumentFeedUseCase

event_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.EVENT}",
    tags=["event"],
    responses={404: {"description": "Not found"}},
    dependencies=[Depends(validate_input_size)],
)


@event_router.post(
    EventEndpointRoutes.FEED,
    response_model=EventFeedAPIResponse,
    summary="Event Feed: Paginable stream of event documents",
    description="""
    Provides an efficient way to iterate through a user's event history
    with support for filtering and pagination. Events are returned in reverse chronological
    order with continuation tokens for seamless pagination.
    """,
    response_description="Paginated list of events with continuation token for next page",
    responses={
        200: {
            "description": "Events retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "items": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Morning workout",
                                "timestamp": "2024-01-15T08:30:00Z",
                                "type": "exercise",
                            }
                        ],
                        "continuation_token": "eyJsYXN0X3RpbWVzdGFtcCI6IjIwMjQtMDEtMTVUMDg6MzA6MDBaIn0=",
                    }
                }
            },
        },
        400: {"description": "Bad request - invalid filter parameters"},
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request parameters"},
    },
)
async def event_feed_endpoint(
    document_feed_use_case: DocumentFeedUseCase = Injected(DocumentFeedUseCase),
    user_uuid: UUID = Depends(get_current_uuid),
    input_boundary: DocumentFeedInputBoundary = Depends(EventFeedAPIRequestInput.to_input_boundary),
):
    try:
        result = await document_feed_use_case.execute_async(user_uuid=user_uuid, input_boundary=input_boundary)
        return EventFeedAPIResponse(
            items=[EventAPIOutputMapper.map(document=e) for e in cast(Sequence[Event | DeprEventModel], result.events)],
            continuation_token=ContinuationTokenMarshaller.encode_feed_continuation_token(result.continuation_token),
        )
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@event_router.post(
    EventEndpointRoutes.BASE,
    summary="Create Events",
    description="""
    Allows users to insert one or more events into their personal data collection.
    Events can be of various types including symptoms, medications, exercises, notes, sleep records,
    and more. Each event must include a name, timestamp, and type-specific data.
    """,
    response_description="Successfully created events with generated IDs and system properties",
    responses={
        200: {
            "description": "Events successfully created",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Morning headache",
                                "timestamp": "2024-01-15T08:30:00Z",
                                "type": "symptom",
                                "category": "headache",
                                "rating": 6,
                                "body_parts": ["head"],
                                "tags": ["morning", "stress"],
                                "system_properties": {
                                    "created_at": "2024-01-15T08:30:00Z",
                                    "updated_at": "2024-01-15T08:30:00Z",
                                },
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - duplicate events or validation errors",
            "content": {
                "application/json": {
                    "examples": {
                        "duplicate_events": {
                            "summary": "Duplicate events",
                            "value": {"detail": "event duplicates found in the input payload"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
    },
)
async def insert_event_endpoint(
    request_input: InsertEventAPIRequestInput = Body(
        ...,
        description="Event creation request containing one or more events to insert",
        example={
            "documents": [
                {
                    "name": "Morning headache",
                    "timestamp": "2024-01-15T08:30:00Z",
                    "type": "symptom",
                    "category": "headache",
                    "rating": 6,
                    "body_parts": ["head"],
                    "tags": ["morning", "stress"],
                    "note": "Woke up with a throbbing headache",
                }
            ]
        },
    ),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: InsertEventUseCase = Injected(InsertEventUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    try:
        events = await use_case.execute_async(
            boundary=InsertEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.patch(
    EventEndpointRoutes.BASE,
    summary="Update Events",
    description="""
    Update existing events with new information while preserving their identity and system properties.
    """,
    response_description="Successfully updated events with modified properties",
    responses={
        200: {
            "description": "Events successfully updated",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Severe morning headache",
                                "timestamp": "2024-01-15T08:30:00Z",
                                "type": "symptom",
                                "category": "headache",
                                "rating": 8,
                                "body_parts": ["head"],
                                "tags": ["morning", "stress", "severe"],
                                "system_properties": {
                                    "created_at": "2024-01-15T08:30:00Z",
                                    "updated_at": "2024-01-15T09:15:00Z",
                                },
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - validation errors or incorrect operation",
            "content": {
                "application/json": {
                    "examples": {
                        "incorrect_operation": {
                            "summary": "Incorrect operation",
                            "value": {"detail": "Event not found or type mismatch"},
                        },
                        "duplicate_events": {
                            "summary": "Duplicate events",
                            "value": {"detail": "event duplicates found in the input payload"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
    },
)
async def update_event_endpoint(
    request_input: UpdateEventAPIRequestInput = Body(
        ...,
        description="Event update request containing event IDs and updated properties",
        example={
            "documents": [
                {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "name": "Severe morning headache",
                    "timestamp": "2024-01-15T08:30:00Z",
                    "type": "symptom",
                    "category": "headache",
                    "rating": 8,
                    "body_parts": ["head"],
                    "tags": ["morning", "stress", "severe"],
                }
            ]
        },
    ),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: UpdateEventUseCase = Injected(UpdateEventUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    try:
        events = await use_case.execute_async(
            boundary=UpdateEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.patch(
    EventEndpointRoutes.MODIFY_ASSETS,
    summary="Modify Event Assets",
    description="""
    Modify asset attachments for existing events by adding, removing, or updating file attachments.
    
    **Supported Asset Types:**
    - **Images**: JPEG
    """,
    response_description="Successfully updated events with modified asset references and metadata",
    responses={
        200: {
            "description": "Event assets successfully modified",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Morning headache",
                                "timestamp": "2024-01-15T08:30:00Z",
                                "type": "symptom",
                                "assets": [
                                    {
                                        "asset_id": "asset_123",
                                        "filename": "headache_photo.jpg",
                                        "content_type": "image/jpeg",
                                        "size": 245760,
                                    }
                                ],
                                "system_properties": {
                                    "created_at": "2024-01-15T08:30:00Z",
                                    "updated_at": "2024-01-15T09:15:00Z",
                                },
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - validation errors or incorrect operation",
            "content": {
                "application/json": {
                    "examples": {
                        "incorrect_operation": {
                            "summary": "Incorrect operation",
                            "value": {"detail": "Event not found or type mismatch"},
                        },
                        "asset_too_large": {
                            "summary": "Asset too large",
                            "value": {"detail": "Asset size exceeds 10MB limit"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
    },
)
async def modify_event_assets_endpoint(
    request_input: ModifyEventAssetsAPIRequestInput = Body(
        ...,
        description="Event asset modification request containing event IDs and asset operations",
        example={
            "documents": [
                {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "type": "symptom",
                    "assets": [
                        {
                            "asset_type": "image",
                            "data": "base64-encoded-image-data...",
                            "filename": "headache_photo.jpg",
                        }
                    ],
                }
            ]
        },
    ),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: ModifyEventAssetsUseCase = Injected(ModifyEventAssetsUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    try:
        events = await use_case.execute_async(
            boundary=ModifyEventAssetsInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.delete(
    EventEndpointRoutes.BASE,
    summary="Delete Events",
    description="""
    Permanently delete events from the user's personal data collection. All associated assets and metadata
    are also removed.
    """,
    response_description="Successfully deleted events with list of removed event IDs",
    responses={
        200: {
            "description": "Events successfully deleted",
            "content": {
                "application/json": {
                    "example": {
                        "document_ids": [
                            "123e4567-e89b-12d3-a456-************",
                            "456e7890-e89b-12d3-a456-************",
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - incorrect operation or validation errors",
            "content": {
                "application/json": {
                    "examples": {
                        "incorrect_operation": {
                            "summary": "Incorrect operation",
                            "value": {"detail": "Event not found"},
                        },
                        "validation_error": {
                            "summary": "Validation error",
                            "value": {"detail": "Event IDs are required"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
    },
)
async def delete_event_endpoint(
    request_input: DeleteEventAPIRequestInput = Body(
        ...,
        description="Event deletion request containing event IDs",
        example={
            "ids": [
                "123e4567-e89b-12d3-a456-************",
                "456e7890-e89b-12d3-a456-************",
            ]
        },
    ),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: DeleteEventByIdUseCase = Injected(DeleteEventByIdUseCase),
) -> CommonDocumentsIdsResponse:
    try:
        deleted_uuids = await use_case.execute_async(
            boundary=DeleteEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        return CommonDocumentsIdsResponse(document_ids=deleted_uuids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@event_router.post(EventEndpointRoutes.BY_ID)
async def fetch_event_by_id_endpoint(
    request_input: FetchEventByIdAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: FetchEventByIdUseCase = Injected(FetchEventByIdUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    try:
        events = await use_case.execute_async(
            boundary=FetchEventByIdInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
        return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@event_router.patch(
    EventEndpointRoutes.GROUP_REORDER,
    summary="Reorder Event Group Child Events",
    description="""
    Reorder the child events within an event group by providing the desired order of child event IDs.
    The order is determined by the position of each ID in the provided list.
    
    **Usage:**
    - Provide a list of child event IDs in the desired order
    - All existing child IDs must be included in the request
    - The group will be updated with the new order
    """,
    response_description="Successfully reordered event group with updated child_ids",
    responses={
        200: {
            "description": "Event group successfully reordered",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "child_ids": [
                                    "456e7890-e89b-12d3-a456-************",
                                    "789e1234-e89b-12d3-a456-************",
                                ],
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - validation errors or incorrect operation",
            "content": {
                "application/json": {
                    "examples": {
                        "group_not_found": {
                            "summary": "Group not found",
                            "value": {"detail": "Event group not found"},
                        },
                        "child_ids_mismatch": {
                            "summary": "Child IDs mismatch",
                            "value": {
                                "detail": "Child IDs mismatch. Missing child IDs: ['id1']; Unknown child IDs: ['id2']"
                            },
                        },
                        "not_event_group": {
                            "summary": "Not an event group",
                            "value": {"detail": "Document is not an event group"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
    },
)
async def reorder_event_group_endpoint(
    request_input: ReorderEventGroupAPIRequestInput = Body(
        ...,
        description="Event group reorder request containing group ID and desired child order",
        example={
            "id": "123e4567-e89b-12d3-a456-************",
            "child_ids": [
                "456e7890-e89b-12d3-a456-************",
                "789e1234-e89b-12d3-a456-************",
            ],
        },
    ),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: ReorderEventGroupUseCase = Injected(ReorderEventGroupUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    try:
        updated_group = await use_case.execute_async(
            boundary=ReorderEventGroupInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(updated_group)]
        return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
