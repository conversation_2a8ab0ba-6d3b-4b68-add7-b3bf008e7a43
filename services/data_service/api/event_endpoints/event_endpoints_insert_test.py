import json
import random
from typing import Any, AsyncGenerator, Awaitable, Callable, Sequence

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.output.annotated_api_outputs import EventV3APIOutput, GroupV3APIOutput
from services.base.api.output.events.event_v3_api_output import PersonAPIOutput
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import InsertableOrigin, Origin
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.contact import Contact
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.domain.schemas.member_user.member_user import Member<PERSON>ser
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.tests.domain.builders.contact_builder import ContactBuilder
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.data_service.api.models.request.event.insert_event_api_request_input import InsertEventAPIRequestInput
from services.data_service.api.tests.common_rpc_calls import (
    _call_post_endpoint,
)
from services.data_service.api.tests.event_assert_helpers import EventAssertHelpers
from services.data_service.api.urls import EventEndpointUrls
from services.data_service.application.builders.event.insert.insert_person_input_builder import InsertPersonInputBuilder
from services.data_service.application.builders.event.insert.insertable_event_input_builder import (
    InsertableEventInputBuilder,
)
from services.data_service.application.builders.event.insert.insertable_group_input_builder import (
    InsertableGroupInputBuilder,
)
from services.data_service.application.builders.event_input_asset_builder import EventInputAssetBuilder
from services.data_service.application.builders.event_metadata_input_builder import EventMetadataInputBuilder
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs
from services.data_service.application.use_cases.events.models.insert_event_group_input import InsertEventGroupInput


class TestInsertEventEndpoints:
    @pytest.fixture
    async def user_with_events(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> AsyncGenerator[tuple[Sequence[Event], MemberUser], Any]:
        user: MemberUser = await user_factory()
        random_input_origin = random.choice([Origin(o.value) for o in InsertableOrigin])
        events = (
            EventBuilder().with_owner_id(owner_id=user.user_uuid).with_origin(origin=random_input_origin).build_all()
        )

        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, user

        # Teardown
        await event_repo.delete_by_id(ids=[e.id for e in inserted_events])

    async def test_insert_simple_events_endpoint_passes(
        self,
        user_headers_factory,
        event_repo: EventRepository,
    ):
        # Arrange
        _, headers = await user_headers_factory()
        input_data = InsertableEventInputBuilder().should_build_groups(should_build=False).build_all()

        input_metadata = EventMetadataInputBuilder().build()
        request_input = InsertEventAPIRequestInput(
            documents=input_data,
            metadata=input_metadata,
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert docs
        assert len(docs) == len(input_data)

        EventAssertHelpers.assert_events(returned_events=docs, expected_events=input_data)
        EventAssertHelpers.assert_metadata(input_metadata=input_metadata, output_events=docs)
        # Teardown
        await event_repo.delete_by_id(ids=[d.id for d in docs])

    async def test_insert_events_with_assets_endpoint_passes(
        self,
        user_headers_factory,
        event_repo: EventRepository,
    ):
        # Arrange
        _, headers = await user_headers_factory()
        input_data = [
            InsertableEventInputBuilder()
            .with_assets(EventInputAssetBuilder().build_n(n=random.randint(0, 1)))
            .should_build_groups(should_build=False)
            .build()
            for _ in range(random.randint(1, 3))
        ]

        input_metadata = EventMetadataInputBuilder().build()
        request_input = InsertEventAPIRequestInput(
            documents=input_data,
            metadata=input_metadata,
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert docs
        assert len(docs) == len(input_data)

        EventAssertHelpers.assert_events(returned_events=docs, expected_events=input_data)
        EventAssertHelpers.assert_metadata(input_metadata=input_metadata, output_events=docs)
        for inserted_event, event_to_insert in zip(docs, input_data):
            if event_to_insert.assets:
                assert inserted_event.asset_references
                await EventAssertHelpers.validate_assets_content(
                    output_assets=inserted_event.asset_references, input_assets=event_to_insert.assets, headers=headers
                )
        # Teardown
        await event_repo.delete_by_id(ids=[d.id for d in docs])

    async def test_insert_group_with_events_nested_assets_endpoint_passes(
        self,
        user_headers_factory,
        event_repo: EventRepository,
    ):
        # Arrange
        _, headers = await user_headers_factory()
        input_events = [
            InsertableEventInputBuilder()
            .with_assets(EventInputAssetBuilder().build_n(n=random.randint(0, 1)))
            .should_build_groups(should_build=False)
            .build()
            for _ in range(random.randint(1, 3))
        ]
        input_group = (
            InsertableGroupInputBuilder()
            .with_assets(EventInputAssetBuilder().build_n(n=random.randint(0, 1)))
            .with_events(input_events)
            .build()
        )

        input_metadata = EventMetadataInputBuilder().build()
        request_input = InsertEventAPIRequestInput(
            documents=[input_group],
            metadata=input_metadata,
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert docs
        assert len(docs) == 1 + len(input_events)

        inserted_group = [d for d in docs if isinstance(d, GroupV3APIOutput)][0]
        EventAssertHelpers.assert_events(returned_events=[inserted_group], expected_events=[input_group])
        EventAssertHelpers.assert_metadata(input_metadata=input_metadata, output_events=docs)
        self._assert_events_in_group(documents=docs, inserted_group=inserted_group, events_to_insert=input_events)
        for inserted_event, event_to_insert in zip(docs, [input_group, *input_events]):
            if event_to_insert.assets:
                assert inserted_event.asset_references
                await EventAssertHelpers.validate_assets_content(
                    output_assets=inserted_event.asset_references, input_assets=event_to_insert.assets, headers=headers
                )
        # Teardown
        await event_repo.delete_by_id(ids=[d.id for d in docs])

    @pytest.fixture
    async def user_with_template(
        self, template_repo: TemplateRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> AsyncGenerator[tuple[EventTemplate, MemberUser], Any]:
        user: MemberUser = await user_factory()
        template = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build()

        inserted_template = (await template_repo.insert(templates=[template], force_strong_consistency=True))[0]
        assert isinstance(inserted_template, EventTemplate)

        yield inserted_template, user

        # Teardown
        await template_repo.delete_by_id(ids=[inserted_template.id])

    async def test_insert_event_with_template_passes(self, user_with_template: tuple[EventTemplate, MemberUser]):
        event_template, user = user_with_template
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        input_data = (
            InsertableEventInputBuilder()
            .with_template_id(template_id=event_template.id)
            .with_type_id(type_id=event_template.document.type_id())
            .build()
        )
        request_input = InsertEventAPIRequestInput(
            documents=[input_data],
            metadata=EventMetadataInputBuilder().build(),
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        assert len(payload.documents) == 1
        inserted_event = payload.documents[0]
        assert inserted_event.template_id == event_template.id

    async def test_insert_event_with_non_existing_template_id_fails(self, user_headers_factory):
        # Arrange
        _, headers = await user_headers_factory()
        non_existing_template_id = PrimitiveTypesGenerator.generate_random_uuid()
        input_data = InsertableEventInputBuilder().with_template_id(template_id=non_existing_template_id).build()
        request_input = InsertEventAPIRequestInput(
            documents=[input_data],
            metadata=EventMetadataInputBuilder().build(),
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"Unexpected response: {response.json()}"

    async def test_insert_event_with_non_existing_plan_id_fails(self, user_headers_factory):
        # Arrange
        _, headers = await user_headers_factory()
        non_existing_plan_id = PrimitiveTypesGenerator.generate_random_uuid()
        input_data = InsertableEventInputBuilder().with_plan_id(plan_id=non_existing_plan_id).build()
        request_input = InsertEventAPIRequestInput(
            documents=[input_data],
            metadata=EventMetadataInputBuilder().build(),
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"Unexpected response: {response.json()}"

    @pytest.fixture
    async def user_with_contact(
        self, contact_repo: ContactRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> AsyncGenerator[tuple[Contact, MemberUser], Any]:
        user: MemberUser = await user_factory()
        contact = ContactBuilder().with_owner_id(owner_id=user.user_uuid).build()

        inserted_contact = (await contact_repo.insert(contacts=[contact], force_strong_consistency=True))[0]

        yield inserted_contact, user

        # Teardown
        await contact_repo.delete_by_id(ids=[inserted_contact.id])

    async def test_insert_person_event_with_existing_contact_id_passes(
        self, user_with_contact: tuple[Contact, MemberUser], event_repo: EventRepository
    ):
        # Arrange
        contact, user = user_with_contact
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        input_data = InsertPersonInputBuilder().with_contact_id(contact_id=contact.id).build()
        request_input = InsertEventAPIRequestInput(
            documents=[input_data],
            metadata=EventMetadataInputBuilder().build(),
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[PersonAPIOutput](**response.json())
        assert len(payload.documents) == 1
        inserted_person_event = payload.documents[0]
        assert inserted_person_event.contact_id == contact.id

        # Teardown
        await event_repo.delete_by_id(ids=[inserted_person_event.id])

    async def test_insert_event_with_non_existing_contact_id_fails(self, user_headers_factory):
        # Arrange
        _, headers = await user_headers_factory()
        non_existing_contact_id = PrimitiveTypesGenerator.generate_random_uuid()
        input_data = InsertPersonInputBuilder().with_contact_id(contact_id=non_existing_contact_id).build()
        request_input = InsertEventAPIRequestInput(
            documents=[input_data],
            metadata=EventMetadataInputBuilder().build(),
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"Unexpected response: {response.json()}"

    async def test_insert_event_endpoint_already_exists_raises(self, user_headers_factory, event_repo):
        # Arrange
        user, headers = await user_headers_factory()
        request_input = InsertEventAPIRequestInput(
            documents=InsertableEventInputBuilder().build_all(n=2),
            metadata=EventMetadataInputBuilder().build(),
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert docs
        doc_ids = [d.id for d in docs]

        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"Unexpected response: {response.json()}"
        await event_repo.delete_by_id(ids=doc_ids)

    async def test_insert_group_event_endpoint_passes(
        self,
        user_headers_factory,
        event_repo: EventRepository,
    ):
        def _collect_nested_group_inputs(group_input: InsertEventGroupInput) -> list[InsertEventGroupInput]:
            docs = [group_input]
            for event in group_input.events:
                if isinstance(event, InsertEventGroupInput):
                    docs.extend(_collect_nested_group_inputs(event))
            return docs

        # Arrange
        user, headers = await user_headers_factory()
        input_data = InsertableGroupInputBuilder().build_all()
        input_metadata = EventMetadataInputBuilder().build()
        request_input = InsertEventAPIRequestInput(documents=input_data, metadata=input_metadata)

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert docs

        inserted_group_list = [e for e in docs if isinstance(e, GroupV3APIOutput)]
        input_groups = []
        for group_input in input_data:
            input_groups.extend(_collect_nested_group_inputs(group_input))
        assert len(inserted_group_list) == len(input_groups)

        for inserted_group, expected_group in zip(inserted_group_list, input_groups):
            EventAssertHelpers.assert_events(returned_events=[inserted_group], expected_events=[expected_group])
            EventAssertHelpers.assert_metadata(input_metadata=input_metadata, output_events=[inserted_group])
            self._assert_events_in_group(
                documents=docs, inserted_group=inserted_group, events_to_insert=expected_group.events
            )

        # Teardown
        await event_repo.delete_by_id(ids=[d.id for d in docs])

    async def test_insert_group_event_child_ids_match_and_order_passes(
        self, user_headers_factory, event_repo: EventRepository
    ):
        # Arrange
        user, headers = await user_headers_factory()
        # Build a group with a specific order of events
        input_events = [
            InsertableEventInputBuilder().should_build_groups(False).build()
            for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=3, max_value=3))
        ]
        input_group = InsertableGroupInputBuilder().with_events(input_events).build()
        input_metadata = EventMetadataInputBuilder().build()
        request_input = InsertEventAPIRequestInput(documents=[input_group], metadata=input_metadata)

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert docs

        group = [e for e in docs if isinstance(e, GroupV3APIOutput)][0]
        returned_events = [e for e in docs if e.group_id == group.id]
        child_events = []
        for id in group.child_ids:
            child_events.extend([e for e in docs if e.id == id])
        assert len(child_events) == len(input_events)

        # Check that names match value and order
        input_event_names = [e.name for e in input_events]
        child_event_names = [e.name for e in child_events]
        returned_event_names = [e.name for e in returned_events]
        assert (
            input_event_names == returned_event_names == child_event_names
        ), f"event name order mismatch: {input_event_names} != {returned_event_names} != {child_event_names}"

        # Teardown
        await event_repo.delete_by_id(ids=[d.id for d in docs])

    async def test_insert_event_with_group_id_updates_group_child_ids(
        self, user_headers_factory, event_repo: EventRepository
    ):
        # Arrange
        user, headers = await user_headers_factory()
        initial_events = [InsertableEventInputBuilder().build() for _ in range(random.choice([0, 1]))]
        input_group = InsertableGroupInputBuilder().with_events(initial_events).build()
        input_metadata = EventMetadataInputBuilder().build()

        # Act
        request_input = InsertEventAPIRequestInput(documents=[input_group], metadata=input_metadata)
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert docs
        group = [e for e in docs if isinstance(e, GroupV3APIOutput)][0]
        new_event_input = InsertableEventInputBuilder().with_group_id(group_id=group.id).build()

        request_input = InsertEventAPIRequestInput(documents=[new_event_input], metadata=input_metadata)
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )
        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())

        new_event = payload.documents[0]
        updated_group = await event_repo.search_by_id(ids=[group.id])
        assert updated_group
        updated_group = updated_group[0]
        assert isinstance(updated_group, EventGroup)

        assert (
            new_event.id in updated_group.child_ids
        ), f"New event id {new_event.id} not in group's child_ids: {updated_group.child_ids}"
        assert new_event.group_id == group.id
        assert new_event.submission_id == group.submission_id

        # Clean up
        await event_repo.delete_by_id(ids=[e.id for e in [group, *docs, new_event]])

    async def test_insert_event_with_nonexistent_group_id_400(self, user_headers_factory):
        _, headers = await user_headers_factory()
        non_existent_group_id = PrimitiveTypesGenerator.generate_random_typed_uuid(data_type=DataType.EventGroup)
        input_event = InsertableEventInputBuilder().with_group_id(non_existent_group_id).build()
        input_metadata = EventMetadataInputBuilder().build()
        request_input = InsertEventAPIRequestInput(documents=[input_event], metadata=input_metadata)

        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )
        assert response.status_code == 400, f"Expected 400, got {response.status_code} with {response.json()}"

    async def test_insert_recursive_nested_group_endpoint_passes(
        self,
        user_headers_factory,
        event_repo: EventRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        input_data = InsertableGroupInputBuilder().build_recursively()
        input_metadata = EventMetadataInputBuilder().build()
        request_input = InsertEventAPIRequestInput(documents=input_data, metadata=input_metadata)

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        # Assert any top level non group events
        non_group_events = [d for d in docs if (isinstance(d, Event) and not d.group_id)]
        EventAssertHelpers.assert_events(
            returned_events=non_group_events,
            expected_events=[e for e in input_data if not isinstance(e, InsertEventGroupInput)],
        )
        EventAssertHelpers.assert_metadata(input_metadata=input_metadata, output_events=non_group_events)

        async def _assert_groups_recursively(
            returned_documents: Sequence[EventV3APIOutput],
            insert_input: Sequence[InsertEventGroupInput],
        ):
            returned_doc_ids = [d.id for d in returned_documents]
            top_level_returned_groups = [
                e
                for e in returned_documents
                if isinstance(e, GroupV3APIOutput) and (not e.group_id or e.group_id not in returned_doc_ids)
            ]
            assert len(top_level_returned_groups) == len(insert_input)

            if not top_level_returned_groups:
                return

            inner_expected_groups = []
            for inserted_group, to_insert_group in zip(top_level_returned_groups, insert_input):
                EventAssertHelpers.assert_events(returned_events=[inserted_group], expected_events=[to_insert_group])
                if to_insert_group.events:
                    self._assert_events_in_group(
                        documents=docs, inserted_group=inserted_group, events_to_insert=to_insert_group.events
                    )

                inner_expected_groups.extend(
                    [e for e in to_insert_group.events if isinstance(e, InsertEventGroupInput)]
                )

            await _assert_groups_recursively(
                returned_documents=[
                    d for d in returned_documents if d.id not in [g.id for g in top_level_returned_groups]
                ],
                insert_input=inner_expected_groups,
            )

        await _assert_groups_recursively(
            returned_documents=docs,
            insert_input=[e for e in input_data if isinstance(e, InsertEventGroupInput)],
        )

        # Teardown
        await event_repo.delete_by_id(ids=[d.id for d in docs])

    @staticmethod
    def _assert_events_in_group(
        documents: Sequence[EventV3APIOutput],
        inserted_group: GroupV3APIOutput,
        events_to_insert: Sequence[InsertEventInputs],
    ):
        """Asserts that the events within a group match the expected events."""
        submission_id = inserted_group.submission_id
        inserted_events = [e for e in documents if e.group_id == inserted_group.id]
        for e in inserted_events:
            assert e.submission_id == submission_id
        EventAssertHelpers.assert_events(returned_events=inserted_events, expected_events=events_to_insert)
