import random
from typing import Any, <PERSON><PERSON><PERSON>enerator, Sequence, Tuple
from uuid import uuid4

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.output.events.event_v3_api_output import ActivityAPIOutput, NoteAPIOutput
from services.base.api.query.boolean_query_api import BooleanQueryType, CompoundBooleanQueryAPI
from services.base.api.request_input.sort_request_input import SortRequestInput
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.models.sorts import SortOrder
from services.base.domain.enums.event_type import EventType
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.activity import Activity
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.event import Event, EventFields
from services.base.domain.schemas.events.note import Note, NoteFields
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.query.leaf_query import MatchType
from services.base.tests.api.query.leaf_query_api_helper import LeafQueryAPIHelper
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.base.tests.domain.builders.note_builder import NoteBuilder
from services.base.type_resolver import TypeResolver
from services.data_service.api.builders.event_feed_api_request_input_builder import EventFeedAPIRequestInputBuilder
from services.data_service.api.models.response.feed.event_feed_api_response import EventFeedAPIResponse
from services.data_service.api.queries.event_query_api import EventQueryAPI, EventTypedQueryAPI
from services.data_service.api.tests.common_rpc_calls import _call_post_endpoint
from services.data_service.api.urls import EventEndpointUrls
from services.data_service.application.use_cases.feed.document_feed_use_case import EXC_MESSAGE_QUERY_MUTATED
from services.data_service.conftest import UserFactoryCallable


class TestDataFeedEndpoint:
    @pytest.fixture
    def _all_organizations(self) -> Sequence[Organization]:
        return [organization for organization in Organization]

    @pytest.fixture
    def _all_origins(self) -> Sequence[Origin]:
        return [origin for origin in Origin]

    @pytest.fixture
    def _all_event_types_v2(self) -> Sequence[EventType]:
        return [EventType(event_v2.type_id()) for event_v2 in TypeResolver.EVENTS_V2]

    @pytest.fixture
    def _all_event_types_v3(self) -> Sequence[EventType]:
        return [EventType(event_v3.type_id()) for event_v3 in TypeResolver.EVENTS_V3]

    @pytest.fixture
    async def user_and_events(
        self,
        event_repo: EventRepository,
        depr_event_repository: DeprEventRepository,
        user_factory: UserFactoryCallable,
    ) -> AsyncGenerator[tuple[Any, Sequence[Event]], Any]:
        user = await user_factory()
        # TODO: replace Document with Event when feed supports only V3
        events = EventBuilder().with_owner_id(owner_id=user.user_uuid).build_all(n=5)

        events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield user, events

        await event_repo.delete_by_id(ids=[e.id for e in events])

    async def test_event_feed_endpoint_expects_n_results_sorted_passes(
        self, user_and_events: tuple[MemberUser, Sequence[Event]]
    ):
        # Arrange
        user, events = user_and_events
        limit = 10
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        type_ids = [Note.type_id(), Activity.type_id()]
        feed_query_item = EventTypedQueryAPI(types=[EventType(t) for t in type_ids])

        request_builder = (
            EventFeedAPIRequestInputBuilder()
            .with_limit(limit=limit)
            .with_query(query=EventQueryAPI(queries=[feed_query_item]))
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            headers=headers,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_query_params_as_dict(),
        )
        assert response.status_code == status.HTTP_200_OK, f"got response: {response.content}"
        response_model = EventFeedAPIResponse(**response.json())
        items = response_model.items

        assert len(items) <= limit
        for item in items:
            assert item.type_id() in type_ids

        # Assert
        sorted_list = sorted(items, key=lambda item: item.timestamp, reverse=True)
        assert items == sorted_list

    async def test_event_feed_endpoint_tags_included_passes(
        self,
        user_and_events: tuple[MemberUser, Sequence[Event]],
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        types = (Note,)
        tags = random.choice([e.tags for e in events if e.tags and isinstance(e, types)])

        tags_query = LeafQueryAPIHelper.tag_values_query(tags)
        compound_query = CompoundBooleanQueryAPI(type=BooleanQueryType.AND, queries=[tags_query])
        feed_query_item = EventTypedQueryAPI(types=[EventType(t.type_id()) for t in types], query=compound_query)

        request_builder = EventFeedAPIRequestInputBuilder().with_query(query=EventQueryAPI(queries=[feed_query_item]))

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            headers=headers,
            query_params=request_builder.build_query_params_as_dict(),
            json=request_builder.build_body_as_dict(),
        )

        assert response.status_code == status.HTTP_200_OK, f"got response: {response.content}"
        response_model = EventFeedAPIResponse(**response.json())
        items = response_model.items

        # Assert
        for item in items:
            assert isinstance(item, (NoteAPIOutput,))
            if item.tags:
                assert any((t in item.tags for t in tags))

    async def test_event_feed_endpoint_tags_excluded_passes(
        self,
        user_and_events: tuple[MemberUser, Sequence[Event]],
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        types = (Note,)

        tags = random.choice([e.tags for e in events if e.tags and isinstance(e, types)])

        exclude_tags_query = LeafQueryAPIHelper.exclude_tags_values_query(tags)
        compound_query = CompoundBooleanQueryAPI(type=BooleanQueryType.AND, queries=[exclude_tags_query])
        feed_query_item = EventTypedQueryAPI(types=[EventType(t.type_id()) for t in types], query=compound_query)

        request_builder = EventFeedAPIRequestInputBuilder().with_query(query=EventQueryAPI(queries=[feed_query_item]))

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            headers=headers,
            query_params=request_builder.build_query_params_as_dict(),
            json=request_builder.build_body_as_dict(),
        )

        assert response.status_code == status.HTTP_200_OK, f"got response: {response.content}"
        response_model = EventFeedAPIResponse(**response.json())
        items = response_model.items

        # Assert
        for item in items:
            assert isinstance(item, (NoteAPIOutput,))
            if item.tags:
                assert all((t not in item.tags for t in tags))

    async def test_event_feed_endpoint_compound_query_passes(
        self,
        user_and_events: tuple[MemberUser, Sequence[Document]],
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        types = (Note, Activity)

        matching_events = [e for e in events if isinstance(e, types) and e.name]
        event = random.choice(matching_events)

        origin_query = LeafQueryAPIHelper.origin_values_query([event.metadata.origin])
        name_query = LeafQueryAPIHelper.name_values_query(names=[event.name])
        compound_query_events = CompoundBooleanQueryAPI(type=BooleanQueryType.AND, queries=[origin_query, name_query])
        feed_query_item = EventTypedQueryAPI(types=[EventType.Note, EventType.Activity], query=compound_query_events)

        request_builder = EventFeedAPIRequestInputBuilder().with_query(query=EventQueryAPI(queries=[feed_query_item]))
        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            headers=headers,
            query_params=request_builder.build_query_params_as_dict(),
            json=request_builder.build_body_as_dict(),
        )

        assert response.status_code == status.HTTP_200_OK, f"got response: {response.content}"
        response_model = EventFeedAPIResponse(**response.json())
        items = response_model.items

        # Assert
        for item in items:
            assert isinstance(item, (NoteAPIOutput, ActivityAPIOutput))
            assert item.metadata.origin == event.metadata.origin
            assert item.name in event.name

    async def test_event_feed_endpoint_where_one_field_is_not_defined_raises_422(
        self,
        user_and_events: tuple[MemberUser, Sequence[Document]],
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        name_query = LeafQueryAPIHelper.name_values_query(names=["this is event name"])
        compound_query = CompoundBooleanQueryAPI(type=BooleanQueryType.AND, queries=[name_query])
        feed_query_item = EventTypedQueryAPI(types=[EventType.Note, EventType.HeartRate], query=compound_query)

        request_builder = EventFeedAPIRequestInputBuilder().with_query(query=EventQueryAPI(queries=[feed_query_item]))
        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_query_params_as_dict(),
            headers=headers,
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, f"got response: {response.content}"
        assert "Field name name does not exist." in response.json()["message"]

    async def test_event_feed_endpoint_with_non_existing_user_raises_204(
        self,
    ):
        # Arrange
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=uuid4())}"}

        # Act
        response = await _call_post_endpoint(request_url=EventEndpointUrls.FEED, headers=headers, json={})

        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT, f"got response: {response.content}"

    @pytest.mark.parametrize("sort_order", [SortOrder.DESCENDING, SortOrder.ASCENDING])
    async def test_event_feed_endpoint_stream_using_continuation_token_passes(
        self,
        sort_order: SortOrder,
        user_and_events: tuple[MemberUser, Sequence[Document]],
        _all_organizations,
        _all_origins,
        _all_event_types_v2,
        _all_event_types_v3,
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        limit = 2

        org_query = LeafQueryAPIHelper.organization_values_query(_all_organizations)
        origin_query = LeafQueryAPIHelper.origin_values_query(_all_origins)
        feed_query_item_v2 = EventTypedQueryAPI(types=_all_event_types_v2, query=org_query)
        feed_query_item_v3 = EventTypedQueryAPI(types=_all_event_types_v3, query=origin_query)

        request_builder = (
            EventFeedAPIRequestInputBuilder()
            .with_limit(limit)
            .with_sort(SortRequestInput(order=sort_order))
            .with_query(query=EventQueryAPI(queries=[feed_query_item_v2, feed_query_item_v3]))
        )

        # Act
        first_response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            query_params=request_builder.build_query_params_as_dict(),
            json=request_builder.build_body_as_dict(),
            headers=headers,
        )
        assert first_response.status_code == status.HTTP_200_OK, f"got response: {first_response.content}"
        first_response_model = EventFeedAPIResponse(**first_response.json())
        first_items = first_response_model.items

        request_builder.with_continuation_token(first_response_model.continuation_token)
        second_response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            query_params=request_builder.build_query_params_as_dict(),
            json=request_builder.build_body_as_dict(),
            headers=headers,
        )
        assert second_response.status_code == status.HTTP_200_OK, f"got response: {second_response.content}"
        second_response_model = EventFeedAPIResponse(**second_response.json())
        second_items = second_response_model.items
        assert len(first_items) == len(second_items)

        # Join the results into single collection
        joined_items = [*first_items, *second_items]

        # Fetch the same amount of documents in single request
        request_builder = (
            EventFeedAPIRequestInputBuilder()
            .with_limit(limit * 2)
            .with_sort(SortRequestInput(order=sort_order))
            .with_query(query=EventQueryAPI(queries=[feed_query_item_v2, feed_query_item_v3]))
        )

        single_response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            query_params=request_builder.build_query_params_as_dict(),
            json=request_builder.build_body_as_dict(),
            headers=headers,
        )
        assert single_response.status_code == status.HTTP_200_OK, f"got response: {single_response.content}"
        single_response_model = EventFeedAPIResponse(**single_response.json())
        single_items = single_response_model.items

        # Assert
        # Ensure the collections are the same
        assert len(joined_items) == len(single_items)
        assert joined_items == single_items

    @pytest.mark.parametrize(
        "sort_orders", [(SortOrder.DESCENDING, SortOrder.ASCENDING), (SortOrder.ASCENDING, SortOrder.DESCENDING)]
    )
    async def test_event_feed_endpoint_stream_using_continuation_token_back_end_forth_passes(
        self,
        sort_orders: Tuple[SortOrder, SortOrder],
        user_and_events: tuple[MemberUser, Sequence[Document]],
        _all_organizations,
        _all_origins,
        _all_event_types_v2,
        _all_event_types_v3,
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        limit = 2

        org_query = LeafQueryAPIHelper.organization_values_query(_all_organizations)
        origin_query = LeafQueryAPIHelper.origin_values_query(_all_origins)
        feed_query_v2 = EventTypedQueryAPI(types=_all_event_types_v2, query=org_query)
        feed_query_v3 = EventTypedQueryAPI(types=_all_event_types_v3, query=origin_query)
        query = EventQueryAPI(queries=[feed_query_v2, feed_query_v3])

        request_builder = (
            EventFeedAPIRequestInputBuilder()
            .with_limit(limit)
            .with_sort(SortRequestInput(order=sort_orders[0]))
            .with_query(query=query)
        )

        # Act
        first_response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_query_params_as_dict(),
            headers=headers,
        )
        assert first_response.status_code == status.HTTP_200_OK, f"got response: {first_response.content}"
        first_response_model = EventFeedAPIResponse(**first_response.json())
        first_items = first_response_model.items

        request_builder.with_continuation_token(first_response_model.continuation_token)
        second_response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_query_params_as_dict(),
            headers=headers,
        )
        assert second_response.status_code == status.HTTP_200_OK, f"got response: {second_response.content}"
        second_response_model = EventFeedAPIResponse(**second_response.json())
        second_items = second_response_model.items
        assert len(first_items) == len(second_items)

        # Join the results into single collection - omit last entry that we reverse from
        joined_items = [*first_items, *second_items[:-1]]

        # Reverse feed order and fetch the same number of documents in single request
        reverse_request_builder = (
            EventFeedAPIRequestInputBuilder()
            .with_query(query)
            .with_limit(2 * limit)
            .with_continuation_token(second_response_model.continuation_token)
            .with_limit(2 * limit)
            .with_sort(sort=SortRequestInput(order=sort_orders[1]))
        )
        reverse_response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            json=reverse_request_builder.build_body_as_dict(),
            query_params=reverse_request_builder.build_query_params_as_dict(),
            headers=headers,
        )
        assert reverse_response.status_code == status.HTTP_200_OK, f"got response: {reverse_response.content}"
        reverse_response_model = EventFeedAPIResponse(**reverse_response.json())
        reverse_items = reverse_response_model.items

        # Assert
        # Ensure the collections are reversed
        assert len(joined_items) == len(reverse_items)
        assert list(reversed(joined_items)) == reverse_items

    async def test_event_feed_endpoint_stream_using_continuation_token_mutated_queries_raises_400(
        self, user_and_events: tuple[MemberUser, Sequence[Document]]
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        first_feed_query_item = EventTypedQueryAPI(types=[EventType.Activity])

        request_builder = (
            EventFeedAPIRequestInputBuilder()
            .with_sort(SortRequestInput(order=SortOrder.DESCENDING))
            .with_query(query=EventQueryAPI(queries=[first_feed_query_item]))
        )

        # Act
        first_response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            query_params=request_builder.build_query_params_as_dict(),
            json=request_builder.build_body_as_dict(),
            headers=headers,
        )
        assert first_response.status_code == status.HTTP_200_OK, f"got response: {first_response.content}"
        first_response_model = EventFeedAPIResponse(**first_response.json())

        second_feed_query_item = EventTypedQueryAPI(types=[EventType.Note])
        request_builder = (
            EventFeedAPIRequestInputBuilder()
            .with_sort(SortRequestInput(order=SortOrder.DESCENDING))
            .with_query(query=EventQueryAPI(queries=[second_feed_query_item]))
            .with_continuation_token(first_response_model.continuation_token)
        )

        second_response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            query_params=request_builder.build_query_params_as_dict(),
            json=request_builder.build_body_as_dict(),
            headers=headers,
        )
        assert second_response.status_code == status.HTTP_400_BAD_REQUEST, f"got response: {second_response.content}"
        assert second_response.json()["message"] == EXC_MESSAGE_QUERY_MUTATED

    async def test_event_feed_endpoint_with_range_query_expected_results_inside_range_passes(
        self, user_and_events: tuple[MemberUser, Sequence[Event]]
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        type_ids = [
            Note.type_id(),
        ]
        event = random.choice([e for e in events if e.type_id() in type_ids and e.timestamp and e.end_time])
        gte = event.timestamp
        lte = event.end_time
        assert lte

        range_query = LeafQueryAPIHelper.create_range_query(field_name="timestamp", gte=gte, lte=lte)
        compound_query = CompoundBooleanQueryAPI(type=BooleanQueryType.AND, queries=[range_query])
        feed_query_item = EventTypedQueryAPI(types=[EventType(t) for t in type_ids], query=compound_query)

        request_builder = (
            EventFeedAPIRequestInputBuilder().with_limit(10).with_query(query=EventQueryAPI(queries=[feed_query_item]))
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_query_params_as_dict(),
            headers=headers,
        )

        assert response.status_code == status.HTTP_200_OK, f"got response: {response.content}"
        response_model = EventFeedAPIResponse(**response.json())
        items = response_model.items

        for item in items:
            assert gte <= item.timestamp <= lte

    async def test_event_feed_endpoint_with_exact_pattern_query_expects_exact_match_passes(
        self, user_and_events: tuple[MemberUser, Sequence[Event]]
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        types = (Note,)
        matching_events = [e for e in events if isinstance(e, types) and e.name]

        expected_name = random.choice(matching_events).name

        pattern_query = LeafQueryAPIHelper.create_pattern_query(
            field_names=[NoteFields.NAME],
            pattern=expected_name,
            match_type=MatchType.EXACT,
        )

        compound_query = CompoundBooleanQueryAPI(type=BooleanQueryType.AND, queries=[pattern_query])
        feed_query_item = EventTypedQueryAPI(types=[EventType(t.type_id()) for t in types], query=compound_query)

        request_builder = EventFeedAPIRequestInputBuilder().with_query(query=EventQueryAPI(queries=[feed_query_item]))

        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_query_params_as_dict(),
            headers=headers,
        )

        assert response
        assert response.status_code == status.HTTP_200_OK, f"got response: {response.content}"

        response_model = EventFeedAPIResponse(**response.json())
        items = response_model.items

        for item in items:
            assert isinstance(item, (NoteAPIOutput,))
            assert item.name == expected_name

    async def test_event_feed_endpoint_with_default_pattern_query_expects_cross_match_passes(
        self, user_and_events: tuple[MemberUser, Sequence[Event]]
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        types = (Note, Activity)
        matching_events: Sequence[Note | Activity] = [e for e in events if isinstance(e, types) and e.note]

        expected_string = random.choice(matching_events).note
        assert expected_string

        pattern_query = LeafQueryAPIHelper.create_pattern_query(
            field_names=[NoteFields.NOTE, EventFields.NAME],
            pattern=expected_string,
        )
        compound_query = CompoundBooleanQueryAPI(type=BooleanQueryType.AND, queries=[pattern_query])
        feed_query_item = EventTypedQueryAPI(types=[EventType(t.type_id()) for t in types], query=compound_query)

        request_builder = EventFeedAPIRequestInputBuilder().with_query(query=EventQueryAPI(queries=[feed_query_item]))

        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_query_params_as_dict(),
            headers=headers,
        )

        assert response
        assert response.status_code == status.HTTP_200_OK, f"got response: {response.content}"

        response_model = EventFeedAPIResponse(**response.json())
        items = response_model.items
        for item in items:
            # Expected string is only a partial match for the real value of the field
            # therefore they don't have to match 1:1, but expected is a subset of the string
            assert isinstance(item, (NoteAPIOutput, ActivityAPIOutput))
            assert any(
                (
                    any((word in (item.note or "") for word in expected_string.split(" "))),
                    any((word in item.name for word in expected_string.split(" "))),
                )
            )

    @pytest.fixture
    async def user_with_explicit_notes(
        self,
        event_repo: EventRepository,
        user_factory: UserFactoryCallable,
    ) -> AsyncGenerator[tuple[MemberUser, Sequence[Event]], Any]:
        user = await user_factory()

        notes = [
            "How to Stay Productive While Working from Home",
            "10 Best Books to Read This Year",
            "Simple Recipes for Busy Weeknights",
            "Today I am grateful for",
            "I see fire inside the mountain",
        ]

        events = (
            NoteBuilder()
            .with_note(note=random.choice(notes))
            .with_owner_id(owner_id=user.user_uuid)
            .with_origin(origin=Origin.AMAZON)
            .build_n(10)
        )

        events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield user, events

        await event_repo.delete_by_id(ids=[e.id for e in events])

    async def test_event_feed_endpoint_with_fuzzy_pattern_query_keyword_expected_results_inside_passes(
        self, user_with_explicit_notes: tuple[MemberUser, Sequence[Note]]
    ):
        # Arrange
        user, notes = user_with_explicit_notes
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        expected_string = random.choice(notes).note
        assert expected_string
        string_malformed = expected_string[1:-2]

        pattern_query = LeafQueryAPIHelper.create_pattern_query(
            field_names=[NoteFields.NOTE],
            pattern=string_malformed,
        )
        compound_query = CompoundBooleanQueryAPI(type=BooleanQueryType.AND, queries=[pattern_query])
        feed_query_item = EventTypedQueryAPI(types=[EventType.Note], query=compound_query)

        request_builder = EventFeedAPIRequestInputBuilder().with_query(query=EventQueryAPI(queries=[feed_query_item]))

        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_query_params_as_dict(),
            headers=headers,
        )

        assert response
        assert response.status_code == status.HTTP_200_OK, f"got response: {response.content}"

        response_model = EventFeedAPIResponse(**response.json())
        items = response_model.items
        for item in items:
            assert isinstance(item, NoteAPIOutput)
            assert item.note == expected_string

    async def test_event_feed_endpoint_with_pattern_query_expects_no_results_raises_204(
        self,
        user_and_events: tuple[MemberUser, Sequence[Event]],
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        expected_string = "this is totally not present in the dataset"

        pattern_query = LeafQueryAPIHelper.create_pattern_query(
            field_names=[EventFields.NAME],
            pattern=expected_string,
            match_type=MatchType.EXACT,
        )
        compound_query = CompoundBooleanQueryAPI(type=BooleanQueryType.AND, queries=[pattern_query])
        feed_query_item = EventTypedQueryAPI(types=[EventType.Activity], query=compound_query)

        request_builder = EventFeedAPIRequestInputBuilder().with_query(query=EventQueryAPI(queries=[feed_query_item]))

        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_query_params_as_dict(),
            headers=headers,
        )

        assert response
        assert response.status_code == status.HTTP_204_NO_CONTENT, f"got response: {response.content}"

    async def test_event_feed_endpoint_with_empty_queries_searches_all_types_passes(
        self, user_and_events: tuple[MemberUser, Sequence[Event]]
    ):
        # Arrange
        user, events = user_and_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_builder = EventFeedAPIRequestInputBuilder().with_limit(len(events)).with_query(query=EventQueryAPI())

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.FEED,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_query_params_as_dict(),
            headers=headers,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"got response: {response.content}"
        response_model = EventFeedAPIResponse(**response.json())
        items = response_model.items
        assert len(items) == len(events)
