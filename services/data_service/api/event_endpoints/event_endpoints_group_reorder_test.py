import json
from typing import Any, Async<PERSON>enerator, <PERSON>wai<PERSON>, Callable, Sequence
from uuid import UUID

import pytest
from pydantic import ValidationError
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.output.annotated_api_outputs import EventV3APIOutput
from services.base.api.output.events.event_v3_api_output import EventGroupAPIOutput
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.base.tests.domain.builders.event_group_builder import EventGroupBuilder
from services.data_service.api.models.request.event.reorder_event_group_api_request_input import (
    ReorderEventGroupAPIRequestInput,
)
from services.data_service.api.tests.common_rpc_calls import _call_patch_endpoint
from services.data_service.api.urls import EventEndpointUrls


class TestReorderEventGroupEndpoint:
    @pytest.fixture
    async def user_with_event_group(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> AsyncGenerator[tuple[EventGroup, Sequence[str], MemberUser], Any]:
        user: MemberUser = await user_factory()

        # Create child events
        child_events = [EventBuilder().with_owner_id(owner_id=user.user_uuid).build() for _ in range(3)]

        # Create event group with child events
        event_group = (
            EventGroupBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_child_ids([e.id for e in child_events])
            .build()
        )

        # Insert all events
        inserted_events = await event_repo.insert(events=[event_group, *child_events], force_strong_consistency=True)

        # Extract the inserted group
        inserted_group = next(e for e in inserted_events if isinstance(e, EventGroup))
        child_ids = [str(child_id) for child_id in inserted_group.child_ids]

        yield inserted_group, child_ids, user

        # Teardown
        await event_repo.delete_by_id(ids=[e.id for e in inserted_events])

    async def test_reorder_event_group_endpoint_success(
        self, user_with_event_group: tuple[EventGroup, Sequence[str], MemberUser]
    ):
        # Arrange
        event_group, child_ids, user = user_with_event_group
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Reverse the order of child IDs
        reordered_child_ids = list(reversed(child_ids))

        request_input = ReorderEventGroupAPIRequestInput(
            id=event_group.id,
            child_ids=[UUID(child_id) for child_id in reordered_child_ids],
        )

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.GROUP_REORDER,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        assert len(payload.documents) == 1
        assert isinstance(payload.documents[0], EventGroupAPIOutput)

        updated_group = payload.documents[0]
        assert updated_group.id == event_group.id
        returned_child_ids = [str(child_id) for child_id in updated_group.child_ids]
        assert len(returned_child_ids) == len(reordered_child_ids)
        assert returned_child_ids == reordered_child_ids

    async def test_reorder_event_group_with_non_existent_group_fails(self, user_headers_factory):
        # Arrange
        _, headers = await user_headers_factory()

        non_existent_group_id = PrimitiveTypesGenerator.generate_random_typed_uuid(data_type=DataType.EventGroup)

        request_input = ReorderEventGroupAPIRequestInput(
            id=non_existent_group_id,
            child_ids=[PrimitiveTypesGenerator.generate_random_typed_uuid(data_type=DataType.EventGroup)],
        )

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.GROUP_REORDER,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"Unexpected response: {response.json()}"

    async def test_reorder_event_group_with_mismatched_child_ids_fails(
        self, user_with_event_group: tuple[EventGroup, Sequence[str], MemberUser]
    ):
        # Arrange
        event_group, child_ids, user = user_with_event_group
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Use different child IDs than what's in the group
        wrong_child_ids = [str(PrimitiveTypesGenerator.generate_random_uuid()) for _ in range(3)]

        request_input = ReorderEventGroupAPIRequestInput(
            id=event_group.id,
            child_ids=[UUID(child_id) for child_id in wrong_child_ids],
        )

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.GROUP_REORDER,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"Unexpected response: {response.json()}"

    async def test_reorder_event_group_with_duplicate_child_ids_fails(
        self, user_with_event_group: tuple[EventGroup, Sequence[str], MemberUser]
    ):
        # Arrange
        event_group, child_ids, _ = user_with_event_group
        duplicate_child_ids = [child_ids[0], child_ids[0], child_ids[1]]

        # Act
        with pytest.raises(ValidationError) as exc_info:
            ReorderEventGroupAPIRequestInput(
                id=event_group.id,
                child_ids=[UUID(child_id) for child_id in duplicate_child_ids],
            )

        # Assert
        assert "Duplicated items found in" in str(exc_info.value)

    async def test_reorder_event_group_with_wrong_owner_fails(
        self,
        user_with_event_group: tuple[EventGroup, Sequence[str], MemberUser],
        user_factory: Callable[[], Awaitable[MemberUser]],
    ):
        # Arrange
        event_group, child_ids, _ = user_with_event_group
        different_user = await user_factory()
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=different_user.user_uuid)}"}

        request_input = ReorderEventGroupAPIRequestInput(
            id=event_group.id,
            child_ids=[UUID(child_id) for child_id in child_ids],
        )

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.GROUP_REORDER,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN, f"Unexpected response: {response.json()}"
