from starlette import status

from services.base.domain.enums.event_type import EventType
from services.data_service.api.models.response.suggest_correlation_parameters_api_output import (
    SuggestCorrelationParametersAPIOutput,
)
from services.data_service.api.queries.event_query_api import EventTypedQueryAPI
from services.data_service.api.tests.common_rpc_calls import _call_post_endpoint
from services.data_service.api.urls import AnalyzeEndpointUrls


class TestSuggestCorrelationEndpoint:
    async def test_suggest_correlation_parameters_endpoint_passes(self, user_headers_factory):
        # Arrange
        _, headers = await user_headers_factory()
        request_url = AnalyzeEndpointUrls.CORRELATE_EVENT_SUGGEST_PARAMETERS

        dependent_query = EventTypedQueryAPI(types=[EventType.Symptom])
        independent_query = EventTypedQueryAPI(types=[EventType.Food])
        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent_query": dependent_query.model_dump(),
                "independent_query": independent_query.model_dump(),
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = SuggestCorrelationParametersAPIOutput(**response.json())
        assert output
        assert output.reasoning
        assert output.temporal_options
        assert output.dependent
        assert output.dependent.query == dependent_query
        assert output.independent
        assert output.independent.query == independent_query
