from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.output.time_interval_aggregation_api_output import TimeIntervalAggregationAPIOutput
from services.base.application.exceptions import BadRequestException, IncorrectOperationException, NoContentException
from services.data_service.api.constants import AggregationEndpointRoutes, DataServicePrefixes
from services.data_service.api.models.request.aggregate.calendar_aggregations_api_request_input import (
    CalendarHistogramAggregationAPIRequestInput,
)
from services.data_service.api.models.request.aggregate.date_histogram_api_request_input import (
    DataHistogramAPIRequestInput,
)
from services.data_service.api.models.request.aggregate.frequency_distribution_api_request_input import (
    FrequencyDistributionAPIRequestInput,
)
from services.data_service.api.models.request.aggregate.time_interval_aggregation_api_request_input import (
    TimeIntervalAggregationAPIRequestInput,
)
from services.data_service.api.models.response.calendar_aggregations_api_response import (
    CalendarHistogramAggregationAPIOutput,
)
from services.data_service.api.models.response.date_histogram_api_output import DataHistogramAPIOutput
from services.data_service.api.models.response.frequency_distribution_api_output import FrequencyDistributionAPIOutput
from services.data_service.application.use_cases.calendar_aggregation.calendar_aggregations_input_boundary import (
    CalendarHistogramAggregationInputBoundary,
)
from services.data_service.application.use_cases.calendar_aggregation.calendar_histogram_aggregation_use_case import (
    CalendarHistogramAggregationUseCase,
)
from services.data_service.application.use_cases.date_histogram_use_case import (
    DateHistogramUseCase,
    DateHistogramUseCaseInputBoundary,
)
from services.data_service.application.use_cases.frequency_distribution_use_case import (
    FrequencyDistributionUseCase,
    FrequencyDistributionUseCaseInputBoundary,
)
from services.data_service.application.use_cases.time_interval_aggregation_use_case import (
    TimeIntervalAggregationUseCase,
)

aggregate_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.AGGREGATION}",
    tags=["aggregate"],
    responses={404: {"description": "Not found"}},
)


@aggregate_router.post(
    AggregationEndpointRoutes.DATE_HISTOGRAM,
)
async def date_histogram_endpoint(
    input_boundary: DateHistogramUseCaseInputBoundary = Depends(DataHistogramAPIRequestInput.to_input_boundary),
    use_case: DateHistogramUseCase = Injected(DateHistogramUseCase),
) -> DataHistogramAPIOutput:
    result = await use_case.execute_async(input_boundary=input_boundary)
    if not result.results:
        raise NoContentException("no data available for the given queries")
    return DataHistogramAPIOutput.map(model=result)


@aggregate_router.post(AggregationEndpointRoutes.FREQUENCY_DISTRIBUTION)
async def frequency_distribution_endpoint(
    input_boundary: FrequencyDistributionUseCaseInputBoundary = Depends(
        FrequencyDistributionAPIRequestInput.to_input_boundary
    ),
    use_case: FrequencyDistributionUseCase = Injected(FrequencyDistributionUseCase),
) -> FrequencyDistributionAPIOutput:
    try:
        result = await use_case.execute_async(input_boundary=input_boundary)
        if all(not type_output for type_output in result.data.values()):
            raise NoContentException("No data available")
        return FrequencyDistributionAPIOutput.map(model=result)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@aggregate_router.post(
    AggregationEndpointRoutes.CALENDAR_HISTOGRAM_AGGREGATION,
    response_model=CalendarHistogramAggregationAPIOutput,
)
async def calendar_histogram_aggregation_endpoint(
    calendar_histogram_aggregation_use_case: CalendarHistogramAggregationUseCase = Injected(
        CalendarHistogramAggregationUseCase
    ),
    input_boundary: CalendarHistogramAggregationInputBoundary = Depends(
        CalendarHistogramAggregationAPIRequestInput.to_input_boundary
    ),
) -> CalendarHistogramAggregationAPIOutput:
    try:
        result = await calendar_histogram_aggregation_use_case.execute_async(
            user_uuid=input_boundary.owner_id,
            calendar_aggregation_type=input_boundary.calendar_aggregation_type,
            field_name=input_boundary.field_name,
            aggregation_method=input_boundary.aggregation_method,
            query=input_boundary.query,
            fill_null_values=input_boundary.fill_null_values,
        )
        if not result.results:
            raise NoContentException("No documents found for CalendarHistogramAggregation")

        return CalendarHistogramAggregationAPIOutput.map(model=result)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@aggregate_router.post(AggregationEndpointRoutes.TIME_INTERVAL_AGGREGATION)
async def time_interval_aggregation_endpoint(
    input_boundary=Depends(TimeIntervalAggregationAPIRequestInput.to_input_boundary),
    use_case: TimeIntervalAggregationUseCase = Injected(TimeIntervalAggregationUseCase),
) -> TimeIntervalAggregationAPIOutput:
    try:
        result = await use_case.execute_async(input_boundary=input_boundary)
        if not result:
            raise NoContentException("No data available or not enough data to aggregate, need at least 2 documents")
        return TimeIntervalAggregationAPIOutput.map(model=result)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
