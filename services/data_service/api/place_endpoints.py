from uuid import UUI<PERSON>

from fastapi import API<PERSON><PERSON><PERSON>, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.output.place.place_api_output import PlaceAPIOutput
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.exceptions import (
    BadRequestException,
    DuplicateDocumentsFound,
    IncorrectOperationException,
    NoContentException,
)
from services.data_service.api.constants import DataServicePrefixes, PlaceEndpointRoutes
from services.data_service.api.models.request.place.insert_place_api_request_input import InsertPlaceAPIRequestInput
from services.data_service.api.models.request.place.search_place_api_request_input import SearchPlaceRequestInput
from services.data_service.api.models.request.place.update_place_api_request_input import UpdatePlaceAPIRequestInput
from services.data_service.application.use_cases.place.archive_place_use_case import ArchivePlaceUseCase
from services.data_service.application.use_cases.place.insert_place_use_case import InsertPlaceUseCase
from services.data_service.application.use_cases.place.models.insert_place_input_boundary import (
    InsertPlaceInputBoundary,
)
from services.data_service.application.use_cases.place.models.search_place_input_boundary import (
    SearchPlaceInputBoundary,
)
from services.data_service.application.use_cases.place.models.update_place_input_boundary import (
    UpdatePlaceInputBoundary,
)
from services.data_service.application.use_cases.place.search_place_use_case import SearchPlaceUseCase
from services.data_service.application.use_cases.place.update_place_use_case import UpdatePlaceUseCase

place_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.PLACE}",
    tags=["place"],
    responses={404: {"description": "Not found"}},
)


@place_router.post(PlaceEndpointRoutes.SEARCH)
async def search_places_endpoint(
    use_case: SearchPlaceUseCase = Injected(SearchPlaceUseCase),
    input_boundary: SearchPlaceInputBoundary = Depends(SearchPlaceRequestInput.to_input_boundary),
) -> CommonDocumentsResponse[PlaceAPIOutput]:
    places = await use_case.execute_async(input_boundary=input_boundary)
    if not places:
        raise NoContentException(message="no matching places found")

    output = [PlaceAPIOutput.map(model=place) for place in places]

    return CommonDocumentsResponse[PlaceAPIOutput](documents=output)


@place_router.patch(PlaceEndpointRoutes.ARCHIVE)
async def archive_places_endpoint(
    place_ids: list[UUID] = Query(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: ArchivePlaceUseCase = Injected(ArchivePlaceUseCase),
) -> CommonDocumentsResponse[PlaceAPIOutput]:
    try:
        places = await use_case.execute_async(owner_id=owner_id, place_ids=place_ids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[PlaceAPIOutput](
        documents=[PlaceAPIOutput(**place.model_dump(by_alias=True)) for place in places]
    )


@place_router.patch(PlaceEndpointRoutes.BASE)
async def update_places_endpoint(
    input_boundary: UpdatePlaceInputBoundary = Depends(UpdatePlaceAPIRequestInput.to_input_boundary),
    use_case: UpdatePlaceUseCase = Injected(UpdatePlaceUseCase),
) -> CommonDocumentsResponse[PlaceAPIOutput]:
    try:
        places = await use_case.execute_async(input_boundary=input_boundary)
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="place duplicates found in the update payload") from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[PlaceAPIOutput](
        documents=[PlaceAPIOutput(**place.model_dump(by_alias=True)) for place in places]
    )


@place_router.post(PlaceEndpointRoutes.BASE)
async def insert_places_endpoint(
    input_boundary: InsertPlaceInputBoundary = Depends(InsertPlaceAPIRequestInput.to_input_boundary),
    use_case: InsertPlaceUseCase = Injected(InsertPlaceUseCase),
) -> CommonDocumentsResponse[PlaceAPIOutput]:
    try:
        places = await use_case.execute_async(input_boundary=input_boundary)
    except DuplicateDocumentsFound as err:
        raise BadRequestException(
            message="Duplicate place detected. A place with the same details already exists in the system."
        ) from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    return CommonDocumentsResponse[PlaceAPIOutput](
        documents=[PlaceAPIOutput(**place.model_dump(by_alias=True)) for place in places]
    )
