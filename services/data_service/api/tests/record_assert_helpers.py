from typing import Sequence

from services.base.api.output.annotated_api_outputs import RecordAPIOutput
from services.base.api.output.records.body_metric_record_api_output import BodyMetricRecordAPIOutput
from services.base.api.output.records.sleep_record_api_output import SleepRecordAPIOutput
from services.base.api.output.records.steps_record_api_output import StepsRecordAPIOutput
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.data_service.application.use_cases.records.insert_record_inputs import InsertRecordInputs
from services.data_service.application.use_cases.records.models.insert_body_metric_record_input import (
    InsertBodyMetricRecordInput,
)
from services.data_service.application.use_cases.records.models.insert_sleep_record_input import InsertSleepRecordInput
from services.data_service.application.use_cases.records.models.insert_steps_record_input import InsertStepsRecordInput
from services.data_service.application.use_cases.records.models.update_body_metric_record_input import (
    UpdateBodyMetricRecordInput,
)
from services.data_service.application.use_cases.records.models.update_sleep_record_input import UpdateSleepRecordInput
from services.data_service.application.use_cases.records.models.update_steps_record_input import UpdateStepsRecordInput
from services.data_service.application.use_cases.records.update_record_inputs import UpdateRecordInputs


class RecordAssertHelpers:
    @staticmethod
    def assert_records(
        returned_records: Sequence[RecordAPIOutput],
        expected_records: Sequence[InsertRecordInputs | UpdateRecordInputs],
    ):
        for inserted_record, expected_record in zip(returned_records, expected_records):
            assert inserted_record.type == expected_record.type
            assert inserted_record.timestamp == expected_record.timestamp
            assert inserted_record.end_time == expected_record.end_time
            if inserted_record.duration:
                assert (
                    inserted_record.duration == (expected_record.end_time - expected_record.timestamp).total_seconds()
                )

            match expected_record:
                case InsertSleepRecordInput() | UpdateSleepRecordInput():
                    assert isinstance(inserted_record, SleepRecordAPIOutput)
                    assert inserted_record.stage == expected_record.stage
                    assert inserted_record.category == expected_record.category
                case InsertStepsRecordInput() | UpdateStepsRecordInput():
                    assert isinstance(inserted_record, StepsRecordAPIOutput)
                    assert inserted_record.value == expected_record.value
                    assert inserted_record.category == expected_record.category
                case InsertBodyMetricRecordInput() | UpdateBodyMetricRecordInput():
                    assert isinstance(inserted_record, BodyMetricRecordAPIOutput)
                    assert inserted_record.value == expected_record.value
                    assert inserted_record.category == expected_record.category
                case _:
                    raise ShouldNotReachHereException(f"Unexpected type {type(inserted_record)}")
