from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from fastapi_injector import Injected
from starlette import status

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.output.mappers.document_api_output_mapper import DocumentAPIOutputMapper
from services.base.application.exceptions import BadRequestException, IncorrectOperationException
from services.base.domain.enums.user_document_type import UserDocumentType
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.data_service.api.constants import DataServicePrefixes, DocumentEndpointRoutes
from services.data_service.api.models.request.feed.document_feed_api_request_input import DocumentFeedAPIRequestInput
from services.data_service.api.models.request.feed.node_feed_api_request_input import NodeFeedAPIRequestInput
from services.data_service.api.models.response.feed.document_feed_api_response import DocumentFeedAPIResponse
from services.data_service.api.queries.document_query_api import DocumentQueryAPI
from services.data_service.api.serializers.continuation_token_marshaller import ContinuationTokenMarshaller
from services.data_service.application.use_cases.feed.document_feed_input_boundary import DocumentFeedInputBoundary
from services.data_service.application.use_cases.feed.document_feed_use_case import DocumentFeedUseCase
from services.data_service.application.use_cases.user_data.delete_user_data_use_case import DeleteUserDataUseCase

document_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.DOCUMENT}",
    tags=["document"],
    responses={404: {"description": "Not found"}},
)


@document_router.delete(
    DocumentEndpointRoutes.BY_QUERY,
    status_code=status.HTTP_202_ACCEPTED,
    description="schedules parametrized deletion of all of the user's V3 documents",
)
async def delete_user_data_by_query_endpoint(
    user_uuid: UUID = Depends(get_current_uuid),
    query: DocumentQueryAPI = Body(...),
    use_case: DeleteUserDataUseCase = Injected(DeleteUserDataUseCase),
):
    return await use_case.execute_async(owner_id=user_uuid, query=query.to_query())


@document_router.delete(
    DocumentEndpointRoutes.ALL_DATA,
    status_code=status.HTTP_202_ACCEPTED,
    description="schedules deletion of all of the user's V3 documents",
)
async def delete_all_user_data_endpoint(
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: DeleteUserDataUseCase = Injected(DeleteUserDataUseCase),
):
    query = Query(
        type_queries=[
            TypeQuery(
                domain_types=[dt.to_domain_model() for dt in UserDocumentType],
                query=None,
            )
        ],
    )

    return await use_case.execute_async(owner_id=user_uuid, query=query)


@document_router.post(
    DocumentEndpointRoutes.FEED,
    response_model=DocumentFeedAPIResponse,
    summary="Document Feed: Paginable stream of user documents",
    description="""
    Provides an efficient way to iterate through a user's event history
    with support for filtering and pagination. Documents are returned sorted
    with continuation tokens for seamless pagination.
    """,
    response_description="Paginated list of documents with continuation token for next page",
    responses={
        200: {
            "description": "success",
            "content": {
                "application/json": {
                    "example": {
                        "items": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Morning workout",
                                "timestamp": "2024-01-15T08:30:00Z",
                                "type": "exercise",
                            }
                        ],
                        "continuation_token": "eyJsYXN0X3RpbWVzdGFtcCI6IjIwMjQtMDEtMTVUMDg6MzA6MDBaIn0=",
                    }
                }
            },
        },
        400: {"description": "Bad request - invalid filter parameters"},
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request parameters"},
    },
)
async def document_feed_endpoint(
    document_feed_use_case: DocumentFeedUseCase = Injected(DocumentFeedUseCase),
    user_uuid: UUID = Depends(get_current_uuid),
    input_boundary: DocumentFeedInputBoundary = Depends(DocumentFeedAPIRequestInput.to_input_boundary),
):
    try:
        result = await document_feed_use_case.execute_async(user_uuid=user_uuid, input_boundary=input_boundary)
        return DocumentFeedAPIResponse(
            items=[DocumentAPIOutputMapper.map(document=e) for e in result.events],
            continuation_token=ContinuationTokenMarshaller.encode_feed_continuation_token(result.continuation_token),
        )
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@document_router.post(
    DocumentEndpointRoutes.NODE_FEED,
    response_model=DocumentFeedAPIResponse,
    summary="Node Feed: Paginable stream of user documents",
)
async def node_feed_endpoint(
    document_feed_use_case: DocumentFeedUseCase = Injected(DocumentFeedUseCase),
    user_uuid: UUID = Depends(get_current_uuid),
    input_boundary: DocumentFeedInputBoundary = Depends(NodeFeedAPIRequestInput.to_input_boundary),
):
    try:
        result = await document_feed_use_case.execute_async(user_uuid=user_uuid, input_boundary=input_boundary)
        return DocumentFeedAPIResponse(
            items=[DocumentAPIOutputMapper.map(document=e) for e in result.events],
            continuation_token=ContinuationTokenMarshaller.encode_feed_continuation_token(result.continuation_token),
        )
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
