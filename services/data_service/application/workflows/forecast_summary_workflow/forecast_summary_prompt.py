FORECAST_SUMMARY_PROMPT = """
Analyze the input data and generate a environment forecast in JSON format.

The input data is a list EnvironmentForecastBucket objects, bucket OpenAPI schema:
{bucket_schema}

The output must strictly follow this OpenAPI schema:
{response_schema}

The data is provided in the metric system, however the user unit preferences may be in imperial, if that is the case, convert the output to the user's preferred unit.

- The response must be concise, informative and **not exceed the given constraints like max length **
- Do not include null values
- Focus primarily on health impacts if levels are moderate or high
- Summarize other environmental factors if relevant
- Do not suggest residents what to do
- Do not be dramatic

Example outputs:
{{
"title": "Hot and sunny with strong UV",
"body": "Highs of 28-32°C with strong UV (index 8-9). Clear skies, moderate humidity, and light winds. Air quality moderate (AQI ~100)."
}},
{{
"title": "Mild and stable conditions",
"body": "Temperatures between 50-60°F with clear skies, low humidity, and steady pressure. No major disturbances expected today."
}},
{{
"title": "Cold and dry with clear skies",
"body": "Temperatures ranging from -10 to 0°C, low humidity, and stable pressure. Light winds with good visibility throughout the day."
}},
{{
"title": "Rainy and windy conditions",
"body": "Expect 10-15°C with high humidity, heavy rain, and wind gusts up to 40 km/h. Pressure dropping by 12hPa. Air quality moderate."
}},
{{
"title": "Severe storm warning",
"body": "Thunderstorms with wind gusts over 70 mph, large hail, and heavy rain expected. Air quality may worsen. Stay alert for sudden changes."
}},
{{
"title": "High pollen and pollution levels",
"body": "Grass pollen levels above 250 grains/m³ and AQI over 100 indicate poor air quality. Sensitive groups should take precautions."
}},
{{
"title": "Freezing temperatures ahead",
"body": "Temperatures from -18°C to -12°C with light winds. Possible snowfall but clear visibility. Extreme cold—dress warmly."
}}
"""
