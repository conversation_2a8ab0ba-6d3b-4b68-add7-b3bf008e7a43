import logging
from asyncio import TaskGroup
from datetime import datetime, time, timedelta, timezone
from typing import Async<PERSON>terator, Optional, Sequence
from uuid import UUID

from pydantic_ai import Agent
from pydantic_ai.models import Model

from services.base.application.notifications.push_notification_models import (
    PushNotificationMessage,
    PushNotificationPublishResponse,
)
from services.base.application.notifications.push_notification_service import PushNotificationService
from services.base.application.retry import retry
from services.base.application.utils.member_user.member_user_settings import get_user_timezone
from services.base.domain.enums.settings.units_system import UnitsSystem
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.wrappers import Range, ReadFromDatabaseWrapper
from services.base.domain.schemas.member_user.member_user import Member<PERSON>ser
from services.base.domain.schemas.member_user.member_user_settings import MemberUserSettings
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from services.data_service.application.use_cases.environment.forecast_summary_use_case import (
    EnvironmentForecastBucket,
    ForecastSummaryUseCase,
)
from services.data_service.application.workflows.forecast_summary_workflow.forecast_summary_prompt import (
    FORECAST_SUMMARY_PROMPT,
)
from services.data_service.application.workflows.forecast_summary_workflow.models import (
    EvaluatedForecastMessage,
    EvaluatedForecastSummary,
)


class ForecastSummaryWorkflow:
    def __init__(
        self,
        user_repository: MemberUserRepository,
        forecast_summary_uc: ForecastSummaryUseCase,
        push_notification_service: PushNotificationService,
        device_repository: MemberUserDeviceRepository,
        settings_repository: MemberUserSettingsRepository,
        model: Model,
    ):
        self._user_repo = user_repository
        self._forecast_summary_uc = forecast_summary_uc
        self._push_notification_service = push_notification_service
        self._device_repository = device_repository
        self._user_settings = settings_repository

        self._prompt = FORECAST_SUMMARY_PROMPT.format(
            bucket_schema=EnvironmentForecastBucket.model_json_schema(),
            response_schema=EvaluatedForecastMessage.model_json_schema(),
        )
        self._agent = Agent(model=model, instructions=self._prompt, output_type=EvaluatedForecastMessage)
        workflow_counter = TelemetryInstrumentor.get_counter(name=__name__)
        self._counter = workflow_counter

    async def run(self, local_time_of_triggering: time):
        logging.info("running forecast workflow")
        total_users = 0

        now = datetime.now(timezone.utc)

        timestamp = now.astimezone(timezone(timedelta(hours=local_time_of_triggering.hour)))
        end_time = timestamp.replace(hour=23, minute=59, second=59)

        user_iter = self._get_eligible_users_as_iter(now=now, local_time_of_triggering=local_time_of_triggering)
        async for users in user_iter:
            total_users += len(users)
            await self._handle_batch(
                users=users,
                timestamp=timestamp,
                end_time=end_time,
            )

        logging.info(f"finished processing forecast alerts, total users: {total_users}")

    async def _handle_batch(self, users: Sequence[MemberUser], timestamp: datetime, end_time: datetime):
        failed_nots = 0
        users_forecasts = await self._forecast_summary_uc.execute(
            user_ids=[u.user_uuid for u in users], timestamp=timestamp, end_time=end_time
        )
        evaluated_forecasts = []
        for f in users_forecasts.forecasts:
            if f.buckets:
                user_settings: MemberUserSettings | None = await self._user_settings.get_by_uuid(user_uuid=f.user_id)
                user_unit = (
                    (user_settings.general.units_system if user_settings else UnitsSystem.METRIC)
                    if user_settings
                    else UnitsSystem.METRIC
                )

                evaluated_forecasts.append(
                    await self._evaluate_forecast_into_message(
                        forecast=f.model_dump_json(by_alias=True, exclude={"user_id"}, exclude_none=True),
                        user_id=f.user_id,
                        user_unit=user_unit,
                    ),
                )
        try:
            push_messages = await self._send_push_notifications(forecast=evaluated_forecasts)
            if push_messages:
                failed_nots += push_messages.failed
        except Exception as error:
            logging.exception(f"failed to send push notifications for forecast warning, err: {error}")
        if failed_nots:
            logging.error(f"failed to send push notifications for {failed_nots} users")

    async def _get_eligible_users_as_iter(
        self,
        now: datetime,
        local_time_of_triggering: time,
        field_name: str = "last_logged_at",
        batch_size: int = 50,
        delta: timedelta = timedelta(days=1),
    ) -> AsyncIterator[Sequence[MemberUser]]:
        """
        Returns an iterator of user sequences, filtered by those whose local time matches the trigger time.
        """
        user_iter = self._user_repo.yield_results(
            wrapper=ReadFromDatabaseWrapper(
                search_keys={},
                range_filter=Range(
                    field_name=field_name,
                    start_date=now - delta,
                ),
            ),
            size=batch_size,
        )

        async for users in user_iter:
            filtered_user_ids = []
            async with TaskGroup() as group:
                timezone_tasks = {
                    user.user_uuid: group.create_task(
                        get_user_timezone(uuid=user.user_uuid, member_user_settings_repo=self._user_settings)
                    )
                    for user in users
                }

            for user_id, task in timezone_tasks.items():
                try:
                    user_timezone = task.result()
                    user_local_time = now.astimezone(user_timezone).time()

                    # Check if user's local time hour matches the trigger time
                    if user_local_time.hour == local_time_of_triggering.hour:
                        filtered_user_ids.append(user_id)
                except Exception as e:
                    logging.error(f"Failed to process timezone for user {user_id}: {str(e)}")
                    continue

            if filtered_user_ids:
                yield [user for user in users if user.user_uuid in filtered_user_ids]

    async def _send_push_notifications(
        self,
        forecast: Sequence[EvaluatedForecastSummary],
    ) -> Optional[PushNotificationPublishResponse]:
        """Sends push notifications with messages for all eligible users given in the results input"""
        push_notification_messages: list[PushNotificationMessage] = []
        for result in forecast:
            device_tokens = [
                d.device_token for d in await self._device_repository.get_by_uuid(user_uuid=result.user_id)
            ]
            if not device_tokens:
                continue

            push_notification_messages.append(
                PushNotificationMessage(
                    device_tokens=device_tokens,
                    body=result.body,
                    title=result.title,
                )
            )

        if not push_notification_messages:
            return None

        response: PushNotificationPublishResponse = self._push_notification_service.publish(
            messages=push_notification_messages
        )

        if response.successful == 0 or response.failed == len(forecast):
            logging.error(f"all push notifications failed to send, push notification errors: {response.errors}")
        elif response.failed > 0:
            logging.error(
                f"not all push notifications were successfully sent, push notification errors: {response.errors}"
            )
        return response

    @retry(delay=0.1)
    async def _evaluate_forecast_into_message(
        self,
        user_id: UUID,
        forecast: str,
        user_unit: UnitsSystem,
    ) -> EvaluatedForecastSummary:
        user_preferences_prompt = f"User unit preferences: {user_unit.value}"
        response = await self._agent.run(
            user_prompt=[forecast, user_preferences_prompt], output_type=EvaluatedForecastMessage
        )
        res = response.output
        return EvaluatedForecastSummary(
            user_id=user_id,
            body=res.body,
            title=res.title,
        )
