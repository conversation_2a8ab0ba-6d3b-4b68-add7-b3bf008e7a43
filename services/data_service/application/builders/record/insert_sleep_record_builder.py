from typing import Self, Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.records.sleep_record import SleepRecordCategory, SleepRecordIdentifier, SleepStageType
from services.data_service.application.builders.record.insert_record_builder_base import InsertRecordBuilderBase
from services.data_service.application.use_cases.records.models.insert_sleep_record_input import InsertSleepRecordInput


class InsertSleepRecordInputBuilder(InsertRecordBuilderBase, SleepRecordIdentifier):
    def __init__(self):
        super().__init__()
        self._stage: SleepStageType | None = None
        self._category: SleepRecordCategory | None = None

    def build(self) -> InsertSleepRecordInput:
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        return InsertSleepRecordInput(
            timestamp=timestamp,
            type=DataType.SleepRecord,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=SleepRecordCategory),
            end_time=self._end_time or PrimitiveTypesGenerator.generate_random_aware_datetime(gte=timestamp),
            stage=self._stage or PrimitiveTypesGenerator.generate_random_enum(enum_type=SleepStageType),
        )

    def with_stage(self, stage: SleepStageType) -> Self:
        self._stage = stage
        return self

    def with_category(self, category: SleepRecordCategory) -> Self:
        self._category = category
        return self

    def build_n(self, n: int | None = None) -> Sequence[InsertSleepRecordInput]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(max_value=5, min_value=1))]
