import random
from uuid import uuid4

from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.place_category import PlaceCategory
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.place_visit import PlaceVisitIdentifier
from services.data_service.application.builders.event.update.update_event_input_builder_base import (
    UpdateEventInputBuilderBase,
)
from services.data_service.application.use_cases.events.models.update_place_visit_input import UpdatePlaceVisitInput


class UpdatePlaceVisitInputBuilder(UpdateEventInputBuilderBase, PlaceVisitIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdatePlaceVisitInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdatePlaceVisitInput(
            id=self._id or uuid4(),
            type=DataType.PlaceVisit,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=PlaceCategory),
            place_id=PrimitiveTypesGenerator.generate_random_uuid(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            rating=PrimitiveTypesGenerator.generate_random_int(
                min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
                allow_none=True,
            ),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            plan_extension=None,
            group_id=self._group_id,
        )
