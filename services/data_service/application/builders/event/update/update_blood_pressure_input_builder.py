import random
from datetime import timed<PERSON><PERSON>
from typing import Sequence
from uuid import uuid4

from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.body_metric.blood_pressure import (
    BloodPressureCategory,
    BloodPressureIdentifier,
    BloodPressureValueLimits,
)
from services.data_service.application.builders.event.update.update_event_input_builder_base import (
    UpdateEventInputBuilderBase,
)
from services.data_service.application.use_cases.events.models.body_metric.update_body_metric_inputs import (
    UpdateBloodPressureInput,
)


class UpdateBloodPressureInputBuilder(UpdateEventInputBuilderBase, BloodPressureIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateBloodPressureInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateBloodPressureInput(
            type=DataType.BloodPressure,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            systolic=PrimitiveTypesGenerator.generate_random_float(
                min_value=BloodPressureValueLimits.MINIMUM_SYSTOLIC, max_value=BloodPressureValueLimits.MAXIMUM_SYSTOLIC
            ),
            diastolic=PrimitiveTypesGenerator.generate_random_float(
                min_value=BloodPressureValueLimits.MINIMUM_DIASTOLIC,
                max_value=BloodPressureValueLimits.MAXIMUM_DIASTOLIC,
            ),
            id=self._id or uuid4(),
            note=PrimitiveTypesGenerator.generate_random_string(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=BloodPressureCategory),
            plan_extension=None,
            group_id=self._group_id,
        )

    def build_series(
        self, n: int | None = None, max_time_delta: timedelta | None = None
    ) -> Sequence[UpdateBloodPressureInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]
