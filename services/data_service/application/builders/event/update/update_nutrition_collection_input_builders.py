import random
from uuid import uuid4

from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.nutrition.drink import DrinkCategory, DrinkIdentifier
from services.base.domain.schemas.events.nutrition.food import FoodCategory, FoodIdentifier
from services.base.domain.schemas.events.nutrition.supplement import (
    SupplementCategory,
    SupplementIdentifier,
)
from services.data_service.application.builders.event.update.update_event_input_builder_base import (
    UpdateEventInputBuilderBase,
)
from services.data_service.application.use_cases.events.models.nutrition.update_nutrition_inputs import (
    UpdateDrinkInput,
    UpdateFoodInput,
    UpdateSupplementInput,
)


class UpdateDrinkInputBuilder(UpdateEventInputBuilderBase, DrinkIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateDrinkInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateDrinkInput(
            id=self._id or uuid4(),
            plan_extension=None,
            group_id=self._group_id,
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            type=DataType.Drink,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=DrinkCategory),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10, allow_none=True),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            nutrients=CustomModelsGenerator.generate_random_nutrients(allow_none=True),
            brand=PrimitiveTypesGenerator.generate_random_string(max_length=32, allow_none=True),
            calories=PrimitiveTypesGenerator.generate_random_float(max_value=10000, allow_none=True),
            consumed_amount=PrimitiveTypesGenerator.generate_random_float(max_value=1000),
            consumed_type=PrimitiveTypesGenerator.generate_random_enum(enum_type=VolumeUnit),
            flavor=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
        )


class UpdateFoodInputBuilder(UpdateEventInputBuilderBase, FoodIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateFoodInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateFoodInput(
            id=self._id or uuid4(),
            plan_extension=None,
            group_id=self._group_id,
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            type=DataType.Food,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=FoodCategory),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10, allow_none=True),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            nutrients=CustomModelsGenerator.generate_random_nutrients(allow_none=True),
            brand=PrimitiveTypesGenerator.generate_random_string(max_length=32, allow_none=True),
            calories=PrimitiveTypesGenerator.generate_random_float(max_value=10000, allow_none=True),
            consumed_amount=PrimitiveTypesGenerator.generate_random_float(max_value=1000),
            consumed_type=PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            flavor=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
        )


class UpdateSupplementInputBuilder(UpdateEventInputBuilderBase, SupplementIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateSupplementInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateSupplementInput(
            id=self._id or uuid4(),
            plan_extension=None,
            group_id=self._group_id,
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            type=DataType.Supplement,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=SupplementCategory),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10, allow_none=True),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            nutrients=CustomModelsGenerator.generate_random_nutrients(allow_none=True),
            brand=PrimitiveTypesGenerator.generate_random_string(max_length=32, allow_none=True),
            calories=PrimitiveTypesGenerator.generate_random_float(max_value=10000, allow_none=True),
            consumed_amount=PrimitiveTypesGenerator.generate_random_float(max_value=1000),
            consumed_type=PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            flavor=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
        )
