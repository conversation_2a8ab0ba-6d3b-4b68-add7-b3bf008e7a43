import random
from uuid import uuid4

from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.activity import ActivityCategory, ActivityIdentifier
from services.data_service.application.builders.event.update.update_event_input_builder_base import (
    UpdateEventInputBuilderBase,
)
from services.data_service.application.use_cases.events.models.update_activity_input import UpdateActivityInput


class UpdateActivityInputBuilder(UpdateEventInputBuilderBase, ActivityIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateActivityInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateActivityInput(
            id=self._id or uuid4(),
            type=DataType.Activity,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=ActivityCategory),
            plan_extension=None,
            group_id=self._group_id,
        )
