import random
from datetime import timed<PERSON><PERSON>
from typing import Sequence
from uuid import uuid4

from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetricCategory, BodyMetricIdentifier
from services.data_service.application.builders.event.update.update_event_input_builder_base import (
    UpdateEventInputBuilderBase,
)
from services.data_service.application.use_cases.events.models.body_metric.update_body_metric_inputs import (
    UpdateBodyMetricInput,
)


class UpdateBodyMetricInputBuilder(UpdateEventInputBuilderBase, BodyMetricIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateBodyMetricInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateBodyMetricInput(
            type=DataType.BodyMetric,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            value=PrimitiveTypesGenerator.generate_random_float(),
            id=self._id or uuid4(),
            note=PrimitiveTypesGenerator.generate_random_string(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=BodyMetricCategory),
            plan_extension=None,
            group_id=self._group_id,
        )

    def build_series(
        self, n: int | None = None, max_time_delta: timedelta | None = None
    ) -> Sequence[UpdateBodyMetricInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]
