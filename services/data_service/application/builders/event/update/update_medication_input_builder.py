from uuid import uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.medication.medication import (
    MedicationCategory,
    MedicationIdentifier,
    SingleDoseInformation,
    WeightUnit,
)
from services.data_service.application.builders.event.update.update_event_input_builder_base import (
    UpdateEventInputBuilderBase,
)
from services.data_service.application.use_cases.events.models.update_medication_input import UpdateMedicationInput


class UpdateMedicationInputBuilder(UpdateEventInputBuilderBase, MedicationIdentifier):

    def __init__(self):
        super().__init__()

    def build(self) -> UpdateMedicationInput:
        return UpdateMedicationInput(
            id=self._id or uuid4(),
            type=DataType.Medication,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=MedicationCategory),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            note=PrimitiveTypesGenerator.generate_random_string(),
            consumed_amount=PrimitiveTypesGenerator.generate_random_float(max_value=100),
            consume_unit=PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            plan_extension=None,
            group_id=self._group_id,
            single_dose_information=SingleDoseInformation(amount=100, amount_unit=WeightUnit.MG, items_quantity=1),
        )
