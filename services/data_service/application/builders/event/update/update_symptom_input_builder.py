import random
from uuid import uuid4

from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.body_location import BodyParts
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.symptom import Symptom<PERSON>ategory, SymptomIdentifier
from services.data_service.application.builders.event.update.update_event_input_builder_base import (
    UpdateEventInputBuilderBase,
)
from services.data_service.application.use_cases.events.models.update_symptom_input import UpdateSymptomInput


class UpdateSymptomInputBuilder(UpdateEventInputBuilderBase, SymptomIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateSymptomInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateSymptomInput(
            type=DataType.Symptom,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=SymptomCategory),
            id=self._id or uuid4(),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(allow_none=True, max_value=10),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            body_parts=[
                PrimitiveTypesGenerator.generate_random_enum(enum_type=BodyParts)
                for _ in range(PrimitiveTypesGenerator.generate_random_int(0, 3))
            ],
            plan_extension=None,
            group_id=self._group_id,
        )
