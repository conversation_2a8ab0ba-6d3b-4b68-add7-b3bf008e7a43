import random
from uuid import uuid4

from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.sleep_v3 import SleepV3<PERSON>ate<PERSON>y, SleepV3Identifier
from services.data_service.application.builders.event.update.update_event_input_builder_base import (
    UpdateEventInputBuilderBase,
)
from services.data_service.application.use_cases.events.models.update_sleep_v3_input import UpdateSleepV3Input


class UpdateSleepV3InputBuilder(UpdateEventInputBuilderBase, SleepV3Identifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateSleepV3Input:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateSleepV3Input(
            id=self._id or uuid4(),
            type=DataType.SleepV3,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=SleepV3Category),
            plan_extension=None,
            group_id=self._group_id,
            provider_score=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            llif_score=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            deep_seconds=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            light_seconds=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            rem_seconds=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            awake_seconds=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            restless_moments=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
        )
