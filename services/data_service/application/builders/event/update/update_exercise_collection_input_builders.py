import random
from datetime import timed<PERSON><PERSON>
from typing import Sequence
from uuid import uuid4

from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.exercise.cardio import CardioCategory, CardioIdentifier
from services.base.domain.schemas.events.exercise.exercise import ExerciseCategory, ExerciseIdentifier
from services.base.domain.schemas.events.exercise.strength import StrengthCategory, StrengthIdentifier
from services.data_service.application.builders.event.update.update_event_input_builder_base import (
    UpdateEventInputBuilderBase,
)
from services.data_service.application.use_cases.events.models.exercise.update_exercise_inputs import (
    UpdateCardioInput,
    UpdateExerciseInput,
    UpdateStrengthInput,
)


class UpdateExerciseInputBuilder(UpdateEventInputBuilderBase, ExerciseIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateExerciseInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateExerciseInput(
            type=DataType.Exercise,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=ExerciseCategory),
            id=self._id or uuid4(),
            plan_extension=None,
            group_id=self._group_id,
        )

    def build_series(
        self, n: int | None = None, max_time_delta: timedelta | None = None
    ) -> Sequence[UpdateExerciseInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]


class UpdateCardioInputBuilder(UpdateEventInputBuilderBase, CardioIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateCardioInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateCardioInput(
            type=DataType.Cardio,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            distance=PrimitiveTypesGenerator.generate_random_float(max_value=100_000),
            elevation=PrimitiveTypesGenerator.generate_random_float(min_value=-500, max_value=8848),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=CardioCategory),
            id=self._id or uuid4(),
            plan_extension=None,
            group_id=self._group_id,
        )

    def build_series(
        self, n: int | None = None, max_time_delta: timedelta | None = None
    ) -> Sequence[UpdateCardioInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]


class UpdateStrengthInputBuilder(UpdateEventInputBuilderBase, StrengthIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateStrengthInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateStrengthInput(
            type=DataType.Strength,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            count=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=100),
            weight=PrimitiveTypesGenerator.generate_random_float(max_value=500),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=StrengthCategory),
            id=self._id or uuid4(),
            plan_extension=None,
            group_id=self._group_id,
        )

    def build_series(
        self, n: int | None = None, max_time_delta: timedelta | None = None
    ) -> Sequence[UpdateStrengthInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]
