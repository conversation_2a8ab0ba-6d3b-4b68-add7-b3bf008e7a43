import bisect
from datetime import datetime, timedelta
from typing import Literal, Sequence

from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.events.document_base import TimestampDocument


class DocumentMatcher:

    @staticmethod
    def _find_greater_timestamp(docs: Sequence[TimestampDocument], ts: datetime) -> int:
        """
        Finds the index of the first document with timestamp >= target_timestamp.
        """
        if not docs:
            raise ValueError("Empty docs provided")
        timestamps = [doc.timestamp for doc in docs]
        return bisect.bisect_left(timestamps, ts)

    @classmethod
    def _find_docs_in_range[T: TimestampDocument](
        cls,
        docs: Sequence[T],
        gte: datetime,
        lte: datetime,
    ) -> Sequence[T]:
        matched_docs = []
        start_idx = cls._find_greater_timestamp(docs=docs, ts=gte)

        for i in range(start_idx, len(docs)):
            doc = docs[i]
            # Since docs are sorted, we can stop once we exceed the max timestamp
            if doc.timestamp > lte:
                break
            matched_docs.append(doc)
        return matched_docs

    @classmethod
    def find_matching_docs[T: TimestampDocument](
        cls,
        ts: datetime,
        docs: Sequence[T],
        temporal_type: Literal["before", "after", "closest"],
        delta_to: timedelta,
        delta_from: timedelta,
    ) -> Sequence[T]:
        """
        Find documents that match a bucket based on temporal relationship.
        delta_from: minimum distance from target timestamp
        delta_to: maximum distance from target timestamp
        """
        if temporal_type == "before":
            min_ts = ts - delta_to
            max_ts = ts - delta_from
            return cls._find_docs_in_range(docs=docs, gte=min_ts, lte=max_ts)

        elif temporal_type == "after":
            min_ts = ts + delta_from
            max_ts = ts + delta_to
            return cls._find_docs_in_range(docs=docs, gte=min_ts, lte=max_ts)

        elif temporal_type == "closest":
            # For closest, we need to check both before and after ranges
            before_min_ts = ts - delta_to
            before_max_ts = ts - delta_from
            after_min_ts = ts + delta_from
            after_max_ts = ts + delta_to

            # Get candidates from both ranges
            before_candidates = cls._find_docs_in_range(docs=docs, gte=before_min_ts, lte=before_max_ts)
            after_candidates = cls._find_docs_in_range(docs=docs, gte=after_min_ts, lte=after_max_ts)

            all_candidates = list(before_candidates) + list(after_candidates)

            if not all_candidates:
                return []

            final_closest_doc = min(all_candidates, key=lambda doc: abs((doc.timestamp - ts).total_seconds()))
            return [final_closest_doc]

        else:
            raise ShouldNotReachHereException(f"Unknown temporal relationship type {temporal_type}")
