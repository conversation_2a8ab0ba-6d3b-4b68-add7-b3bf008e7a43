from datetime import datetime, timedelta
from typing import Sequence
from zoneinfo import ZoneInfo

import pytest

from services.base.domain.schemas.events.document_base import TimestampDocument
from services.data_service.application.use_cases.event_correlation.document_matcher import DocumentMatcher


class TestDocumentMatcher:
    @pytest.mark.parametrize(
        "ts,doc_timestamps,delta_to,delta_from,expected_indices",
        [
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 11, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 30, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=15),
                [0, 1],
                id="before_1h_window_15min_min_distance",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 10, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 30, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=2),
                timedelta(minutes=30),
                [0, 1],
                id="before_2h_window_30min_min_distance",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 11, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 11, 45, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=30),
                [0, 1],
                id="before_with_30min_minimum_distance",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 11, 45, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=0),
                [0, 1, 2],
                id="before_1h_window_no_min_distance_includes_exact",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 10, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 10, 30, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(minutes=30),
                timedelta(minutes=0),
                [],
                id="before_no_matches_in_window",
            ),
        ],
    )
    def test_find_matching_docs_before(
        self,
        ts: datetime,
        doc_timestamps: Sequence[datetime],
        delta_to: timedelta,
        delta_from: timedelta,
        expected_indices: Sequence[int],
    ):
        # Arrange
        docs = [TimestampDocument(timestamp=dt) for dt in doc_timestamps]

        # Act
        result = DocumentMatcher.find_matching_docs(
            ts=ts, docs=docs, temporal_type="before", delta_to=delta_to, delta_from=delta_from
        )

        # Assert
        expected_docs = [docs[i] for i in expected_indices]
        assert len(result) == len(expected_docs)
        for doc in result:
            assert doc in expected_docs

    @pytest.mark.parametrize(
        "ts,doc_timestamps,delta_to,delta_from,expected_indices",
        [
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 15, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 45, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=10),
                [1, 2, 3],
                id="after_1h_window_10min_min_distance",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 45, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 13, 30, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=2),
                timedelta(minutes=20),
                [2, 3],
                id="after_2h_window_20min_min_distance",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 12, 15, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 45, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 13, 30, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=2),
                timedelta(minutes=30),
                [1, 2],
                id="after_with_30min_minimum_distance",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 13, 0, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=0),
                [1, 2, 3],
                id="after_1h_window_no_min_distance_includes_exact",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 11, 30, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=0),
                [],
                id="after_no_matches_all_before",
            ),
        ],
    )
    def test_find_matching_docs_after(
        self,
        ts: datetime,
        doc_timestamps: Sequence[datetime],
        delta_to: timedelta,
        delta_from: timedelta,
        expected_indices: Sequence[int],
    ):
        # Arrange
        docs = [TimestampDocument(timestamp=dt) for dt in doc_timestamps]

        # Act
        result = DocumentMatcher.find_matching_docs(
            ts=ts, docs=docs, temporal_type="after", delta_to=delta_to, delta_from=delta_from
        )

        # Assert
        expected_docs = [docs[i] for i in expected_indices]
        assert len(result) == len(expected_docs)
        for doc in result:
            assert doc in expected_docs

    @pytest.mark.parametrize(
        "ts,doc_timestamps,delta_to,delta_from,expected_indices",
        [
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 20, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 25, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=10),
                [1],
                id="closest_after_is_closer_with_min_distance",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 35, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 40, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=5),
                [0],
                id="closest_before_is_closer_with_min_distance",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 15, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=20),
                [0],
                id="closest_with_min_distance_returns_before_candidate",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 10, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 11, 45, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 10, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 14, 0, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=30),
                [],
                id="closest_no_matches_with_30min_min_distance",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 20, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=0),
                [1],
                id="closest_no_min_distance_returns_closest_after",
            ),
            pytest.param(
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 45, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                timedelta(minutes=15),
                [0],
                id="closest_both_in_range_returns_closer",
            ),
        ],
    )
    def test_find_matching_docs_closest(
        self,
        ts: datetime,
        doc_timestamps: Sequence[datetime],
        delta_to: timedelta,
        delta_from: timedelta,
        expected_indices: Sequence[int],
    ):
        # Arrange
        docs = [TimestampDocument(timestamp=dt) for dt in doc_timestamps]

        # Act
        result = DocumentMatcher.find_matching_docs(
            ts=ts, docs=docs, temporal_type="closest", delta_to=delta_to, delta_from=delta_from
        )

        # Assert
        expected_docs = [docs[i] for i in expected_indices]
        assert len(result) == len(expected_docs)
        for doc in result:
            assert doc in expected_docs

    @pytest.mark.parametrize(
        "docs, target_ts, expected_idx",
        [
            # Before all
            (
                [
                    TimestampDocument(timestamp=datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 3, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 4, tzinfo=ZoneInfo("UTC"))),
                ],
                datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC")),
                0,
            ),
            # After all
            (
                [
                    TimestampDocument(timestamp=datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 3, tzinfo=ZoneInfo("UTC"))),
                ],
                datetime(2023, 1, 4, tzinfo=ZoneInfo("UTC")),
                3,
            ),
            # Equal
            (
                [
                    TimestampDocument(timestamp=datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 3, tzinfo=ZoneInfo("UTC"))),
                ],
                datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC")),
                1,
            ),
            # Between
            (
                [
                    TimestampDocument(timestamp=datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 3, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 5, tzinfo=ZoneInfo("UTC"))),
                ],
                datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC")),
                1,
            ),
            # Documents with duplicate timestamps
            (
                [
                    TimestampDocument(timestamp=datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 3, tzinfo=ZoneInfo("UTC"))),
                ],
                datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC")),
                2,
            ),
        ],
    )
    def test_find_greater_timestamp_passes(
        self, docs: Sequence[TimestampDocument], target_ts: datetime, expected_idx: int
    ):
        # Act
        result = DocumentMatcher._find_greater_timestamp(docs=docs, ts=target_ts)

        # Assert
        assert result == expected_idx

    def test_find_greater_timestamp_empty_docs_raises(self):
        # Arrange
        docs = []
        target_ts = datetime(2023, 1, 1)

        # Act & Assert
        with pytest.raises(ValueError):
            DocumentMatcher._find_greater_timestamp(docs=docs, ts=target_ts)
