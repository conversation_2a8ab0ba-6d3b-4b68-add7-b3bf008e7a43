from abc import ABC
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, UniqueSequenceStr
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.events.event import EventFields, EventPlanExtension, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.data_service.application.use_cases.events.models.shared import (
    EventInputTimeInterval,
    IdentifiableInputDocument,
)


class UpdateEventInput(EventInputTimeInterval, IdentifiableInputDocument, TypeIdentifier, ABC):
    name: NonEmptyStr = Field(alias=EventFields.NAME, max_length=EventValueLimits.MAX_NAME_LENGTH, min_length=1)
    plan_extension: EventPlanExtension | None = Field(alias=EventFields.PLAN_EXTENSION)
    tags: UniqueSequenceStr = Field(alias=EventFields.TAGS, max_length=DocumentValueLimits.MaxTagsCount)
    group_id: UUID | None = Field(alias=EventFields.GROUP_ID, description="Moves event to different event group")
