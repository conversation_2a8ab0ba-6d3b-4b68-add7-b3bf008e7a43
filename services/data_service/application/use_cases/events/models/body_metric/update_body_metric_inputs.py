from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr, RoundedFloat
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.body_metric.blood_glucose import (
    BloodGlucoseCategory,
    BloodGlucoseFields,
    BloodGlucoseIdentifier,
    BloodGlucoseSpecimenSource,
    BloodGlucoseValueLimits,
)
from services.base.domain.schemas.events.body_metric.blood_pressure import (
    BloodPressureCategory,
    BloodPressureFields,
    BloodPressureIdentifier,
    BloodPressureValueLimits,
)
from services.base.domain.schemas.events.body_metric.body_metric import (
    BodyMetricCategory,
    BodyMetricFields,
    BodyMetricIdentifier,
)
from services.base.domain.schemas.events.event import EventValueLimits
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateBodyMetricInput(UpdateEventInput, BodyMetricIdentifier):
    type: Literal[DataType.BodyMetric] = Field(alias=BodyMetricFields.TYPE)
    category: BodyMetricCategory = Field(alias=BodyMetricFields.CATEGORY)
    value: RoundedFloat = Field(alias=BodyMetricFields.VALUE, ge=-(10**9), le=10**9)
    note: NonEmptyStr | None = Field(
        alias=BodyMetricFields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.value,
            ]
        )


class UpdateBloodPressureInput(UpdateEventInput, BloodPressureIdentifier):
    type: Literal[DataType.BloodPressure] = Field(alias=BloodPressureFields.TYPE)
    category: BloodPressureCategory = Field(alias=BloodPressureFields.CATEGORY)
    systolic: RoundedFloat = Field(
        alias=BloodPressureFields.SYSTOLIC,
        ge=BloodPressureValueLimits.MINIMUM_SYSTOLIC,
        le=BloodPressureValueLimits.MAXIMUM_SYSTOLIC,
    )
    diastolic: RoundedFloat = Field(
        alias=BloodPressureFields.DIASTOLIC,
        ge=BloodPressureValueLimits.MINIMUM_DIASTOLIC,
        le=BloodPressureValueLimits.MAXIMUM_DIASTOLIC,
    )
    note: NonEmptyStr | None = Field(
        alias=BloodPressureFields.NOTE, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.systolic,
                self.diastolic,
            ]
        )


class UpdateBloodGlucoseInput(UpdateEventInput, BloodGlucoseIdentifier):
    type: Literal[DataType.BloodGlucose] = Field(alias=BloodGlucoseFields.TYPE)
    category: BloodGlucoseCategory = Field(alias=BloodGlucoseFields.CATEGORY)
    value: RoundedFloat = Field(
        alias=BloodGlucoseFields.VALUE,
        ge=BloodGlucoseValueLimits.MINIMUM_VALUE,
        le=BloodGlucoseValueLimits.MAXIMUM_VALUE,
    )
    specimen_source: BloodGlucoseSpecimenSource = Field(alias=BloodGlucoseFields.SPECIMEN_SOURCE)
    note: NonEmptyStr | None = Field(
        alias=BloodGlucoseFields.NOTE, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.value,
            ]
        )
