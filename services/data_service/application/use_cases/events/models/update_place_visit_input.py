from typing import Literal
from uuid import UUID

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.place_category import PlaceCategory
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.events.place_visit import (
    PlaceVisitFields,
    PlaceVisitIdentifier,
)
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdatePlaceVisitInput(UpdateEventInput, PlaceVisitIdentifier):
    type: Literal[DataType.PlaceVisit] = Field(alias=EventFields.TYPE)
    category: PlaceCategory = Field(alias=PlaceVisitFields.CATEGORY)
    place_id: UUID | None = Field(alias=PlaceVisitFields.PLACE_ID)
    rating: int | None = Field(
        alias=PlaceVisitFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=EventFields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )
