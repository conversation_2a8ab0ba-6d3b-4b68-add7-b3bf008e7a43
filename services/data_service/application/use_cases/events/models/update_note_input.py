from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.events.note import Note<PERSON><PERSON><PERSON><PERSON>, Note<PERSON>ields, NoteIdentifier
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateNoteInput(UpdateEventInput, NoteIdentifier):
    type: Literal[DataType.Note] = Field(alias=EventFields.TYPE)
    category: NoteCategory = Field(alias=NoteFields.CATEGORY)
    note: NonEmptyStr = Field(
        alias=NoteFields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.note or "",
            ]
        )
