from typing import Literal
from uuid import uuid4

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.type_tree.activity_node import ActivityNode
from services.base.domain.schemas.events.activity import (
    Activity,
    ActivityCategory,
    ActivityFields,
    ActivityIdentifier,
)
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.type_tree.type_tree import type_tree
from services.data_service.application.use_cases.events.models.insert_event_input import (
    EventInsertionContext,
    InsertEventInput,
)


class InsertActivityInput(InsertEventInput, ActivityIdentifier):
    type: Literal[DataType.Activity] = Field(alias=EventFields.TYPE)
    category: ActivityCategory = Field(alias=EventFields.CATEGORY)
    node: ActivityNode | None = Field(alias=EventFields.NODE, default=None)
    rating: int | None = Field(
        alias=ActivityFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
        default=None,
    )
    note: NonEmptyStr | None = Field(
        alias=ActivityFields.NOTE, default=None, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self, ctx: EventInsertionContext) -> Activity:
        return Activity(
            # technical
            type=DataType.Activity,
            rbac=RBACSchema(owner_id=ctx.owner_id),
            group_id=ctx.group_id,
            submission_id=ctx.submission_id,
            template_id=self.template_id,
            id=uuid4(),
            category=self.category,
            node=self.node or type_tree.data_type_category_to_enum(data_type=DataType.Activity, category=self.category),
            plan_extension=self.plan_extension,
            # common
            timestamp=self.timestamp,
            end_time=self.end_time,
            metadata=ctx.metadata,
            tags=self.tags,
            asset_references=ctx.asset_references,
            name=self.name,
            # specific
            note=self.note,
            rating=self.rating,
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )
