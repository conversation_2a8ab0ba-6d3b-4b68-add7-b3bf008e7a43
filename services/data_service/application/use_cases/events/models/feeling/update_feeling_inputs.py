from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.events.feeling.emotion import EmotionCategory, EmotionFields, EmotionIdentifier
from services.base.domain.schemas.events.feeling.stress import (
    Stress<PERSON>ategory,
    StressFields,
    StressIdentifier,
    StressValueLimits,
)
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateEmotionInput(UpdateEventInput, EmotionIdentifier):
    type: Literal[DataType.Emotion] = Field(alias=EventFields.TYPE)
    category: EmotionCategory = Field(alias=EmotionFields.CATEGORY)
    rating: int = Field(
        alias=EmotionFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=EmotionFields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )


class UpdateStressInput(UpdateEventInput, StressIdentifier):
    type: Literal[DataType.Stress] = Field(alias=EventFields.TYPE)
    category: StressCategory = Field(alias=StressFields.CATEGORY)
    rating: int = Field(
        alias=StressFields.RATING,
        ge=StressValueLimits.STRESS_MINIMUM_VALUE,
        le=StressValueLimits.STRESS_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=StressFields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )
