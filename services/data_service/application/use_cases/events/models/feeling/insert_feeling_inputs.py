from typing import Literal
from uuid import uuid4

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.events.feeling.emotion import (
    Emotion,
    EmotionCategory,
    EmotionFields,
    EmotionIdentifier,
)
from services.base.domain.schemas.events.feeling.stress import (
    Stress,
    StressCategory,
    StressFields,
    StressIdentifier,
    StressValueLimits,
)
from services.data_service.application.use_cases.events.models.insert_event_input import (
    EventInsertionContext,
    InsertEventInput,
)


class InsertEmotionInput(InsertEventInput, EmotionIdentifier):
    type: Literal[DataType.Emotion] = Field(alias=EventFields.TYPE)
    category: EmotionCategory = Field(alias=EmotionFields.CATEGORY)
    rating: int = Field(
        alias=EmotionFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=EmotionFields.NOTE, default=None, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self, ctx: EventInsertionContext) -> Emotion:
        return Emotion(
            # technical
            type=DataType.Emotion,
            rbac=RBACSchema(owner_id=ctx.owner_id),
            group_id=ctx.group_id,
            submission_id=ctx.submission_id,
            template_id=self.template_id,
            id=uuid4(),
            category=self.category,
            plan_extension=self.plan_extension,
            # common
            timestamp=self.timestamp,
            end_time=self.end_time,
            metadata=ctx.metadata,
            tags=self.tags,
            asset_references=ctx.asset_references,
            name=self.name,
            # specific
            rating=self.rating,
            note=self.note,
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )


class InsertStressInput(InsertEventInput, StressIdentifier):
    type: Literal[DataType.Stress] = Field(alias=EventFields.TYPE)
    category: StressCategory = Field(alias=StressFields.CATEGORY)
    rating: int = Field(
        alias=StressFields.RATING,
        ge=StressValueLimits.STRESS_MINIMUM_VALUE,
        le=StressValueLimits.STRESS_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=StressFields.NOTE, default=None, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self, ctx: EventInsertionContext) -> Stress:
        return Stress(
            # technical
            type=DataType.Stress,
            rbac=RBACSchema(owner_id=ctx.owner_id),
            group_id=ctx.group_id,
            submission_id=ctx.submission_id,
            template_id=self.template_id,
            id=uuid4(),
            category=self.category,
            plan_extension=self.plan_extension,
            # common
            timestamp=self.timestamp,
            end_time=self.end_time,
            metadata=ctx.metadata,
            tags=self.tags,
            asset_references=ctx.asset_references,
            name=self.name,
            # specific
            rating=self.rating,
            note=self.note,
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )


InsertFeelingInputs = InsertEmotionInput | InsertStressInput
