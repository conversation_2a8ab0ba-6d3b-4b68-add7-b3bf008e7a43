from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.activity import ActivityCategory, ActivityFields, ActivityIdentifier
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateActivityInput(UpdateEventInput, ActivityIdentifier):
    type: Literal[DataType.Activity] = Field(alias=EventFields.TYPE)
    category: ActivityCategory = Field(alias=ActivityFields.CATEGORY)
    rating: int | None = Field(
        alias=ActivityFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=ActivityFields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )
