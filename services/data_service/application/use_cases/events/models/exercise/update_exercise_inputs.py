from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr, RoundedFloat
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.exercise.cardio import CardioCategory, CardioFields, CardioIdentifier
from services.base.domain.schemas.events.exercise.exercise import ExerciseCategory, ExerciseFields, ExerciseIdentifier
from services.base.domain.schemas.events.exercise.strength import StrengthCategory, StrengthFields, StrengthIdentifier
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateExerciseInput(UpdateEventInput, ExerciseIdentifier):
    type: Literal[DataType.Exercise] = Field(alias=ExerciseFields.TYPE)
    category: ExerciseCategory = Field(alias=ExerciseFields.CATEGORY)
    rating: int | None = Field(
        alias=ExerciseFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=ExerciseFields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )


class UpdateStrengthInput(UpdateEventInput, StrengthIdentifier):
    type: Literal[DataType.Strength] = Field(alias=StrengthFields.TYPE)
    category: StrengthCategory = Field(alias=ExerciseFields.CATEGORY)
    name: NonEmptyStr = Field(alias=ExerciseFields.NAME, max_length=EventValueLimits.MAX_NAME_LENGTH)
    count: int = Field(alias=StrengthFields.COUNT, ge=0, le=1000)
    weight: RoundedFloat | None = Field(alias=StrengthFields.WEIGHT, ge=0, le=500)
    rating: int | None = Field(
        alias=StrengthFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=StrengthFields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.count,
            ]
        )


class UpdateCardioInput(UpdateEventInput, CardioIdentifier):
    type: Literal[DataType.Cardio] = Field(alias=CardioFields.TYPE)
    category: CardioCategory = Field(alias=ExerciseFields.CATEGORY)
    name: NonEmptyStr = Field(alias=CardioFields.NAME)
    distance: RoundedFloat | None = Field(alias=CardioFields.DISTANCE, ge=0, le=100_000)
    elevation: RoundedFloat | None = Field(alias=CardioFields.ELEVATION, ge=-500, le=8848)
    rating: int | None = Field(
        alias=CardioFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=CardioFields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.distance,
            ]
        )
