from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.content import (
    ContentCategory,
    ContentFields,
    ContentIdentifier,
    ContentValueLimits,
)
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateContentInput(UpdateEventInput, ContentIdentifier):
    type: Literal[DataType.Content] = Field(alias=ContentFields.TYPE)
    category: ContentCategory = Field(alias=ContentFields.CATEGORY)
    title: NonEmptyStr | None = Field(alias=ContentFields.TITLE, max_length=ContentValueLimits.TITLE_MAX_LENGTH)
    url: str | None = Field(alias=ContentFields.URL, max_length=ContentValueLimits.URL_MAX_LENGTH)
    rating: int | None = Field(
        alias=ContentFields.RATING,
        ge=ContentValueLimits.CONTENT_RATING_MINIMUM_VALUE,
        le=ContentValueLimits.CONTENT_RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=ContentFields.NOTE,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.url,
                self.title,
            ]
        )
