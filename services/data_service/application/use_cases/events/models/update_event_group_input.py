from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.events.event_group import EventGroupFields, EventGroupIdentifier
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateEventGroupInput(UpdateEventInput, EventGroupIdentifier):
    type: Literal[DataType.EventGroup] = Field(alias=EventFields.TYPE)
    note: NonEmptyStr | None = Field(
        alias=EventGroupFields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        content_string: str = "".join(
            [
                self.name,
                self.timestamp.isoformat(),
            ]
        )
        return Hasher.content_sha256(content=content_string)
