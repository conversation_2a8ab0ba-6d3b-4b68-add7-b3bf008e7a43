from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr, Rounded6Float
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.medication.medication import (
    ConsumeUnit,
    MedicationCategory,
    MedicationFields,
    MedicationIdentifier,
    MedicationValueLimits,
    SingleDoseInformation,
    VolumeUnit,
    WeightUnit,
)
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateMedicationInput(UpdateEventInput, MedicationIdentifier):
    type: Literal[DataType.Medication] = Field(alias=MedicationFields.TYPE)
    category: MedicationCategory = Field(alias=MedicationFields.CATEGORY)
    single_dose_information: SingleDoseInformation = Field(alias=MedicationFields.SINGLE_DOSE_INFORMATION)

    consumed_amount: Rounded6Float = Field(
        alias=MedicationFields.CONSUMED_AMOUNT,
        ge=MedicationValueLimits.MIN_CONSUMED_QUANTITY,
        le=MedicationValueLimits.MAX_CONSUMED_QUANTITY,
    )
    consume_unit: VolumeUnit | WeightUnit | ConsumeUnit = Field(alias=MedicationFields.CONSUME_UNIT)
    note: NonEmptyStr | None = Field(
        alias=MedicationFields.NOTE, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
            ]
        )
