from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.events.sleep_v3 import SleepV3<PERSON>ategory, SleepV3Fields, SleepV3Identifier
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateSleepV3Input(UpdateEventInput, SleepV3Identifier):
    type: Literal[DataType.SleepV3] = Field(alias=EventFields.TYPE)
    category: SleepV3Category = Field(alias=SleepV3Fields.CATEGORY)
    rating: int | None = Field(
        alias=SleepV3Fields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=SleepV3Fields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )
    deep_seconds: int | None = Field(alias=SleepV3Fields.DEEP_SECONDS)
    light_seconds: int | None = Field(alias=SleepV3Fields.LIGHT_SECONDS)
    rem_seconds: int | None = Field(alias=SleepV3Fields.REM_SECONDS)
    awake_seconds: int | None = Field(alias=SleepV3Fields.AWAKE_SECONDS)
    restless_moments: int | None = Field(alias=SleepV3Fields.RESTLESS_MOMENTS)
    provider_score: int | None = Field(alias=SleepV3Fields.PROVIDER_SCORE)
    llif_score: int | None = Field(alias=SleepV3Fields.LLIF_SCORE)

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
            ]
        )
