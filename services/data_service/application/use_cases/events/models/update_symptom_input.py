from typing import Literal, Sequence

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.body_location import BodyParts
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.symptom import (
    SymptomCategory,
    SymptomFields,
    SymptomIdentifier,
    SymptomValueLimits,
)
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateSymptomInput(UpdateEventInput, SymptomIdentifier):
    type: Literal[DataType.Symptom] = Field(alias=SymptomFields.TYPE)
    category: SymptomCategory = Field(alias=SymptomFields.CATEGORY)
    rating: int | None = Field(
        alias=SymptomFields.RATING,
        ge=SymptomValueLimits.SYMPTOM_RATING_MINIMUM_VALUE,
        le=SymptomValueLimits.SYMPTOM_RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=SymptomFields.NOTE,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )
    body_parts: Sequence[BodyParts] = Field(alias=SymptomFields.BODY_PARTS)

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )
