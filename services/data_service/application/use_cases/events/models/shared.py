import binascii
from base64 import b64decode
from typing import Optional, Sequence
from uuid import UUID

from filetype import filetype
from pydantic import AwareDatetime, Field, model_validator

from services.base.application.assets import Assets
from services.base.application.size_constants import SIZE_MB
from services.base.domain.annotated_types import NonEmptyStr, UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.assets_enums import AssetType
from services.base.domain.enums.metadata_v3 import InsertableOrigin, SourceOS, SourceService
from services.base.domain.schemas.events.document_base import (
    DocumentValueLimits,
    EventMetadataFields,
    EventMetadataLimits,
)
from services.base.domain.schemas.shared import BaseDataModel


class EventMetadataInput(BaseDataModel):
    origin: InsertableOrigin = Field(alias=EventMetadataFields.ORIGIN)
    origin_device: NonEmptyStr | None = Field(
        max_length=EventMetadataLimits.ORIGIN_DEVICE_LENGTH, alias=EventMetadataFields.ORIGIN_DEVICE
    )
    source_os: SourceOS = Field(alias=EventMetadataFields.SOURCE_OS)
    source_service: SourceService = Field(alias=EventMetadataFields.SOURCE_SERVICE)


class EventInputTimestamp(BaseDataModel):
    timestamp: AwareDatetime = Field(alias=DocumentLabels.TIMESTAMP)


class EventInputTimeInterval(EventInputTimestamp):
    end_time: Optional[AwareDatetime] = Field(alias=DocumentLabels.END_TIME, default=None)

    @model_validator(mode="after")
    def validate_datetime_range_filter(self):
        if self.end_time:
            if self.timestamp > self.end_time:
                raise ValueError("timestamp has to be less than end_time")
        return self


class EventInputTags(BaseDataModel):
    tags: UniqueSequenceStr = Field(alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount)


class EventInputAsset(BaseDataModel):
    payload: bytes = Field(min_length=1, max_length=SIZE_MB)
    asset_type: AssetType = Field(...)
    name: NonEmptyStr = Field(min_length=1)

    @model_validator(mode="after")
    def validate_payload(self):
        try:
            # Try decoding the payload assuming it's base64 encoded
            payload = b64decode(self.payload, validate=True)
        except (binascii.Error, ValueError):
            # If decoding fails, assume it's raw binary data
            payload = self.payload

        if len(payload) > SIZE_MB:
            raise ValueError(f"Payload size exceeds maximum of {SIZE_MB} bytes")

        kind = filetype.guess(payload)
        if kind is None:
            raise ValueError("Cannot determine the file type.")

        mime_type = kind.mime

        if self.asset_type == AssetType.PDF and mime_type not in Assets.VALID_MIME_TYPES[AssetType.PDF]:
            raise ValueError(f"Invalid PDF file. Supported: {Assets.VALID_MIME_TYPES[AssetType.PDF]}")
        elif self.asset_type == AssetType.IMAGE and mime_type not in Assets.VALID_MIME_TYPES[AssetType.IMAGE]:
            raise ValueError(f"Invalid image file. Supported: {Assets.VALID_MIME_TYPES[AssetType.IMAGE]}")
        # elif self.asset_type == AssetType.VIDEO and mime_type not in Assets.VALID_MIME_TYPES[AssetType.VIDEO]:
        #    raise ValueError(f"Invalid video file. Supported: {Assets.VALID_MIME_TYPES[AssetType.VIDEO]}")
        # elif self.asset_type == AssetType.AUDIO and mime_type not in Assets.VALID_MIME_TYPES[AssetType.AUDIO]:
        #    raise ValueError(f"Invalid audio file. Supported: {Assets.VALID_MIME_TYPES[AssetType.AUDIO]}")
        return self


class EventInputAssetsDocument(BaseDataModel):
    assets: Sequence[EventInputAsset] = Field(default_factory=list)


class IdentifiableInputDocument(BaseDataModel):
    id: UUID = Field(alias=DocumentLabels.ID)
