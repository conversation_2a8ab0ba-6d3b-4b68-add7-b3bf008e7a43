from typing import Sequence
from uuid import UUID

from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
)
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.type_resolver import TypeResolver
from services.data_service.application.use_cases.events.event_validators import (
    DuplicatesValidator,
    GroupReferencesValidator,
)
from services.data_service.application.use_cases.events.models.update_event_input_boundary import (
    UpdateEventInputBoundary,
)
from services.data_service.application.use_cases.events.updatable_event_inputs import UpdatableEventInputs


class UpdateEventUseCase:
    def __init__(
        self,
        event_repo: EventRepository,
        duplicate_validator: DuplicatesValidator,
        group_validator: GroupReferencesValidator,
    ):
        self._event_repo = event_repo
        self._duplicate_validator = duplicate_validator
        self._group_validator = group_validator

    async def execute_async(self, boundary: UpdateEventInputBoundary, owner_id: UUID) -> Sequence[Event]:
        input_events = boundary.documents
        existing_events = await self._fetch_events_from_db(input_events=input_events, owner_id=owner_id)

        await self._validate_group_handling(
            existing_events=existing_events, input_events=input_events, owner_id=owner_id
        )

        updated_events = []
        events_needing_dup_check = []

        existing_event: Event
        # TODO: consider shifting to to_domain approach
        input_event: UpdatableEventInputs
        for existing_event, input_event in zip(
            sorted(existing_events, key=lambda e: e.id), sorted(input_events, key=lambda e: e.id)
        ):
            domain_type = TypeResolver.get_event(type_id=input_event.type_id())
            updated_event = domain_type(
                **(existing_event.model_dump(by_alias=True) | input_event.model_dump(by_alias=True))
            )
            updated_events.append(updated_event)
            if input_event.name != existing_event.name or input_event.timestamp != existing_event.timestamp:
                events_needing_dup_check.append(updated_event)

        # validate duplicates
        await self._duplicate_validator.validate(documents=events_needing_dup_check)

        updated_groups = await self._update_groups_for_events(existing_events, input_events)

        # Update all at once
        all_to_update = [*updated_events, *updated_groups]
        return await self._event_repo.update(events=all_to_update)

    async def _update_groups_for_events(
        self,
        existing_events: Sequence[Event],
        input_events: Sequence[UpdatableEventInputs],
    ) -> Sequence[EventGroup]:
        groups_to_update: dict[UUID, EventGroup] = {}

        for existing_event, input_event in zip(
            sorted(existing_events, key=lambda e: e.id),
            sorted(input_events, key=lambda e: e.id),
        ):
            old_group_id = existing_event.group_id
            new_group_id = input_event.group_id
            event_id = existing_event.id

            if old_group_id != new_group_id:
                # Remove from old group
                if old_group_id:
                    if old_group_id not in groups_to_update:
                        old_group = (await self._event_repo.search_by_id([old_group_id]))[0]
                        assert isinstance(old_group, EventGroup)
                        if old_group:
                            groups_to_update[old_group_id] = old_group

                    group = groups_to_update.get(old_group_id)
                    if group:
                        child_ids = list(group.child_ids)
                        if event_id in child_ids:
                            child_ids.remove(event_id)
                            group.child_ids = child_ids

                # Add to new group
                if new_group_id:
                    if new_group_id not in groups_to_update:
                        new_group = (await self._event_repo.search_by_id([new_group_id]))[0]
                        assert isinstance(new_group, EventGroup)
                        if new_group:
                            groups_to_update[new_group_id] = new_group

                    group = groups_to_update.get(new_group_id)
                    if group:
                        child_ids = list(group.child_ids)
                        if event_id not in child_ids:
                            child_ids.append(event_id)
                            group.child_ids = child_ids

        return list(groups_to_update.values())

    async def _fetch_events_from_db(
        self, input_events: Sequence[UpdatableEventInputs], owner_id: UUID
    ) -> Sequence[Event]:
        existing_events: Sequence[Event] = await self._event_repo.search_by_id(ids=[d.id for d in input_events])

        if len(input_events) != len(existing_events):
            ee = [e.id for e in existing_events]
            not_found_ids = [str(p.id) for p in input_events if p.id not in ee]
            raise IncorrectOperationException(message=f"Documents {not_found_ids} were not found")

        for existing_event, input_event in zip(existing_events, input_events):
            if existing_event.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to update some of the documents")
            if existing_event.type_id() != input_event.type_id():
                raise IncorrectOperationException(
                    message=f"Trying to change document type for document_id:{input_event.id}"
                )
        return existing_events

    async def _validate_group_handling(
        self, existing_events: Sequence[Event], input_events: Sequence[UpdatableEventInputs], owner_id: UUID
    ):
        events_linking_to_new_group = []
        for existing_event, input_event in zip(existing_events, input_events):
            if input_event.group_id and existing_event.group_id != input_event.group_id:
                events_linking_to_new_group.append(input_event)

        # validate groups
        updated_groups = await self._group_validator.validate(data=events_linking_to_new_group, owner_id=owner_id)

        # validate submissions
        await self._validate_submission_id_compatibility(
            input_events=events_linking_to_new_group, updated_groups=updated_groups, existing_events=existing_events
        )

    @staticmethod
    async def _validate_submission_id_compatibility(
        existing_events: Sequence[Event],
        input_events: Sequence[UpdatableEventInputs],
        updated_groups: Sequence[EventGroup],
    ) -> None:
        group_map = {group.id: group for group in updated_groups}
        event_map = {event.id: event for event in existing_events}

        for input_event in input_events:
            assert input_event.group_id
            existing_event = event_map[input_event.id]
            target_group = group_map[input_event.group_id]
            if existing_event.submission_id != target_group.submission_id:
                raise IncorrectOperationException(
                    message=f"Cannot move event {input_event.id} to group {input_event.group_id} of different submission."
                )
