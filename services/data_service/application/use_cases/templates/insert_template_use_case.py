from typing import Sequence
from uuid import UUID, uuid4

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_type import EventType
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.templates.template import Template
from services.data_service.application.use_cases.events.event_validators import DuplicatesValidator
from services.data_service.application.use_cases.templates.models.insert_template_input_boundary import (
    InsertableInputs,
    InsertEventTemplateInputBoundaryItem,
    InsertGroupTemplateInputBoundaryItem,
    InsertSeparateGroupTemplateInputBoundaryItem,
    InsertTemplateInputBoundary,
)
from services.data_service.application.use_cases.templates.template_validator import TemplateValidator


class InsertTemplateUseCase:
    def __init__(
        self,
        template_repo: TemplateRepository,
        duplicate_validator: Duplicates<PERSON>alida<PERSON>,
        template_validator: TemplateValidator,
    ):
        self._template_repo = template_repo
        self._duplicate_validator = duplicate_validator
        self._template_validator = template_validator

    async def execute_async(
        self,
        boundary: InsertTemplateInputBoundary,
        owner_id: UUID,
    ) -> Sequence[Template]:
        to_insert: list[Template] = await self._generate_templates_to_insert(
            documents=boundary.documents, owner_id=owner_id
        )
        await self._duplicate_validator.validate(documents=to_insert)

        return await self._template_repo.insert(templates=to_insert)

    async def _generate_templates_to_insert(
        self, documents: Sequence[InsertableInputs], owner_id: UUID
    ) -> list[Template]:
        to_insert: list[Template] = []

        for input_t in documents:
            if isinstance(input_t, InsertEventTemplateInputBoundaryItem):
                to_insert.append(
                    EventTemplate(
                        name=input_t.name,
                        document=input_t.document.to_domain(),
                        document_name=input_t.document.name,
                        rbac=RBACSchema(owner_id=owner_id),
                        type=DataType.EventTemplate,
                        document_type=EventType(input_t.document.type.value),
                        id=uuid4(),
                        tags=input_t.tags,
                        archived_at=None,
                    )
                )
            elif isinstance(input_t, InsertSeparateGroupTemplateInputBoundaryItem):
                await self._template_validator.fetch_and_validate_group_template_ids(
                    template_ids=input_t.template_ids, owner_id=owner_id
                )
                to_insert.append(
                    GroupTemplate(
                        name=input_t.name,
                        template_ids=input_t.template_ids,
                        rbac=RBACSchema(owner_id=owner_id),
                        type=DataType.GroupTemplate,
                        id=uuid4(),
                        tags=input_t.tags,
                        archived_at=None,
                    )
                )
            elif isinstance(input_t, InsertGroupTemplateInputBoundaryItem):
                templates_to_insert: list[Template] = [
                    EventTemplate(
                        name=template.name,
                        document=template.document.to_domain(),
                        document_name=template.document.name,
                        document_type=EventType(template.document.type.value),
                        rbac=RBACSchema(owner_id=owner_id),
                        type=DataType.EventTemplate,
                        id=uuid4(),
                        tags=template.tags,
                        archived_at=None,
                    )
                    for template in input_t.templates
                ]
                to_insert.extend(templates_to_insert)
                to_insert.append(
                    GroupTemplate(
                        name=input_t.name,
                        template_ids=[template.id for template in templates_to_insert],
                        rbac=RBACSchema(owner_id=owner_id),
                        type=DataType.GroupTemplate,
                        id=uuid4(),
                        tags=input_t.tags,
                        archived_at=None,
                    )
                )

        return to_insert
