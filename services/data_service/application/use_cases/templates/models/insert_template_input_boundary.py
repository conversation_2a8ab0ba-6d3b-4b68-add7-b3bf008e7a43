from typing import Sequence
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.schemas.templates.template import TemplateFields
from services.data_service.application.use_cases.templates.insert_template_payload_inputs import (
    InsertTemplatePayloadInputs,
)


class InsertTemplateInputBoundaryItemBase(BaseDataModel):
    name: NonEmptyStr = Field(alias=TemplateFields.NAME, min_length=1)
    tags: UniqueSequenceStr = Field(
        alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount, default_factory=list
    )


class InsertEventTemplateInputBoundaryItem(InsertTemplateInputBoundaryItemBase):
    document: InsertTemplatePayloadInputs = Field(alias=TemplateFields.DOCUMENT)


class InsertSeparateGroupTemplateInputBoundaryItem(InsertTemplateInputBoundaryItemBase):
    template_ids: Sequence[UUID] = Field(alias=TemplateFields.TEMPLATE_IDS, min_length=1)


class InsertGroupTemplateInputBoundaryItem(InsertTemplateInputBoundaryItemBase):
    templates: Sequence[InsertEventTemplateInputBoundaryItem] = Field(min_length=1)


InsertableInputs = (
    InsertSeparateGroupTemplateInputBoundaryItem
    | InsertEventTemplateInputBoundaryItem
    | InsertGroupTemplateInputBoundaryItem
)


class InsertTemplateInputBoundary(BaseDataModel):
    documents: Sequence[InsertableInputs] = Field(min_length=1)
