from typing import Sequence
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.schemas.templates.event_template import TypedTemplatePayloads
from services.base.domain.schemas.templates.template import TemplateFields


class UpdateTemplateInputBoundaryItem(BaseDataModel):
    id: UUID
    name: str = Field(alias=TemplateFields.NAME, min_length=1)
    tags: UniqueSequenceStr = Field(alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount)


class UpdateEventTemplateInputBoundaryItem(UpdateTemplateInputBoundaryItem):
    document: TypedTemplatePayloads = Field(alias=TemplateFields.DOCUMENT)


class UpdateGroupTemplateInputBoundaryItem(UpdateTemplateInputBoundaryItem):
    template_ids: Sequence[UUID] = Field(alias=TemplateFields.TEMPLATE_IDS, min_length=1)


UpdatableTemplateInputs = UpdateEventTemplateInputBoundaryItem | UpdateGroupTemplateInputBoundaryItem


class UpdateTemplateInputBoundary(BaseDataModel):
    documents: Sequence[UpdatableTemplateInputs] = Field(min_length=1)
