from datetime import datetime, timezone
from enum import StrEnum
from http import <PERSON><PERSON><PERSON>eth<PERSON>
from typing import List, Literal

from pydantic import Field

from services.base.application.io.httpclient import HttpClient
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.nutrition.drink import DrinkCategory
from services.base.domain.schemas.events.nutrition.food import FoodCategory
from services.base.domain.schemas.events.nutrition.nutrients import Nutrients
from services.base.domain.schemas.events.nutrition.supplement import SupplementCategory
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.lookup.nutrition_provider.nutrition_provider import (
    LookupProviderEnum,
    NutritionProvider,
    NutritionProviderInsertDrinkInput,
    NutritionProviderInsertFoodInput,
    NutritionProviderInsertSupplementInput,
    NutritionProviderOutput,
)


class UsdaAbridgedFoodNutrient(BaseDataModel):
    nutrient_id: int | None = Field(
        alias="nutrientId",
    )
    nutrient_name: str | None = Field(alias="nutrientName")
    nutrient_number: str | None = Field(alias="nutrientNumber")
    value: float | None = Field(alias="value")
    unit_name: str | None = Field(alias="unitName")


class UsdaSearchResultFood(BaseDataModel):
    fdc_id: int = Field(alias="fdcId")
    description: str = Field(alias="description")
    data_type: str | None = Field(alias="dataType")
    food_category: str | None = Field(alias="foodCategory")
    food_nutrients: List[UsdaAbridgedFoodNutrient] = Field(alias="foodNutrients")
    brand_name: str | None = Field(alias="brandName", default=None)
    serving_size: float | None = Field(alias="servingSize", default=None)
    serving_size_unit: str | None = Field(alias="servingSizeUnit", default=None)


class UsdaSearchResult(BaseDataModel):
    total_hits: int = Field(alias="totalHits")
    current_page: int = Field(alias="currentPage")
    total_pages: int = Field(alias="totalPages")
    foods: List[UsdaSearchResultFood] = Field(alias="foods")


class UsdaRelevantNutrientKey(StrEnum):
    Energy = "Energy"
    TotalFat = "Total lipid (fat)"
    SaturatedFat = "Fatty acids, total saturated"
    PolyUnsaturated = "Fatty acids, total polyunsaturated"
    MonoUnsaturated = "Fatty acids, total monounsaturated"
    TransFat = "Fatty acids, total trans"
    Cholesterol = "Cholesterol"
    Sodium = "Sodium, Na"
    Carbohydrates = "Carbohydrate, by difference"
    Fiber = "Fiber, total dietary"
    Sugar = "Total Sugars"
    SugarWithNLEA = "Sugars, total including NLEA"
    Protein = "Protein"
    VitaminA = "Vitamin A, IU"
    VitaminA_UG = "Vitamin A, RAE"
    VitaminC = "Vitamin C, total ascorbic acid"
    Calcium = "Calcium, Ca"
    Iron = "Iron, Fe"
    Potassium = "Potassium, K"
    Caffeine = "Caffeine"
    Copper = "Copper, Cu"
    Magnesium = "Magnesium, Mg"
    Niacin = "Niacin"
    Phosphorus = "Phosphorus, P"
    Riboflavin = "Riboflavin"
    Selenium = "Selenium, Se"
    Thiamin = "Thiamin"
    VitaminB6 = "Vitamin B-6"
    VitaminB12 = "Vitamin B-12"
    VitaminD = "Vitamin D"
    VitaminE = "Vitamin E"
    VitaminK = "Vitamin K (phylloquinone)"
    Zinc = "Zinc, Zn"
    Manganese = "Manganese, Mn"
    Molybdenum = "Molybdenum, Mo"
    Chromium = "Chromium, Cr"
    Biotin = "Biotin"
    FolateTotal = "Folate, total"
    Iodine = "Iodine, I"
    PantothenicAcid = "Pantothenic acid"


class UsdaProvider(NutritionProvider):
    def __init__(self, lookup_url: str, api_key: str, http_client: HttpClient):
        self._lookup_url = f"{lookup_url}?api_key={api_key}"
        self._http_client = http_client

    async def lookup_upc(self, upc: str) -> NutritionProviderOutput:
        return await self.lookup(name=f"gtinUpc:{upc}", size=1)

    async def lookup_image(self, image: bytes, name: str | None = None) -> NutritionProviderOutput:
        raise NotImplementedError("Image lookup is not supported for USDA provider")

    async def lookup(self, name: str, size: int = 5) -> NutritionProviderOutput:
        body = {
            "query": name,
            "pageSize": size,
        }

        response = await self._http_client.do_request(
            url=self._lookup_url,
            response_model=UsdaSearchResult,
            method=HTTPMethod.POST,
            body=body,
        )

        if not response.foods:
            raise ValueError(f"No food found matching '{name}'")

        results = []
        for food in response.foods:
            nutrients = self._extract_nutrients(food)
            calories = self._get_nutrient_key(food, UsdaRelevantNutrientKey.Energy)
            consumed_amount = food.serving_size or 100
            brand = None
            if food.data_type == "Branded":
                brand = food.brand_name

            consumed_type = self._get_serving_unit(food.serving_size_unit)

            # Determine if it's a drink or supplement
            description_lower = food.description.lower()
            category_lower = food.food_category.lower() if food.food_category else ""

            # Check for drinks
            drink_keywords = ["beverage", "drink", "juice", "water", "soda", "coffee", "tea", "milk", "smoothie"]
            if any(keyword in description_lower for keyword in drink_keywords) or "beverages" in category_lower:
                drink_consumed_type = "serving" if isinstance(consumed_type, WeightUnit) else consumed_type
                results.append(
                    NutritionProviderInsertDrinkInput(
                        type=DataType.Drink,
                        category=DrinkCategory.OTHER,
                        timestamp=datetime.now(timezone.utc),
                        name=food.description.lower(),
                        consumed_type=drink_consumed_type,
                        consumed_amount=consumed_amount,
                        calories=calories,
                        nutrients=nutrients,
                        brand=brand,
                        flavor=None,
                        rating=None,
                        note=None,
                        provider=LookupProviderEnum.USDA_FOOD_DB,
                    )
                )
                continue

            # Check for supplements
            supplement_keywords = ["supplement", "vitamin", "mineral", "protein powder", "powder", "capsule", "tablet"]
            if any(keyword in description_lower for keyword in supplement_keywords) or "supplements" in category_lower:
                results.append(
                    NutritionProviderInsertSupplementInput(
                        type=DataType.Supplement,
                        category=SupplementCategory.OTHER,
                        timestamp=datetime.now(timezone.utc),
                        name=food.description.lower(),
                        consumed_type=consumed_type,
                        consumed_amount=consumed_amount,
                        calories=calories,
                        nutrients=nutrients,
                        brand=brand,
                        flavor=None,
                        rating=None,
                        note=None,
                        provider=LookupProviderEnum.USDA_FOOD_DB,
                    )
                )
                continue

            # Default to food
            results.append(
                NutritionProviderInsertFoodInput(
                    type=DataType.Food,
                    category=FoodCategory.OTHER,
                    timestamp=datetime.now(timezone.utc),
                    name=food.description.lower(),
                    consumed_type=consumed_type,
                    consumed_amount=consumed_amount,
                    calories=calories,
                    nutrients=nutrients,
                    brand=brand,
                    flavor=None,
                    rating=None,
                    note=None,
                    provider=LookupProviderEnum.USDA_FOOD_DB,
                )
            )
        return NutritionProviderOutput(documents=results)

    def _get_serving_unit(self, unit: str | None) -> VolumeUnit | WeightUnit | Literal["item", "serving"]:
        if unit is None:
            return WeightUnit.G
        elif unit in ["g", "grm"]:
            return WeightUnit.G
        elif unit in ["ml", "mlt", "MLT"]:
            return VolumeUnit.ML
        elif unit in ["fl_oz"]:
            return VolumeUnit.FL_OZ
        elif unit in ["mcg", "ug"]:
            return WeightUnit.MG
        else:
            return WeightUnit.G

    def _get_nutrient_key(self, food: UsdaSearchResultFood, nutrient_key: UsdaRelevantNutrientKey) -> float | None:
        for nutrient in food.food_nutrients:
            if nutrient.nutrient_name == nutrient_key.value:
                # convert IU to mcg for vitamin A
                if nutrient_key == UsdaRelevantNutrientKey.VitaminA:
                    return nutrient.value * 0.3 if nutrient.value else None
                return nutrient.value
        return None

    def _extract_nutrients(self, food: UsdaSearchResultFood) -> Nutrients:
        """Extract nutrient data from USDA food response."""
        vitaminAifIU = self._get_nutrient_key(food, UsdaRelevantNutrientKey.VitaminA)
        vitaminAifMCG = self._get_nutrient_key(food, UsdaRelevantNutrientKey.VitaminA_UG)
        vitaminA = vitaminAifIU or vitaminAifMCG

        # Create Nutrients instance with all fields set to None
        nutrients = Nutrients(
            fat=self._get_nutrient_key(food, UsdaRelevantNutrientKey.TotalFat),
            saturated_fat=self._get_nutrient_key(food, UsdaRelevantNutrientKey.SaturatedFat),
            polyunsaturated_fat=self._get_nutrient_key(food, UsdaRelevantNutrientKey.PolyUnsaturated),
            monounsaturated_fat=self._get_nutrient_key(food, UsdaRelevantNutrientKey.MonoUnsaturated),
            trans_fat=self._get_nutrient_key(food, UsdaRelevantNutrientKey.TransFat),
            cholesterol=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Cholesterol),
            carbohydrates=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Carbohydrates),
            fiber=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Fiber),
            sugar=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Sugar),
            protein=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Protein),
            sodium=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Sodium),
            potassium=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Potassium),
            vitamin_a=vitaminA,
            vitamin_c=self._get_nutrient_key(food, UsdaRelevantNutrientKey.VitaminC),
            iron=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Iron),
            calcium=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Calcium),
            caffeine=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Caffeine),
            chromium=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Chromium),
            copper=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Copper),
            folate=self._get_nutrient_key(food, UsdaRelevantNutrientKey.FolateTotal),
            iodine=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Iodine),
            niacin=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Niacin),
            phosphorus=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Phosphorus),
            riboflavin=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Riboflavin),
            selenium=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Selenium),
            thiamin=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Thiamin),
            vitamin_b6=self._get_nutrient_key(food, UsdaRelevantNutrientKey.VitaminB6),
            vitamin_b12=self._get_nutrient_key(food, UsdaRelevantNutrientKey.VitaminB12),
            vitamin_d=self._get_nutrient_key(food, UsdaRelevantNutrientKey.VitaminD),
            vitamin_e=self._get_nutrient_key(food, UsdaRelevantNutrientKey.VitaminE),
            vitamin_k=self._get_nutrient_key(food, UsdaRelevantNutrientKey.VitaminK),
            zinc=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Zinc),
            biotin=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Biotin),
            pantothenic_acid=self._get_nutrient_key(food, UsdaRelevantNutrientKey.PantothenicAcid),
            magnesium=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Magnesium),
            manganese=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Manganese),
            molybdenum=self._get_nutrient_key(food, UsdaRelevantNutrientKey.Molybdenum),
            # Not found in USDA nutrition mapping
            chloride=None,
        )

        return nutrients
