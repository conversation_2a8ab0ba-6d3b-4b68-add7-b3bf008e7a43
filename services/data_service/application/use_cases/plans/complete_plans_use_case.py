import itertools
from asyncio import TaskGroup
from datetime import timed<PERSON><PERSON>
from typing import Sequence
from uuid import UUID

from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
)
from services.base.domain.enums.metadata_v3 import Insertable<PERSON><PERSON>in, SourceOS, SourceService
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.events.event import Event, EventPlanExtension
from services.base.domain.schemas.plan.plan import Plan
from services.base.domain.schemas.templates.event_template import EventTemplate, TypedTemplatePayloads
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.templates.template import Template
from services.data_service.application.use_cases.events.insert_event_use_case import InsertEventUseCase
from services.data_service.application.use_cases.events.models.insert_event_input_boundary import (
    InsertEventInputBoundary,
)
from services.data_service.application.use_cases.events.models.shared import EventMetadataInput
from services.data_service.application.use_cases.plans.complete_plan_helpers import CompletePlanHelpers
from services.data_service.application.use_cases.plans.models.complete_plans_input_boundary import (
    CompletePlanInput,
    CompletePlansInputBoundary,
)
from services.data_service.application.use_cases.plans.models.complete_plans_output_boundary import (
    CompletePlansOutputBoundary,
    CompletePlansOutputBoundaryItem,
)
from services.data_service.application.use_cases.templates.template_validator import TemplateValidator
from services.data_service.type_resolver import TypeResolver


class CompletePlansUseCase:
    def __init__(
        self,
        plan_repo: PlanRepository,
        template_repo: TemplateRepository,
        insert_event_uc: InsertEventUseCase,
        template_validator: TemplateValidator,
    ):
        self._plan_repo = plan_repo
        self._template_repo = template_repo
        self._insert_event_uc = insert_event_uc
        self._template_validator = template_validator

    async def execute_async(
        self,
        input_boundary: CompletePlansInputBoundary,
    ) -> CompletePlansOutputBoundary:
        input_plans = input_boundary.documents
        existing_plans = await self._fetch_plans_from_db(complete_inputs=input_plans, owner_id=input_boundary.owner_id)

        completed_plans = []
        event_inputs = []

        complete_input: CompletePlanInput
        plan: Plan
        for complete_input, plan in zip(
            sorted(input_plans, key=lambda p: p.id), sorted(existing_plans, key=lambda p: p.id)
        ):
            updated_plan = CompletePlanHelpers.complete_plan(complete_input=complete_input, existing_plan=plan)
            completed_plans.append(updated_plan)

            template = (
                await self._template_validator.fetch_and_validate_templates(
                    template_ids=[plan.template_id], owner_id=input_boundary.owner_id
                )
            )[0]
            returned_event_inputs = await self._collect_insert_event_inputs(
                template=template, complete_input=complete_input, owner_id=input_boundary.owner_id
            )
            for event_input in returned_event_inputs:
                event_inputs.append(event_input)

        async with TaskGroup() as group:
            tasks = [
                group.create_task(self._insert_event_uc.execute_async(boundary=b, owner_id=input_boundary.owner_id))
                for b in event_inputs
            ]
        events: Sequence[Event] = list(itertools.chain(*[t.result() for t in tasks]))
        updated_plans = await self._plan_repo.update(plans=completed_plans)
        out = []
        for p in updated_plans:
            docs = [e for e in events if e.plan_extension and (e.plan_extension.plan_id == p.id)]
            out.append(CompletePlansOutputBoundaryItem(events=docs, plan=p))
        return CompletePlansOutputBoundary(documents=out)

    async def _fetch_plans_from_db(
        self, complete_inputs: Sequence[CompletePlanInput], owner_id: UUID
    ) -> Sequence[Plan]:
        existing_plans: Sequence[Plan] = await self._plan_repo.search_by_id(ids=[d.id for d in complete_inputs])

        if len(complete_inputs) != len(existing_plans):
            existing_ids = [e.id for e in existing_plans]
            not_found_ids = [str(p.id) for p in complete_inputs if p.id not in existing_ids]
            raise IncorrectOperationException(message=f"Plans {not_found_ids} were not found")

        for plan in existing_plans:
            if plan.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to access some of the documents")
            if plan.archived_at:
                raise IncorrectOperationException(message="completing archived plan")
        return existing_plans

    async def _collect_insert_event_inputs(
        self, template: Template, complete_input: CompletePlanInput, owner_id: UUID
    ) -> Sequence[InsertEventInputBoundary]:
        if isinstance(template, EventTemplate):
            payload = template.document
            if complete_input.payload:
                if not isinstance(complete_input.payload, type(template.document)):
                    raise IncorrectOperationException(
                        f"failed to complete plan {complete_input.id} - input payload mismatches template payload type {template.document_type.value}"
                    )
                payload = complete_input.payload
            return self._collect_from_event_template_payload(
                payload=payload,
                complete_input=complete_input,
                template_id=template.id,
            )
        elif isinstance(template, GroupTemplate):
            return await self._collect_from_group_template(
                template=template, owner_id=owner_id, complete_input=complete_input
            )

        else:
            raise ShouldNotReachHereException(f"unexpected template input {type(template)}")

    @staticmethod
    def _collect_from_event_template_payload(
        payload: TypedTemplatePayloads, template_id: UUID, complete_input: CompletePlanInput
    ) -> Sequence[InsertEventInputBoundary]:
        timestamp = complete_input.completed_at
        end_time = (timestamp + timedelta(seconds=payload.duration)) if payload.duration else None
        insertable_model = TypeResolver.get_insert_event_input(type_id=payload.type_id())

        event_input = insertable_model(
            **payload.model_dump(by_alias=True),
            timestamp=timestamp,
            end_time=end_time,
            template_id=template_id,
            plan_extension=EventPlanExtension(plan_id=complete_input.id),
        )
        return [
            InsertEventInputBoundary(
                documents=[event_input],
                metadata=EventMetadataInput(
                    origin=InsertableOrigin.LLIF,
                    origin_device=None,
                    source_os=SourceOS.UNKNOWN,
                    source_service=SourceService.BEST_LIFE_APP,
                ),
            )
        ]

    async def _collect_from_group_template(
        self, template: GroupTemplate, complete_input: CompletePlanInput, owner_id: UUID
    ) -> Sequence[InsertEventInputBoundary]:
        templates = await self._template_validator.fetch_and_validate_templates(
            template_ids=template.template_ids, owner_id=owner_id
        )
        if not templates:
            raise ShouldNotReachHereException(f"No associated templates for group template with id:{template.id}")
        event_inputs = []
        for child_template in templates:
            event_input = await self._collect_insert_event_inputs(
                template=child_template, complete_input=complete_input, owner_id=owner_id
            )
            event_inputs.extend(event_input)
        return event_inputs
