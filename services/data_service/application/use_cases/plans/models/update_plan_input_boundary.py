from typing import Literal, Sequence
from uuid import UUID

from pydantic import AwareDatetime, field_validator, model_validator
from pydantic.fields import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr, UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.content_hash import Hasher
from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.priority import Priority
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.plan.plan import PlanFields
from services.base.domain.schemas.plan.plan_base import PlanBaseValueLimits, Streak
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.events.models.shared import IdentifiableInputDocument


class UpdatePlanInput(IdentifiableInputDocument):
    type: Literal[DataType.Plan] = Field(alias=PlanFields.TYPE)

    name: NonEmptyStr = Field(alias=PlanFields.NAME, min_length=1, max_length=PlanBaseValueLimits.MAX_NAME_LENGTH)
    template_id: UUID = Field(alias=PlanFields.TEMPLATE_ID)
    next_scheduled_at: AwareDatetime = Field(alias=PlanFields.NEXT_SCHEDULED_AT)
    streak: Streak = Field(alias=PlanFields.STREAK)
    recurrence: NonEmptyStr | None = Field(
        alias=PlanFields.RECURRENCE, description="Expects format of 'DTSTART:'tz_aware_iso8601}\n{OPTS}"
    )
    is_absolute_schedule: bool = Field(alias=PlanFields.IS_ABSOLUTE_SCHEDULE)
    is_urgent: bool = Field(alias=PlanFields.IS_URGENT)
    is_confirmation_required: bool = Field(alias=PlanFields.IS_CONFIRMATION_REQUIRED)
    priority: Priority = Field(alias=PlanFields.PRIORITY, default=Priority.UNSET)
    prompt: NonEmptyStr | None = Field(
        alias=PlanFields.PROMPT, min_length=1, max_length=PlanBaseValueLimits.MAX_PROMPT_LENGTH
    )
    current_completed: int = Field(alias=PlanFields.CURRENT_COMPLETED, ge=0)
    total_completed_on_time: int = Field(alias=PlanFields.TOTAL_COMPLETED_ON_TIME, ge=0)

    note: NonEmptyStr | None = Field(alias=PlanFields.NOTE, min_length=1)
    max_completed: int | None = Field(alias=PlanFields.MAX_COMPLETED, ge=0)
    tags: UniqueSequenceStr = Field(alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount)

    @model_validator(mode="after")
    def validate_model(self):
        if self.max_completed and self.current_completed > self.max_completed:
            raise ValueError(f"{PlanFields.CURRENT_COMPLETED} must be less than {PlanFields.MAX_COMPLETED}")
        if not self.recurrence:
            if not self.max_completed or (self.max_completed - self.current_completed) > 1:
                raise ValueError(
                    f"Difference between {PlanFields.MAX_COMPLETED} and {PlanFields.CURRENT_COMPLETED} must be 1 if {PlanFields.RECURRENCE} is not set"
                )
            return self

        try:
            recurrence = CustomRRule.from_rrule(rule=self.recurrence)
        except ValueError as err:
            raise ValueError(f"invalid recurrence, err: {err}")
        except Exception:
            raise ValueError("error parsing recurrence")

        started_at = recurrence._dtstart  # pyright: ignore
        if self.next_scheduled_at < started_at:
            raise ValueError(f"{PlanFields.RECURRENCE} dtstart must be less than {PlanFields.NEXT_SCHEDULED_AT}")
        # Ensure plan has a next tick
        next_tick = recurrence.after(dt=started_at)
        if not next_tick:
            raise ValueError("Given plan cannot be scheduled")
        return self

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.content_sha256(
            content=self.name
            + (self.recurrence or "")
            + str(self.template_id)
            + (self.next_scheduled_at.isoformat() if self.next_scheduled_at else "")
        )


class UpdatePlansInputBoundary(BaseDataModel):
    documents: Sequence[UpdatePlanInput] = Field(min_length=1)
    owner_id: UUID

    @field_validator("documents")
    def duplicates_validator(cls, documents: Sequence[UpdatePlanInput]) -> Sequence[UpdatePlanInput]:
        ids = {d.id for d in documents}
        if len(ids) != len(documents):
            raise ValueError("multiple update inputs for single document provided")
        content_hashes = {d.content_hash for d in documents}
        if len(content_hashes) != len(documents):
            raise ValueError("request contains duplicate entries")
        return documents
