from typing import Sequence
from uuid import UUID

from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
)
from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.plan.plan import Plan
from services.data_service.application.use_cases.events.event_validators import DuplicatesValidator
from services.data_service.application.use_cases.plans.models.update_plan_input_boundary import (
    UpdatePlanInput,
    UpdatePlansInputBoundary,
)


class UpdatePlansUseCase:
    def __init__(
        self,
        plan_repo: PlanRepository,
        template_repo: TemplateRepository,
        duplicate_validator: DuplicatesValidator,
    ):
        self._plan_repo = plan_repo
        self._template_repo = template_repo
        self._duplicate_validator = duplicate_validator

    async def execute_async(
        self,
        input_boundary: UpdatePlansInputBoundary,
    ) -> Sequence[Plan]:
        input_plans = input_boundary.documents
        existing_plans = await self._fetch_plans_from_db(input_plans=input_plans, owner_id=input_boundary.owner_id)
        if not existing_plans:
            return []
        await self._check_template_ids(
            template_ids=[d.template_id for d in input_plans], owner_id=input_boundary.owner_id
        )

        updated_plans = []
        plans_needing_dup_check = []
        input_p: UpdatePlanInput
        existing_p: Plan
        for input_p, existing_p in zip(
            sorted(input_plans, key=lambda p: p.id), sorted(existing_plans, key=lambda p: p.id)
        ):
            recurrence = CustomRRule.from_rrule(rule=input_p.recurrence) if input_p.recurrence else None
            in_rec_serialized = str(recurrence) if recurrence else None
            existing_rec_serialized = str(existing_p.recurrence) if existing_p.recurrence else None
            is_rec_updated = in_rec_serialized != existing_rec_serialized

            if recurrence and is_rec_updated:
                dtstart = input_p.next_scheduled_at
                recurrence = CustomRRule(
                    dtstart=dtstart,
                    freq=recurrence._freq,  # pyright: ignore
                    interval=recurrence._interval,  # pyright: ignore
                    wkst=recurrence._wkst,  # pyright: ignore
                    until=recurrence._until,  # pyright: ignore
                    bysetpos=recurrence._bysetpos,  # pyright: ignore
                    byweekday=recurrence.by_weekday,  # pyright: ignore
                    bymonthday=recurrence.by_monthday,  # pyright: ignore
                )

            updated_plan = Plan(
                type=DataType.Plan,
                id=existing_p.id,
                rbac=existing_p.rbac,
                metadata=existing_p.metadata,
                first_completed_at=existing_p.first_completed_at if not is_rec_updated else None,
                # from input
                name=input_p.name,
                template_id=input_p.template_id,
                next_scheduled_at=input_p.next_scheduled_at,
                is_urgent=input_p.is_urgent,
                is_confirmation_required=input_p.is_confirmation_required,
                streak=input_p.streak,
                recurrence=recurrence,
                note=input_p.note,
                is_absolute_schedule=input_p.is_absolute_schedule,
                priority=input_p.priority,
                prompt=input_p.prompt,
                current_completed=input_p.current_completed,
                max_completed=input_p.max_completed,
                tags=input_p.tags,
                archived_at=None,
                total_completed_on_time=input_p.total_completed_on_time if not is_rec_updated else 0,
            )

            updated_plans.append(updated_plan)

            if any(
                (
                    input_p.name != existing_p.name,
                    is_rec_updated,
                    input_p.next_scheduled_at != existing_p.next_scheduled_at,
                )
            ):
                plans_needing_dup_check.append(updated_plan)

        await self._duplicate_validator.validate(documents=plans_needing_dup_check)
        return await self._plan_repo.update(plans=updated_plans)

    async def _fetch_plans_from_db(self, input_plans: Sequence[UpdatePlanInput], owner_id: UUID) -> Sequence[Plan]:
        existing_plans: Sequence[Plan] = await self._plan_repo.search_by_id(ids=[d.id for d in input_plans])
        if len(input_plans) != len(existing_plans):
            ep_ids = [ep.id for ep in existing_plans]
            not_found_ids = [str(p.id) for p in input_plans if p.id not in ep_ids]
            raise IncorrectOperationException(message=f"Documents {not_found_ids} were not found")

        for plan in existing_plans:
            if plan.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to update some of the documents")
            if plan.archived_at:
                raise IncorrectOperationException(message="updating archived plan")
        return existing_plans

    async def _check_template_ids(self, template_ids: Sequence[UUID], owner_id: UUID):
        existing_templates = await self._template_repo.search_by_id(ids=template_ids)

        if len(template_ids) != len(existing_templates):
            et_ids = [et.id for et in existing_templates]
            not_found_ids = [str(id) for id in template_ids if id not in et_ids]
            raise IncorrectOperationException(message=f"Templates {not_found_ids} were not found")

        for template in existing_templates:
            if template.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to access some of the documents")
