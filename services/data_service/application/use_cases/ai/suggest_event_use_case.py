from datetime import datetime, timezone
from uuid import UUID
from zoneinfo import ZoneInfo

from pydantic import BaseModel, ValidationError
from pydantic_ai import Agent, ModelHTTPError
from pydantic_ai.models import Model

from services.base.application.retry import retry
from services.base.application.use_case_base import UseCaseBase
from services.base.application.utils.member_user.member_user_settings import get_user_timezone
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.data_service.application.use_cases.ai.classify_event_type_use_case import ClassifyEventTypeUseCase
from services.data_service.application.use_cases.ai.prompts.prompt_schemas import PromptSchema
from services.data_service.application.use_cases.ai.prompts.suggest_event_prompt import (
    UserTemplate,
    generate_suggest_event_prompt,
)
from services.data_service.application.use_cases.ai.suggest_template_use_case import (
    SuggestTemplateUseCase,
    SuggestTemplateUseCaseInputBoundary,
)
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs


class SuggestEventUseCaseInputBoundary(BaseModel):
    query: NonEmptyStr


class SuggestEventUseCase(UseCaseBase):
    def __init__(
        self,
        model: Model,
        suggest_template_use_case: SuggestTemplateUseCase,
        member_user_settings_repo: MemberUserSettingsRepository,
        classify_event_type_uc: ClassifyEventTypeUseCase,
    ):
        self._model = model
        self._suggest_template_use_case = suggest_template_use_case
        self._member_user_settings_repo = member_user_settings_repo
        self._classify_event_type_uc = classify_event_type_uc

    @retry(exceptions=(ValidationError, ModelHTTPError), max_times=3, delay=0.0)
    async def execute(self, user_uuid: UUID, input_boundary: SuggestEventUseCaseInputBoundary) -> InsertEventInputs:
        prompt_schema = await self._classify_event_type_uc.execute(query=input_boundary.query)

        suggested_template = await self._suggest_template_use_case.execute(
            user_uuid=user_uuid, input_boundary=SuggestTemplateUseCaseInputBoundary(query=input_boundary.query)
        )

        serialized_template = self._serialize_template(suggested_template) if suggested_template else None

        user_timezone = await get_user_timezone(
            uuid=user_uuid,
            member_user_settings_repo=self._member_user_settings_repo,
        )

        return await self._generate_event(
            query=input_boundary.query,
            schema=prompt_schema,
            template=serialized_template,
            user_timezone=user_timezone,
        )

    def _serialize_template(self, template: EventTemplate) -> dict:
        return UserTemplate(
            template_id=template.id,
            template_name=template.document_name,
            document=template.document.model_dump(by_alias=True),
        ).model_dump(by_alias=True)

    async def _generate_event(
        self,
        query: str,
        schema: PromptSchema,
        user_timezone: ZoneInfo,
        template: dict | None,
    ) -> InsertEventInputs:
        current_timestamp = datetime.now(timezone.utc).isoformat()
        instructions = generate_suggest_event_prompt(
            schema=schema,
            template=template,
            current_timestamp=current_timestamp,
        )
        agent = Agent(model=self._model, instructions=instructions, output_type=schema.event_type)
        response = await agent.run(user_prompt=query, output_type=schema.event_type)
        event = response.output

        # We replace the timezones to match the user's timezone while keeping the logic of the LLM timestamp in UTC
        event.timestamp = event.timestamp.replace(tzinfo=user_timezone)
        if event.end_time:
            event.end_time = event.end_time.replace(tzinfo=user_timezone)

        return response.output
