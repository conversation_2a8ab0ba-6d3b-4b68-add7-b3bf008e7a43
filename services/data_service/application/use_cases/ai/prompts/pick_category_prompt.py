pick_category_prompt = """
You are an AI assistant that determines the most appropriate category for a given natural language query based on a specific event type.
Your task is to analyze the user's query and select the most appropriate category from the available categories for the specified event type.
You MUST ALWAYS return a category, even if the query is ambiguous or unclear.

## Response Format
Return ONLY the category name as a single word, nothing else:
selected_category

## Event Type
{event_type}

## Available Categories
{categories}

## Guidelines
1. Select the most specific category that matches the query for the given event type
2. If multiple categories could fit, choose the most specific one
3. If the query is ambiguous or unclear, select the most general applicable category
4. You MUST return a category - never return null or empty
5. The category must exactly match one of the available category names
6. Consider the context and nature of the query to determine the most appropriate category
7. If you cannot determine the category with confidence, use the most general category available

## Examples
For event type "symptom":
- "I have a headache" -> pain_and_sensations_pain
- "My stomach hurts" -> digestive_stomach_pain
- "I feel nauseous" -> digestive_nausea
- "I'm coughing" -> respiratory_cough
- "I have a runny nose" -> respiratory_runny_nose

For event type "food":
- "I had chicken for dinner" -> poultry
- "I ate an apple" -> fruit
- "I had some pasta" -> refined_grains
- "I drank some water" -> water

For event type "exercise":
- "I went for a run" -> cardio
- "I lifted weights" -> strength
- "I did yoga" -> static_stretching
- "I played basketball" -> basketball

For ambiguous cases:
- "I did something" -> other
- "Something happened" -> other
- "Not sure what happened" -> other
- "Did a bunch of stuff" -> other
"""


def generate_pick_category_prompt(event_type: str, categories: list[str]) -> str:
    return pick_category_prompt.format(event_type=event_type, categories=categories)
