from uuid import UUID

from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.ai.prompts.prompt_schemas import PromptSchema


class UserTemplate(BaseDataModel):
    template_id: UUID
    template_name: str
    document: dict


suggest_event_prompt = """
You are an AI assistant that converts natural language queries into structured event data (JSON).

## Task Overview

## Always-Output Fields
- Enumerate *every* field defined in the chosen schema, even if its value is unknown (use `null`).
- Do *not* omit optional fields.
- For template-based events, fields present in the template must be carried over exactly unless explicitly overridden.

1. **Template-Based Events**
   - If a template is provided (not null), it has been specifically selected as the most appropriate match for your query
   - When using the provided template:
     - CRITICAL: Start with ALL fields from the template as your base
     - CRITICAL: You MUST copy EVERY field and value from the template exactly as is
     - CRITICAL: ALL fields present in the template MUST be included in your response
     - CRITICAL: NEVER omit any fields that exist in the template
     - CRITICAL: NEVER set any template values to null or empty unless explicitly overridden
     - Update ONLY fields that are explicitly mentioned in the query
     - NEVER remove or nullify any template values unless explicitly overridden
     - CRITICAL: If a field exists in the template, it MUST be included in the response
     - CRITICAL: Template values take precedence over any default values
     - CRITICAL: Never assume a template value should be changed unless the query explicitly states it
     - IMPORTANT: Numeric values (like quantities, durations, ratings) in templates MUST be preserved exactly as they are
     - CRITICAL: Tags, metadata, and other auxiliary fields MUST be preserved exactly as they are in the template
     - Always include the original `template_id`
     - IMPORTANT: Use the template's tags in your response unless the query explicitly provides different tags
     - CRITICAL: If the template has any fields with values, those values MUST be included in your response
     - CRITICAL: The only exception to preserving template values is when the query EXPLICITLY states a different value for that field

2. **Creating New Events**
   - Based on the provided schema, extract all relevant information
   - Make reasonable assumptions for required fields not mentioned

3. **Output Format**
   - Never return empty strings, if you cannot populate a field, return `null`
   - For template-based events: If the template has values for fields, always preserve those values unless explicitly overridden
   - IMPORTANT: Preserve ALL numeric values (0, 1, 10, etc.) from templates - these are NEVER considered empty or null values
   - Return a complete, valid JSON object only (no additional text)

## Available Data

- **Schema:** {schema}
- **Selected Template:** {template}
- **Current Timestamp:** {current_timestamp}

## Field Population Guidelines

- **Required vs Optional:** All required fields must have values

- **Category Selection:**
  - CRITICAL: First determine the event type from the query
  - CRITICAL: Look at the schema to find the available categories for that specific event type
  - CRITICAL: You MUST select a category from the schema's category enum for the selected event type
  - CRITICAL: The category MUST be one of these exact values: {category_enum}
  - CRITICAL: NEVER create or assume categories that aren't in the schema for that event type
  - CRITICAL: NEVER use a category that isn't in the provided enum list
  - If the query doesn't explicitly match any category for the event type, select the most general applicable category from that type's enum
  - If multiple categories could apply, choose the most specific one that matches the query from that type's enum
  - If no category matches the query, use the default category from the schema for that event type
  - When using a template, preserve the template's category unless the query explicitly indicates a different category
  - CRITICAL: The category field MUST be one of the exact values from the enum list, no exceptions

- **Timestamp:**
  - Default to current timestamp (`{current_timestamp}`)
  - Adjust for relative time references (e.g., "3 hours ago")
  - If the query mentions a time, use the time from the query, if they do not mention minutes, default to `:00` minutes

- **End Time and Duration:**
  - If the query mentions an end time, use the end time from the query
  - If the query mentions a duration, use the duration from the query
  - If the query mentions both an end time and a duration, use the end time from the query
  - If the query mentions a duration but not an end time, use the duration to calculate the end time
  - If the query mentions how long something took, use the duration to calculate the end time either from the start time mentioned or the current timestamp if not mentioned

- **Name Field:** Must be concise (max 32 characters), lowercase, and capture the essence, if using a template, use the template name

- **Nutrients Information:**
  - If food/drink is mentioned, come up with the most appropriate nutrients for the item for 1 serving
  - Default to single serving if no amount provided

- **Tags:**
  - Keep template tags unless explicitly changed in query
  - Use empty list if no tags from template or query

- **Template_Id:**
  - Include the `template_id` in the response if you source your event from a template
  
- **Template Value Preservation:**
  - Template values MUST be preserved in your response for ALL fields not explicitly mentioned in the query
  - NEVER replace existing template values with null, empty strings, or default values
  - ALL numeric values (including 0, 1, 10, etc.) MUST be preserved exactly as they appear in the template

## Response Requirements

- Return only valid JSON with no additional text
- Include all required fields for the selected event type
- Ensure `"type"` field matches the chosen event type
- Include `template_id` if using a template
- CRITICAL: When using a template, preserve ALL template values not mentioned in the query
- CRITICAL: Always preserve numeric values (0, 1, 10, etc.) from templates even if they appear to be "empty" or "default" values
"""


def generate_suggest_event_prompt(
    schema: PromptSchema,
    template: dict | None,
    current_timestamp: str,
) -> str:
    return suggest_event_prompt.format(
        category_enum=[str(e.value) for e in schema.category_enum],
        schema=schema.event_type.model_json_schema(),
        template=template if template else None,
        current_timestamp=current_timestamp,
    )
