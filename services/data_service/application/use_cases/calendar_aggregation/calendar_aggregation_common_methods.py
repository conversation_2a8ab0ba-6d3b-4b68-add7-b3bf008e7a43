from typing import Sequence

from services.base.domain.schemas.query.aggregations import CalendarAggregationType


class CommonCalendarAggregationMethods:
    @staticmethod
    def get_month_part(day: int, total_days: int):
        part_size = total_days / 5
        part = int((day - 1) // part_size) + 1
        return part

    @staticmethod
    def get_possible_keys(aggregation_type: CalendarAggregationType) -> Sequence[str]:
        keys = {
            CalendarAggregationType.MONTH_NAMES: [
                "January",
                "February",
                "March",
                "April",
                "May",
                "June",
                "July",
                "August",
                "September",
                "October",
                "November",
                "December",
            ],
            CalendarAggregationType.HOURS_IN_DAY: [str(hour) for hour in range(24)],
            # PARTS_OF_MONTH are meant to divide calendar months into 5 sectors and tries to determine if certain
            # events happen at the start of the month, end or any other part.
            CalendarAggregationType.PARTS_OF_MONTH: [str(part) for part in range(1, 6)],
            CalendarAggregationType.DAYS_OF_MONTH: [str(day) for day in range(1, 32)],
            CalendarAggregationType.WEEKDAYS: [
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday",
                "Sunday",
            ],
            CalendarAggregationType.LUNAR_PHASES: ["New Moon", "First Quarter", "Full Moon", "Last Quarter"],
        }
        return keys.get(aggregation_type, [])
