from uuid import UUID

from services.base.domain.schemas.query.aggregations import CalendarAggregationType, SimpleAggregationMethod
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.shared import BaseDataModel


class CalendarHistogramAggregationInputBoundary(BaseDataModel):
    query: Query
    calendar_aggregation_type: CalendarAggregationType
    field_name: str | None
    aggregation_method: SimpleAggregationMethod
    fill_null_values: bool
    owner_id: UUID
