import calendar
from collections import OrderedDict
from typing import Optional, Sequence
from uuid import UUID

import numpy as np
from numpy import std

from services.base.application.boundaries.aggregates import CalendarHistogramAggregate
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts, SortOrder
from services.base.application.utils.hashmap import HashMapUtils
from services.base.domain.schemas.query.aggregations import (
    CalendarAggregationType,
    SimpleAggregationMethod,
)
from services.base.domain.schemas.query.builders.common_query_adjustments import CommonQueryAdjustments
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.calendar_aggregation.calendar_aggregation_common_methods import (
    CommonCalendarAggregationMethods,
)
from services.data_service.application.use_cases.calendar_aggregation.calendar_aggregation_lunar_phase_determinator import (
    LunarPhaseDeterminator,
)


class CalendarHistogramAggregationUseCaseOutputBoundary(BaseDataModel):
    results: Sequence[CalendarHistogramAggregate]
    standard_deviation: Optional[float]
    coefficient_of_variation: Optional[float]


class CalendarHistogramAggregationUseCase:
    def __init__(self, lunar_determinator: LunarPhaseDeterminator, search_service: DocumentSearchService):
        self._search_service = search_service
        self._lunar_determinator = lunar_determinator

    async def execute_async(
        self,
        user_uuid: UUID,
        query: Query,
        field_name: str | None,
        aggregation_method: SimpleAggregationMethod,
        calendar_aggregation_type: CalendarAggregationType,
        fill_null_values: bool = True,
    ) -> CalendarHistogramAggregationUseCaseOutputBoundary:
        query = CommonQueryAdjustments.add_user_uuid_to_query(query=query, user_uuid=user_uuid)

        response = await self._search_service.search_documents_by_query(
            query=query, size=10000, sorts=[CommonSorts.timestamp(order=SortOrder.ASCENDING)]
        )
        documents = response.documents
        if not documents:
            return CalendarHistogramAggregationUseCaseOutputBoundary(
                results=[], standard_deviation=None, coefficient_of_variation=None
            )

        agg_result = self.calendar_aggregate(
            documents=documents,
            field_name=field_name,
            aggregation_method=aggregation_method,
            aggregation_type=calendar_aggregation_type,
            fill_null_values=fill_null_values,
        )
        values = [res.value for res in agg_result if res.value]
        std_value = float(std([values]))
        mean_value = np.mean(values)
        cv_value = float(std_value / mean_value) if mean_value != 0 else 0.0
        return CalendarHistogramAggregationUseCaseOutputBoundary(
            results=agg_result,
            standard_deviation=std_value,
            coefficient_of_variation=cv_value,
        )

    def calendar_aggregate(
        self,
        documents: Sequence,
        field_name: str | None,
        aggregation_method: SimpleAggregationMethod,
        aggregation_type: CalendarAggregationType,
        fill_null_values: bool,
    ) -> list[CalendarHistogramAggregate]:
        aggregation = {}

        if aggregation_type == CalendarAggregationType.WEEKDAYS:
            for doc in documents:
                dt = doc.timestamp
                weekday_index = dt.weekday()
                weekday_names = CommonCalendarAggregationMethods.get_possible_keys(CalendarAggregationType.WEEKDAYS)
                bucket_key = weekday_names[weekday_index]
                if bucket_key not in aggregation:
                    aggregation[bucket_key] = []
                value = HashMapUtils.get_nested_field_value(doc, field_name) if field_name else 1
                if value is not None:
                    aggregation[bucket_key].append(value)

        elif aggregation_type == CalendarAggregationType.MONTH_NAMES:
            for doc in documents:
                dt = doc.timestamp
                month_index = dt.month - 1
                month_names = CommonCalendarAggregationMethods.get_possible_keys(CalendarAggregationType.MONTH_NAMES)
                bucket_key = month_names[month_index]
                if bucket_key not in aggregation:
                    aggregation[bucket_key] = []
                value = HashMapUtils.get_nested_field_value(doc, field_name) if field_name else 1
                if value is not None:
                    aggregation[bucket_key].append(value)

        elif aggregation_type == CalendarAggregationType.HOURS_IN_DAY:
            for doc in documents:
                dt = doc.timestamp
                hour = dt.hour
                bucket_key = str(hour)
                if bucket_key not in aggregation:
                    aggregation[bucket_key] = []
                value = HashMapUtils.get_nested_field_value(doc, field_name) if field_name else 1
                if value is not None:
                    aggregation[bucket_key].append(value)

        elif aggregation_type == CalendarAggregationType.DAYS_OF_MONTH:
            for doc in documents:
                dt = doc.timestamp
                day = dt.day
                bucket_key = str(day)
                if bucket_key not in aggregation:
                    aggregation[bucket_key] = []
                value = HashMapUtils.get_nested_field_value(doc, field_name) if field_name else 1
                if value is not None:
                    aggregation[bucket_key].append(value)

        elif aggregation_type == CalendarAggregationType.PARTS_OF_MONTH:
            for doc in documents:
                dt = doc.timestamp
                total_days = calendar.monthrange(dt.year, dt.month)[1]
                month_part = CommonCalendarAggregationMethods.get_month_part(dt.day, total_days)
                bucket_key = str(month_part)
                if bucket_key not in aggregation:
                    aggregation[bucket_key] = []
                value = HashMapUtils.get_nested_field_value(doc, field_name) if field_name else 1
                if value is not None:
                    aggregation[bucket_key].append(value)

        elif aggregation_type == CalendarAggregationType.LUNAR_PHASES:
            phase_names = CommonCalendarAggregationMethods.get_possible_keys(aggregation_type=aggregation_type)
            for doc in documents:
                dt = doc.timestamp.date()
                phase_index = self._lunar_determinator.get_lunar_phase(doc_date=dt)
                phase_name = phase_names[phase_index]
                if phase_name not in aggregation:
                    aggregation[phase_name] = []
                value = HashMapUtils.get_nested_field_value(doc, field_name) if field_name else 1
                if value is not None:
                    aggregation[phase_name].append(value)

        if fill_null_values:
            all_possible_keys = CommonCalendarAggregationMethods.get_possible_keys(aggregation_type=aggregation_type)
            aggregation = OrderedDict(
                (key, aggregation.get(key, [])) for key in all_possible_keys
            )  # Initialize missing buckets with empty lists

        # Calculate aggregation values
        aggregation_result = self._calculate_aggregation_values(aggregation, aggregation_method)
        return aggregation_result

    @staticmethod
    def _calculate_aggregation_values(aggregation, aggregation_method):
        aggregation_result = []
        for key, values in aggregation.items():
            # Filter out None values for calculations
            filtered_values = [v for v in values if v is not None]
            doc_count = len(values)
            if aggregation_method == SimpleAggregationMethod.AVG:
                value = round(np.mean(filtered_values), 6) if filtered_values else None
            elif aggregation_method == SimpleAggregationMethod.SUM:
                value = sum(filtered_values) if filtered_values else 0
            elif aggregation_method == SimpleAggregationMethod.MAX:
                value = max(filtered_values) if filtered_values else None
            elif aggregation_method == SimpleAggregationMethod.MIN:
                value = min(filtered_values) if filtered_values else None
            elif aggregation_method == SimpleAggregationMethod.COUNT:
                value = len(filtered_values) if filtered_values else 0
            else:
                raise ValueError(f"Unsupported aggregation method {aggregation_method}")

            aggregation_result.append(
                CalendarHistogramAggregate(
                    aggregation_key=key,
                    agg_method=aggregation_method.value,
                    doc_count=doc_count,
                    value=float(value) if value is not None else None,
                )
            )
        return aggregation_result
