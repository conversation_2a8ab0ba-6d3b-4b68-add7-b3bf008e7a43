from abc import ABC, abstractmethod

from httpx import AsyncClient
from scrapy import Selector

from services.base.application.exceptions import NoContentException
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.activity import ActivityCategory
from services.base.domain.schemas.events.content.content import ContentCategory
from services.data_service.application.use_cases.content.content_lookup_boundaries import ContentLookupOutputBoundary
from services.data_service.application.use_cases.content.meta_extractor import MetaExtractor, MetaProperty


class ContentHandlerBase(ABC):
    """Base class for content handlers using strategy pattern."""

    _TIMEOUT = 5
    _BROWSER_HEADERS = {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "identity",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
    }

    @abstractmethod
    async def can_handle(self, url: str) -> bool:
        """Determine if this handler can process the given URL."""
        pass

    @abstractmethod
    async def handle(self, url: str) -> ContentLookupOutputBoundary:
        """Process the URL and return content details."""
        pass

    @staticmethod
    def _categorize(
        normalized_type: str | None,
    ) -> ContentCategory:
        """Maps a normalized type string to appropriate category enum."""
        if not normalized_type:
            return ContentCategory.CONTENT

        normalized_type = normalized_type.lower().strip()
        return ContentCategory(normalized_type)

    def _normalize_output(
        self,
        title: str,
        category: ActivityCategory | ContentCategory | str | None,
        description: str | None = None,
    ) -> ContentLookupOutputBoundary:
        """Normalize text fields and create output boundary."""
        normalized_title = MetaExtractor.normalize_text(title)

        if not normalized_title:
            raise NoContentException("Unable to extract title from content.")

        normalized_description = MetaExtractor.normalize_text(description) if description else None
        if isinstance(category, str) or category is None:
            normalized_type = MetaExtractor.normalize_text(category)
            category = self._categorize(normalized_type)

        return ContentLookupOutputBoundary(
            title=normalized_title.strip(),
            category=category,
            description=normalized_description.strip() if normalized_description else None,
            type=DataType.Activity if category in ActivityCategory else DataType.Content,
        )


class DefaultHandler(ContentHandlerBase):
    """Fallback handler for any URL not handled by specific handlers."""

    def __init__(self, client: AsyncClient):
        self._client = client

    async def can_handle(self, url: str) -> bool:
        return True  # This is our fallback handler

    async def handle(self, url: str) -> ContentLookupOutputBoundary:
        try:
            response = await self._client.get(
                url=url,
                timeout=ContentHandlerBase._TIMEOUT,
                follow_redirects=True,
                headers=ContentHandlerBase._BROWSER_HEADERS,
            )
            response.raise_for_status()
            html = response.text

        except Exception as e:
            raise NoContentException(f"Unable to retrieve content from URL: {str(e)}")

        selector = Selector(text=html)

        title = MetaExtractor.get_meta_content(selector, MetaProperty.TITLE)
        if not title:
            raise NoContentException(f"Could not parse page title from {url}")

        # Clean up title by taking only the first line
        if "\n" in title:
            title = title.split("\n")[0].strip()

        meta_type = MetaExtractor.get_meta_content(selector, MetaProperty.TYPE)
        description = MetaExtractor.get_meta_content(selector, MetaProperty.DESCRIPTION)

        category = ContentCategory.CONTENT
        if meta_type:
            try:
                category = ContentCategory(meta_type.strip())
            except ValueError:
                pass

        return self._normalize_output(
            title=title.strip(),
            category=category,
            description=description.strip() if description else None,
        )
