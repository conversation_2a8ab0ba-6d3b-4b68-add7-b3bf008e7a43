.DEFAULT_GOAL := help
.EXPORT_ALL_VARIABLES:
DOCKER_NETWORK := stack
COVERAGE := 80
DOCKER_BUILDKIT := 1
COMPOSE_DOCKER_CLI_BUILD := 1


help: ## Show this help (runs only in bash)
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

test_service_base: ## run base tests for all services
	docker run --rm --network $(DOCKER_NETWORK) data_service bash -lce "pytest ./services/base/tests/ --verbose \
		--cov=services/base/ "

test_api_layer: ## run data_service integration tests
	docker run --rm --network $(DOCKER_NETWORK) data_service bash -lce "pytest ./services/data_service/api --verbose -vv "
	docker run --rm --network $(DOCKER_NETWORK) data_service bash -lce "pytest ./services/data_service/v1/api --verbose -vv "
	docker run --rm --network $(DOCKER_NETWORK) data_service bash -lce "pytest ./services/data_service/v02/api --verbose -vv "

test_application_layer:
	docker run --rm --network $(DOCKER_NETWORK) data_service bash -lce "pytest ./services/data_service/application/ --verbose -vv "

test_data_service: test_service_base test_api_layer test_application_layer ## run tests specific to data service
