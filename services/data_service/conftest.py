import asyncio
import os
from datetime import <PERSON><PERSON><PERSON>
from typing import As<PERSON><PERSON>enerator, <PERSON>wai<PERSON>, Callable, Protocol

import pytest
from starlette.testclient import TestClient

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.object_storage_service import ObjectStorageService
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.extension_provider_repository import ExtensionProviderRepository
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.repository.extension_subscriptions_repository import ExtensionSubscriptionsRepository
from services.base.domain.repository.inbox_message_repository import InboxMessageRepository
from services.base.domain.repository.member_user_os_tasks_repository import MemberUserOSTasksRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.place_repository import PlaceRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.record_repository import RecordRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.repository.use_case_repository import UseCaseRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.member_user_builder import MemberUserBuilder
from services.data_service.application.services.asset_service import AssetService
from services.data_service.application.use_cases.ai.classify_event_category_use_case import ClassifyEventCategoryUseCase
from services.data_service.application.use_cases.ai.classify_event_type_use_case import ClassifyEventTypeUseCase
from services.data_service.application.use_cases.ai.classify_multi_event_type_use_case import (
    ClassifyMultiEventTypeUseCase,
)
from services.data_service.application.use_cases.ai.classify_nutrition_type_use_case import ClassifyNutritionTypeUseCase
from services.data_service.application.use_cases.ai.classify_user_action_use_case import ClassifyUserActionUseCase
from services.data_service.application.use_cases.ai.suggest_template_use_case import SuggestTemplateUseCase
from services.data_service.application.use_cases.by_id.delete_by_id_use_case import DeleteByIdUseCase
from services.data_service.application.use_cases.by_id.fetch_asset_by_id_use_case import FetchAssetByIdUseCase
from services.data_service.application.use_cases.content.content_lookup_use_case import ContentLookupUseCase
from services.data_service.application.use_cases.environment.forecast_summary_use_case import ForecastSummaryUseCase
from services.data_service.application.use_cases.event_correlation.event_correlation_use_case import (
    EventCorrelationUseCase,
)
from services.data_service.application.use_cases.feed.document_feed_use_case import DocumentFeedUseCase
from services.data_service.application.use_cases.loading.location.load_location_use_case import LoadLocationUseCase
from services.data_service.application.use_cases.lookup.nutrition_provider.nutrition_ai_provider import (
    NutritionAIProvider,
)
from services.data_service.application.use_cases.lookup.nutrition_provider.open_food_facts_provider import (
    OpenFoodFactsProvider,
)
from services.data_service.application.use_cases.lookup.nutrition_provider.usda_provider import UsdaProvider
from services.data_service.application.use_cases.plans.calculate_goal_progress_use_case import (
    CalculateGoalProgressUseCase,
)
from services.data_service.application.use_cases.plans.check_goal_to_recalculate_use_case import (
    CheckGoalsToRecalculateUseCase,
)
from services.data_service.application.use_cases.templates.template_validator import TemplateValidator
from services.data_service.application.use_cases.user_data.delete_user_data_use_case import DeleteUserDataUseCase
from services.data_service.application.workflows.forecast_summary_workflow.forecast_summary_workflow import (
    ForecastSummaryWorkflow,
)
from services.data_service.dependency_bootstrapper import DependencyBootstrapper
from services.data_service.main import app
from settings.app_constants import DEMO1_UUID


class UserFactoryCallable(Protocol):
    def __call__(self) -> Awaitable[MemberUser]: ...


@pytest.fixture(scope="function")
def seed_user_header() -> dict:
    user: MemberUser = MemberUserBuilder().with_uuid(DEMO1_UUID).build()
    return {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}


@pytest.fixture(scope="session")
async def dependency_bootstrapper() -> AsyncGenerator[DependencyBootstrapper, None]:
    bootstrapper = DependencyBootstrapper().build()
    yield bootstrapper
    await bootstrapper.cleanup()


@pytest.fixture(scope="session")
def object_storage_service(dependency_bootstrapper: DependencyBootstrapper) -> ObjectStorageService:
    return dependency_bootstrapper.get(interface=ObjectStorageService)


@pytest.fixture(scope="session")
def depr_event_repository(dependency_bootstrapper: DependencyBootstrapper) -> DeprEventRepository:
    return dependency_bootstrapper.get(interface=DeprEventRepository)


@pytest.fixture(scope="session")
def search_service(dependency_bootstrapper: DependencyBootstrapper) -> DocumentSearchService:
    return dependency_bootstrapper.get(interface=DocumentSearchService)


@pytest.fixture(scope="session")
def asset_service(dependency_bootstrapper: DependencyBootstrapper) -> AssetService:
    return dependency_bootstrapper.get(interface=AssetService)


@pytest.fixture(scope="session")
def use_case_repo(dependency_bootstrapper: DependencyBootstrapper) -> UseCaseRepository:
    return dependency_bootstrapper.get(interface=UseCaseRepository)


@pytest.fixture(scope="session")
def contact_repo(dependency_bootstrapper: DependencyBootstrapper) -> ContactRepository:
    return dependency_bootstrapper.get(interface=ContactRepository)


@pytest.fixture(scope="session")
def place_repo(dependency_bootstrapper: DependencyBootstrapper) -> PlaceRepository:
    return dependency_bootstrapper.get(interface=PlaceRepository)


@pytest.fixture(scope="session")
def event_repo(dependency_bootstrapper: DependencyBootstrapper) -> EventRepository:
    return dependency_bootstrapper.get(interface=EventRepository)


@pytest.fixture(scope="session")
def record_repo(dependency_bootstrapper: DependencyBootstrapper) -> RecordRepository:
    return dependency_bootstrapper.get(interface=RecordRepository)


@pytest.fixture(scope="session")
def inbox_message_repo(dependency_bootstrapper: DependencyBootstrapper) -> InboxMessageRepository:
    return dependency_bootstrapper.get(interface=InboxMessageRepository)


@pytest.fixture(scope="session")
def template_repo(dependency_bootstrapper: DependencyBootstrapper) -> TemplateRepository:
    return dependency_bootstrapper.get(interface=TemplateRepository)


@pytest.fixture(scope="session")
def template_validator(dependency_bootstrapper: DependencyBootstrapper) -> TemplateValidator:
    return dependency_bootstrapper.get(interface=TemplateValidator)


@pytest.fixture(scope="session")
def plan_repo(dependency_bootstrapper: DependencyBootstrapper) -> PlanRepository:
    return dependency_bootstrapper.get(interface=PlanRepository)


@pytest.fixture(scope="session")
def fetch_use_case(dependency_bootstrapper: DependencyBootstrapper) -> FetchAssetByIdUseCase:
    return dependency_bootstrapper.get(interface=FetchAssetByIdUseCase)


@pytest.fixture(scope="session")
def os_tasks_repository(dependency_bootstrapper: DependencyBootstrapper) -> MemberUserOSTasksRepository:
    return dependency_bootstrapper.get(interface=MemberUserOSTasksRepository)


@pytest.fixture(scope="session")
def load_location_use_case(dependency_bootstrapper: DependencyBootstrapper) -> LoadLocationUseCase:
    return dependency_bootstrapper.get(interface=LoadLocationUseCase)


@pytest.fixture(scope="session")
def calculate_goal_progress_and_completion_uc(
    dependency_bootstrapper: DependencyBootstrapper,
) -> CalculateGoalProgressUseCase:
    return dependency_bootstrapper.get(interface=CalculateGoalProgressUseCase)


@pytest.fixture(scope="session")
def check_goals_to_recalculate_uc(dependency_bootstrapper: DependencyBootstrapper) -> CheckGoalsToRecalculateUseCase:
    return dependency_bootstrapper.get(interface=CheckGoalsToRecalculateUseCase)


@pytest.fixture(scope="session")
def classify_event_type_use_case(dependency_bootstrapper: DependencyBootstrapper) -> ClassifyEventTypeUseCase:
    return dependency_bootstrapper.get(interface=ClassifyEventTypeUseCase)


@pytest.fixture(scope="session")
def classify_event_category_use_case(dependency_bootstrapper: DependencyBootstrapper) -> ClassifyEventCategoryUseCase:
    return dependency_bootstrapper.get(interface=ClassifyEventCategoryUseCase)


@pytest.fixture(scope="session")
def classify_user_action_use_case(dependency_bootstrapper: DependencyBootstrapper) -> ClassifyUserActionUseCase:
    return dependency_bootstrapper.get(interface=ClassifyUserActionUseCase)


@pytest.fixture(scope="session")
def suggest_template_use_case(dependency_bootstrapper: DependencyBootstrapper) -> SuggestTemplateUseCase:
    return dependency_bootstrapper.get(interface=SuggestTemplateUseCase)


@pytest.fixture(scope="session")
def classify_multi_event_type_use_case(
    dependency_bootstrapper: DependencyBootstrapper,
) -> ClassifyMultiEventTypeUseCase:
    return dependency_bootstrapper.get(interface=ClassifyMultiEventTypeUseCase)


@pytest.fixture(scope="session")
def classify_nutrition_type_use_case(dependency_bootstrapper: DependencyBootstrapper) -> ClassifyNutritionTypeUseCase:
    return dependency_bootstrapper.get(interface=ClassifyNutritionTypeUseCase)


@pytest.fixture(scope="session")
def delete_by_id_use_case(dependency_bootstrapper: DependencyBootstrapper) -> DeleteByIdUseCase:
    return dependency_bootstrapper.get(interface=DeleteByIdUseCase)


@pytest.fixture(scope="session")
def feed_use_case(dependency_bootstrapper: DependencyBootstrapper) -> DocumentFeedUseCase:
    return dependency_bootstrapper.get(interface=DocumentFeedUseCase)


@pytest.fixture(scope="session")
def event_correlation_use_case(dependency_bootstrapper: DependencyBootstrapper) -> EventCorrelationUseCase:
    return dependency_bootstrapper.get(interface=EventCorrelationUseCase)


@pytest.fixture(scope="session")
def content_lookup_use_case(dependency_bootstrapper: DependencyBootstrapper) -> ContentLookupUseCase:
    return dependency_bootstrapper.get(interface=ContentLookupUseCase)


@pytest.fixture(scope="session")
def delete_user_data_use_case(dependency_bootstrapper: DependencyBootstrapper) -> DeleteUserDataUseCase:
    return dependency_bootstrapper.get(interface=DeleteUserDataUseCase)


@pytest.fixture(scope="session")
def forecast_summary_workflow(dependency_bootstrapper: DependencyBootstrapper):
    return dependency_bootstrapper.get(interface=ForecastSummaryWorkflow)


@pytest.fixture(scope="session")
def forecast_summary_uc(dependency_bootstrapper: DependencyBootstrapper):
    return dependency_bootstrapper.get(interface=ForecastSummaryUseCase)


@pytest.fixture(scope="session")
def insert_event_use_case(dependency_bootstrapper: DependencyBootstrapper):
    from services.data_service.application.use_cases.events.insert_event_use_case import InsertEventUseCase

    return dependency_bootstrapper.get(interface=InsertEventUseCase)


@pytest.fixture(scope="session")
def test_client():
    return TestClient(app)


@pytest.fixture(scope="session")
async def member_user_repository(dependency_bootstrapper: DependencyBootstrapper) -> MemberUserRepository:
    return dependency_bootstrapper.get(interface=MemberUserRepository)


@pytest.fixture(scope="session")
async def extension_detail_repository(dependency_bootstrapper: DependencyBootstrapper) -> ExtensionDetailRepository:
    return dependency_bootstrapper.get(interface=ExtensionDetailRepository)


@pytest.fixture(scope="session")
async def extension_subscriptions_repository(
    dependency_bootstrapper: DependencyBootstrapper,
) -> ExtensionSubscriptionsRepository:
    return dependency_bootstrapper.get(interface=ExtensionSubscriptionsRepository)


@pytest.fixture(scope="session")
async def extension_provider_repository(dependency_bootstrapper: DependencyBootstrapper) -> ExtensionProviderRepository:
    return dependency_bootstrapper.get(interface=ExtensionProviderRepository)


@pytest.fixture(scope="session")
async def extension_run_repo(dependency_bootstrapper: DependencyBootstrapper) -> ExtensionRunRepository:
    return dependency_bootstrapper.get(interface=ExtensionRunRepository)


@pytest.fixture(scope="session")
async def extension_result_repo(dependency_bootstrapper: DependencyBootstrapper) -> ExtensionResultRepository:
    return dependency_bootstrapper.get(interface=ExtensionResultRepository)


@pytest.fixture(scope="session")
async def member_user_settings_repo(dependency_bootstrapper: DependencyBootstrapper) -> MemberUserSettingsRepository:
    return dependency_bootstrapper.get(interface=MemberUserSettingsRepository)


@pytest.fixture(scope="function")
async def user_factory(
    member_user_repository: MemberUserRepository,
) -> AsyncGenerator[Callable[[], Awaitable[MemberUser]], None]:
    created_users: list[MemberUser] = []

    async def create_user() -> MemberUser:
        user = MemberUserBuilder().build()
        user = await member_user_repository.insert_or_update(user)
        assert user is not None
        created_users.append(user)
        return user

    yield create_user

    # Cleanup code: delete all created users
    for user_to_delete in created_users:
        await member_user_repository.delete(user_to_delete)


@pytest.fixture(scope="function")
async def user_headers_factory(
    user_factory: Callable[[], Awaitable[MemberUser]],
) -> AsyncGenerator[Callable[[], Awaitable[tuple[MemberUser, dict]]], None]:
    async def yield_user_with_headers():
        user = await user_factory()
        assert user is not None
        return user, {
            "Authorization": (
                f"Bearer {generate_access_token(user_uuid=user.user_uuid, time_delta=timedelta(minutes=10))}"
            )
        }

    yield yield_user_with_headers


@pytest.fixture(scope="session")
def usda_provider(dependency_bootstrapper: DependencyBootstrapper) -> UsdaProvider:
    return dependency_bootstrapper.get(interface=UsdaProvider)


@pytest.fixture(scope="session")
def open_food_facts_provider(dependency_bootstrapper: DependencyBootstrapper) -> OpenFoodFactsProvider:
    return dependency_bootstrapper.get(interface=OpenFoodFactsProvider)


@pytest.fixture(scope="session")
def nutrition_ai_provider(dependency_bootstrapper: DependencyBootstrapper) -> NutritionAIProvider:
    return dependency_bootstrapper.get(interface=NutritionAIProvider)


# For some reason the loop is closed
# This overrides the default behaviour of the loop in pytest-asyncio
@pytest.fixture(scope="session")
def event_loop():
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# Does not run integration test in normal pytest invocation
def pytest_collection_modifyitems(config, items):
    # Get the value of the environment variable and command-line option
    pipeline_run = os.getenv("PIPELINE_RUN")
    marks_option = config.getoption("-m", "")
    non_skipped_tests = []

    if pipeline_run:
        if "integration" not in marks_option:
            skip_integration = pytest.mark.skip(reason="skipped because it is an integration test")
            for item in items:
                if any(marker.name == "integration" for marker in item.iter_markers()):
                    item.add_marker(skip_integration)
                else:
                    non_skipped_tests.append(item)
        else:
            skip_non_integration = pytest.mark.skip(reason="skipped because it not an integration test")
            for item in items:
                if not any(marker.name == "integration" for marker in item.iter_markers()):
                    item.add_marker(skip_non_integration)
                else:
                    non_skipped_tests.append(item)
