import csv
import io
import json
import os
import shutil
from pathlib import Path
from typing import AsyncGenerator, List, Sequence
from uuid import UUID

import aiofiles
from aiofiles.threadpool import AsyncFileIO

from services.base.api.output.mappers.document_api_output_mapper import DocumentAPIOutputMapper
from services.base.application.boundaries.documents import ExtensionResultOutputBoundary
from services.base.application.io.zip import Zip
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.sleep import Sleep
from services.file_service.application.mappings.csv_mappings.extension_mappings import (
    CommonExtensionFields,
    ExtensionSpecificCSVMappings,
)
from services.file_service.application.use_cases.export.data_export_unique_csv_conversions import (
    extensions_mapper,
    sleep_mapper,
)


class DataExportFileHandler:
    @staticmethod
    def initialize_directories(
        export_path: Path, documents_folder: str, csv_folder: str, export_csv: bool = True
    ) -> tuple[Path, Path, Path | None]:
        export_path.mkdir(exist_ok=True)
        csv_path = None
        documents_path = Path.joinpath(export_path, "documents")
        documents_path.mkdir(exist_ok=True)
        json_documents_path = Path.joinpath(documents_path, documents_folder)
        json_documents_path.mkdir(exist_ok=True)
        if export_csv:
            csv_path = Path.joinpath(documents_path, csv_folder)
            csv_path.mkdir(exist_ok=True)

        markdown_source_path = Path.joinpath(
            Path(__file__).resolve(strict=True).parent, "LLIF_EXPORT_TAKEOUT_README.md"
        )
        shutil.copyfile(markdown_source_path, Path.joinpath(documents_path, markdown_source_path.name))

        return documents_path, json_documents_path, csv_path

    @staticmethod
    def zip_files(filename: str, export_path: Path, documents_path: Path) -> Path:
        return Zip.zip_folder(src=documents_path, dst=export_path, zip_name=os.path.splitext(filename)[0])

    @staticmethod
    async def process_stream_to_json_csv(
        documents: AsyncGenerator[Sequence[Document], None],
        json_fstream: AsyncFileIO,
        csv_fstream: AsyncFileIO | None,
        csv_fields: List[str] | None,
    ):
        """
        Iterates over a single document stream and writes batches to both JSON and CSV files.
        """
        json_is_first = True
        csv_writer = None
        buffer = None

        if csv_fstream and csv_fields:
            # Use an in-memory buffer for csv.writer compatibility with aiofiles
            buffer = io.StringIO()
            csv_writer = csv.DictWriter(
                buffer,
                fieldnames=[field.split(".")[-1] for field in csv_fields],
                delimiter=",",
                quotechar='"',
                quoting=csv.QUOTE_MINIMAL,
            )
            csv_writer.writeheader()
            await csv_fstream.write(buffer.getvalue())
            buffer.seek(0)
            buffer.truncate(0)

        await json_fstream.write("[\n")

        async for doc_batch in documents:
            if not doc_batch:
                continue

            try:
                mapped_documents = [DocumentAPIOutputMapper.map(document=document) for document in doc_batch]
            except Exception:
                # @REVIEW: Unsure how to handle environment,
                # the takeout export uses aggregated data and fails on type_id check in the API output mapper code above
                mapped_documents = doc_batch

            # Write to JSON
            for doc in mapped_documents:
                if not json_is_first:
                    await json_fstream.write(",\n")

                json_string = json.dumps(
                    doc.model_dump(
                        exclude_none=True,
                        mode="json",
                    ),
                    indent=4,
                )
                await json_fstream.write(json_string)
                json_is_first = False

            # Write to CSV
            if csv_writer and buffer:
                for model in doc_batch:
                    if isinstance(model, Sleep):
                        for sleep_event in model.sleep_events:
                            fields_to_write = await sleep_mapper(sleep_event=sleep_event)
                            if fields_to_write:
                                csv_writer.writerow(fields_to_write)
                    else:
                        selected_fields = {}
                        model_data_dict = json.loads(model.model_dump_json())
                        for field in csv_fields:
                            nested_keys = field.split(".")
                            value = model_data_dict
                            for key in nested_keys:
                                value = value.get(key) if isinstance(value, dict) else None
                                if value is None:
                                    break
                            selected_fields[nested_keys[-1]] = value
                        csv_writer.writerow(selected_fields)

                await csv_fstream.write(buffer.getvalue())
                buffer.seek(0)
                buffer.truncate(0)

        await json_fstream.write("\n]")

    @staticmethod
    async def write_extension_output_to_csv(
        extension_id: UUID,
        extension_results: Sequence[ExtensionResultOutputBoundary],
        file_dir: Path,
        filename: str,
    ):
        """Asynchronously write extension output models to a CSV file."""
        file_dir.mkdir(parents=True, exist_ok=True)
        file_path = file_dir / filename

        if file_path.is_dir():
            raise IsADirectoryError(f"{file_path} is a directory, not a file.")

        file_exists = file_path.exists()

        extension_specific_fields = ExtensionSpecificCSVMappings.get(extension_id)

        try:
            async with aiofiles.open(file_path, "a", newline="", encoding="utf-8") as fstream:
                # Use StringIO as an in-memory buffer for csv.writer compatibility with aiofiles
                buffer = io.StringIO()
                writer = csv.DictWriter(
                    buffer,
                    fieldnames=[field.split(".")[-1] for field in CommonExtensionFields + extension_specific_fields],
                    delimiter=",",
                    quotechar='"',
                    quoting=csv.QUOTE_MINIMAL,
                )

                if not file_exists:
                    writer.writeheader()
                for model in extension_results:
                    await extensions_mapper(writer=writer, extension_id=extension_id, extension_result=model)

                # Write buffered content to the actual file
                await fstream.write(buffer.getvalue())

        except Exception as e:
            raise IOError(f"Failed to write to CSV {file_path}: {str(e)}")

    @staticmethod
    async def write_extension_output_to_json(file_dir: Path, filename: str, models: Sequence[Document]):
        if not models:
            return

        file_path = file_dir / filename
        file_dir.mkdir(parents=True, exist_ok=True)

        try:
            async with aiofiles.open(file_path, "w", encoding="utf-8") as fstream:
                await fstream.write("[\n")

                is_first = True
                for model in models:
                    if not is_first:
                        await fstream.write(",\n")

                    json_string = json.dumps(
                        model.model_dump(
                            exclude={DocumentLabels.METADATA: {DocumentLabels.USER_UUID}},
                            exclude_none=True,
                            mode="json",
                        ),
                        indent=4,
                    )
                    await fstream.write(json_string)
                    is_first = False

                await fstream.write("\n]")

        except Exception as e:
            raise IOError(f"Failed to write to {file_path}: {str(e)}")
