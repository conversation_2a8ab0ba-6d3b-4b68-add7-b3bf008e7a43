import logging
import shutil
from datetime import datetime, timezone
from pathlib import Path
from typing import Optional
from uuid import UUID
from zoneinfo import ZoneInfo

import aiofiles

from services.base.application.assets import Assets
from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.async_use_case_base import AsyncUse<PERSON>aseBase
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.filter_types import (
    TimestampRangeFilter,
)
from services.base.application.event_models.takeout_export_finished_event_model import TakeoutExportFinishedModel
from services.base.application.event_models.takeout_export_scheduled_event_model import TakeoutExportScheduledModel
from services.base.application.exceptions import NoContentException
from services.base.application.markdown.markdown_parser import MarkdownParser
from services.base.application.object_storage_service import ObjectStorageService
from services.base.domain.annotated_types import AssetId
from services.base.domain.constants.messaging import ATT_NAME_TAKEOUT_EXPORT_EVENT, MessageTopics
from services.base.domain.enums.event_type import EventType
from services.base.domain.enums.task_status import TaskStatus
from services.base.domain.repository.export_task_repository import ExportTaskRepository
from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.message_queue.utils import create_string_message_attribute
from services.base.type_resolver import TypeResolver
from services.file_service.application.enums.exportable_data_type import ExportableType
from services.file_service.application.mappings.csv_mappings.extension_mappings import ExtensionSpecificCSVMappings
from services.file_service.application.mappings.data_types import ExportableDataTypeToCsvFieldsMapping
from services.file_service.application.use_cases.export.data_export_documents_handler import DataExportDocumentsHandler
from services.file_service.application.use_cases.export.data_export_file_handler import DataExportFileHandler
from settings.extension_constants import SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID, TREND_INSIGHTS_EXTENSION_ID


class ProcessDataExportUseCase(AsyncUseCaseBase):
    def __init__(
        self,
        search_service: DocumentSearchService,
        object_storage_service: ObjectStorageService,
        message_broker_client: AsyncMessageBrokerClient,
        export_repo: ExportTaskRepository,
        extension_run_repo: ExtensionRunRepository,
        extension_result_repo: ExtensionResultRepository,
        extension_detail_repo: ExtensionDetailRepository,
    ):
        self._search_service = search_service
        self._object_storage_service = object_storage_service
        self._message_broker_client = message_broker_client
        self._export_repo = export_repo
        self._extension_run_repo = extension_run_repo
        self._extension_result_repo = extension_result_repo
        self._extension_detail_repo = extension_detail_repo

    async def execute_async(self, input_object: TakeoutExportScheduledModel) -> str:
        takeout_name = input_object.takeout_name
        user_id = input_object.user_uuid
        data_types = input_object.data_types
        range_filter = input_object.range_filter
        export_csv = input_object.export_csv
        user_timezone = ZoneInfo(input_object.user_timezone)
        task_id = input_object.task_id
        environment_aggregation_interval = input_object.environment_aggregation_interval
        system_run = input_object.system_run
        storage_path = input_object.storage_path
        # The export path is a folder in this file's parent directory named as the user's id
        user_export_path = Path.joinpath(Path(__file__).resolve(strict=True).parent, str(user_id))

        export_task = await self._export_repo.get_by_id(task_id=task_id)
        if not export_task:
            raise ValueError(f"Task not found, task id: {task_id}")

        export_task.status = TaskStatus.IN_PROGRESS
        export_task = await self._export_repo.insert_or_update(task=export_task)
        try:
            documents_path, json_documents_path, csv_path = DataExportFileHandler.initialize_directories(
                export_path=user_export_path,
                documents_folder="json_documents",
                csv_folder="csv_documents",
                export_csv=export_csv,
            )
            for dt in data_types:
                try:
                    if dt.value in [e.value for e in EventType]:
                        await self._store_users_events(
                            dt=dt,
                            user_id=user_id,
                            range_filter=range_filter,
                            store_path=json_documents_path,
                            csv_path=csv_path,
                        )
                    if dt in [
                        ExportableType.Plan,
                        ExportableType.EventTemplate,
                        ExportableType.GroupTemplate,
                        ExportableType.UseCase,
                        ExportableType.Contact,
                    ]:
                        await self._store_non_event_v3_user_data(
                            dt=dt,
                            user_id=user_id,
                            store_path=json_documents_path,
                            csv_path=csv_path,
                        )
                    if dt in [ExportableType.Pollen, ExportableType.Weather, ExportableType.AirQuality]:
                        await self._store_environment_data(
                            dt=dt,
                            user_id=user_id,
                            range_filter=range_filter,
                            user_timezone=user_timezone,
                            store_path=json_documents_path,
                            csv_path=csv_path,
                            aggregation_interval=environment_aggregation_interval,
                        )
                    if dt in [ExportableType.ExtensionRun]:
                        await self._store_extension_output(
                            user_id=user_id,
                            store_path=json_documents_path,
                            range_filter=range_filter,
                            csv_path=csv_path,
                        )
                except NoContentException:
                    continue
                except Exception as error:
                    logging.exception(f"Error storing data for {dt}: {error}")

            zip_name = DataExportFileHandler.zip_files(
                filename=takeout_name,
                export_path=user_export_path,
                documents_path=documents_path,
            )

            await self._save_file_to_object_storage(
                asset_name=takeout_name,
                filepath=zip_name,
                storage_path=storage_path,
            )
            if not system_run:
                await self._publish_export_finished(
                    timestamp=datetime.now(timezone.utc),
                    user_id=user_id,
                    takeout_name=takeout_name,
                )
            export_task.status = TaskStatus.COMPLETED
        except Exception as error:
            logging.exception(error)
            export_task.status = TaskStatus.FAILED

        export_task.completed_at = datetime.now(timezone.utc)
        _ = await self._export_repo.insert_or_update(task=export_task)
        # Cleanup
        shutil.rmtree(user_export_path)
        return takeout_name

    async def _store_users_events(
        self,
        user_id: UUID,
        dt: ExportableType,
        store_path: Path,
        csv_path: Path | None,
        range_filter: TimestampRangeFilter | None = None,
    ):
        """
        Streams batched user data and writes it to a JSON and CSV file.
        """
        documents_stream = DataExportDocumentsHandler.stream_batched_user_data(
            data_type=dt,
            user_id=user_id,
            search_service=self._search_service,
            range_filter=range_filter,
        )

        first_batch, documents_stream = await DataExportDocumentsHandler._peek_async_generator(documents_stream)
        if not first_batch:
            logging.info(f"Did not find any documents for {dt}. No files will be created.")
            return

        markdown_file_path = store_path / f"{dt.value}.md"
        with open(markdown_file_path, "w", encoding="utf-8") as markdown_file:
            markdown_file.write(
                MarkdownParser.model_to_markdown(TypeResolver.get_event_api_output(dt.to_domain_model().type_id()))
            )

        json_filename = f"{dt.value}_V3.json" if dt == ExportableType.SleepV3 else f"{dt.value}.json"
        json_file_path = store_path / json_filename
        csv_filename = f"{dt.value}.csv"
        csv_file_path = csv_path / csv_filename if csv_path else None

        csv_fields = ExportableDataTypeToCsvFieldsMapping.get(dt) if csv_path else None

        try:
            async with aiofiles.open(json_file_path, "w", encoding="utf-8") as json_fstream:
                if csv_file_path and csv_fields:
                    store_path.mkdir(parents=True, exist_ok=True)
                    async with aiofiles.open(csv_file_path, "w", newline="", encoding="utf-8") as csv_fstream:
                        await DataExportFileHandler.process_stream_to_json_csv(
                            documents=DataExportDocumentsHandler.first_batch_stream(first_batch, documents_stream),
                            json_fstream=json_fstream,
                            csv_fstream=csv_fstream,
                            csv_fields=csv_fields,
                        )
                else:
                    await DataExportFileHandler.process_stream_to_json_csv(
                        documents=DataExportDocumentsHandler.first_batch_stream(first_batch, documents_stream),
                        json_fstream=json_fstream,
                        csv_fstream=None,
                        csv_fields=None,
                    )
        except Exception as e:
            logging.error(f"An error occurred during data streaming: {e}")

    async def _store_non_event_v3_user_data(
        self,
        user_id: UUID,
        dt: ExportableType,
        store_path: Path,
        csv_path: Path | None,
    ) -> None:
        """
        Streams batched non-event user data and writes it to a JSON and CSV file.
        """
        documents_stream = DataExportDocumentsHandler.stream_user_data(
            data_type=dt,
            user_id=user_id,
            search_service=self._search_service,
        )

        first_batch, documents_stream = await DataExportDocumentsHandler._peek_async_generator(documents_stream)
        if not first_batch:
            logging.info(f"Did not find any documents for {dt}. No files will be created.")
            return

        json_file_path = store_path / f"{dt.value}.json"
        csv_filename = f"{dt.value}.csv"
        csv_file_path = csv_path / csv_filename if csv_path else None

        csv_fields = ExportableDataTypeToCsvFieldsMapping.get(dt) if csv_path else None

        try:
            async with aiofiles.open(json_file_path, "w", encoding="utf-8") as json_fstream:
                if csv_file_path and csv_fields:
                    store_path.mkdir(parents=True, exist_ok=True)
                    async with aiofiles.open(csv_file_path, "w", newline="", encoding="utf-8") as csv_fstream:
                        await DataExportFileHandler.process_stream_to_json_csv(
                            documents=DataExportDocumentsHandler.first_batch_stream(first_batch, documents_stream),
                            json_fstream=json_fstream,
                            csv_fstream=csv_fstream,
                            csv_fields=csv_fields,
                        )
                else:
                    await DataExportFileHandler.process_stream_to_json_csv(
                        documents=DataExportDocumentsHandler.first_batch_stream(first_batch, documents_stream),
                        json_fstream=json_fstream,
                        csv_fstream=None,
                        csv_fields=None,
                    )
        except Exception as e:
            logging.error(f"An error occurred during data streaming: {e}")

    @staticmethod
    async def _store_environment_data(
        user_id: UUID,
        dt: ExportableType,
        user_timezone: ZoneInfo,
        range_filter: TimestampRangeFilter | None,
        store_path: Path,
        aggregation_interval: str,
        csv_path: Path | None,
    ) -> None:
        """
        Streams batched environment data and writes it to a JSON and CSV file.
        """
        space_time_coordinates = await DataExportDocumentsHandler.get_space_time_input(
            user_id=user_id,
            user_timezone=user_timezone,
            range_filter=range_filter,
            aggregation_interval=aggregation_interval,
        )

        documents_stream = DataExportDocumentsHandler.stream_monthly_environment_data(
            user_id=user_id,
            data_type=dt,
            timezone=user_timezone,
            space_time_input=space_time_coordinates,
            aggregation_interval=aggregation_interval,
        )

        first_batch, documents_stream = await DataExportDocumentsHandler._peek_async_generator(documents_stream)
        if not first_batch:
            logging.info(f"No environment data found for type {dt.value}. No files will be created.")
            return

        json_file_path = store_path / f"{dt.value}.json"
        csv_filename = f"{dt.value}.csv"
        csv_file_path = csv_path / csv_filename if csv_path else None

        csv_fields = ExportableDataTypeToCsvFieldsMapping.get(dt) if csv_path else None

        try:
            async with aiofiles.open(json_file_path, "w", encoding="utf-8") as json_fstream:
                if csv_file_path and csv_fields:
                    store_path.mkdir(parents=True, exist_ok=True)
                    async with aiofiles.open(csv_file_path, "w", newline="", encoding="utf-8") as csv_fstream:
                        await DataExportFileHandler.process_stream_to_json_csv(
                            documents=DataExportDocumentsHandler.first_batch_stream(first_batch, documents_stream),
                            json_fstream=json_fstream,
                            csv_fstream=csv_fstream,
                            csv_fields=csv_fields,
                        )
                else:
                    await DataExportFileHandler.process_stream_to_json_csv(
                        documents=DataExportDocumentsHandler.first_batch_stream(first_batch, documents_stream),
                        json_fstream=json_fstream,
                        csv_fstream=None,
                        csv_fields=None,
                    )
        except Exception as e:
            logging.error(f"An error occurred during environment data streaming: {e}")

    async def _store_extension_output(
        self,
        user_id: UUID,
        store_path: Path,
        csv_path: Path | None,
        range_filter: TimestampRangeFilter | None = None,
    ):
        """
        Streams extension output data and writes it to separate JSON and CSV files.

        A new JSON file is created for each extension run due to the timestamp in the path.
        """
        if not range_filter:
            try:
                range_filter = await DataExportDocumentsHandler.get_extension_output_range(
                    user_uuid=user_id, extension_run_repo=self._extension_run_repo
                )
            except NoContentException:
                return None
        for extension_id in [
            TREND_INSIGHTS_EXTENSION_ID,
            SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID,
        ]:
            extension_detail = await self._extension_detail_repo.get_by_extension_id(extension_id=extension_id)
            extension_name = extension_detail.name
            async for extension_run, extension_results in DataExportDocumentsHandler.get_extension_outputs(
                user_id=user_id,
                range_filter=range_filter,
                extension_id=extension_id,
                extension_run_repo=self._extension_run_repo,
                extension_result_repo=self._extension_result_repo,
            ):
                # The file path is unique for each extension run, so we can write from scratch.
                await DataExportFileHandler.write_extension_output_to_json(
                    file_dir=Path.joinpath(store_path, extension_name, f"{extension_run.timestamp.date()}"),
                    filename="ExtensionRun.json",
                    models=[extension_run],
                )
                await DataExportFileHandler.write_extension_output_to_json(
                    file_dir=Path.joinpath(store_path, extension_name, f"{extension_run.timestamp.date()}"),
                    filename="ExtensionResults.json",
                    models=extension_results,
                )

                if csv_path and extension_id in ExtensionSpecificCSVMappings.keys():
                    await DataExportFileHandler.write_extension_output_to_csv(
                        extension_id=extension_id,
                        extension_results=extension_results,
                        file_dir=Path.joinpath(csv_path, extension_name, f"{extension_run.timestamp.date()}"),
                        filename=f"{extension_name}_{extension_run.timestamp.date()}.csv",
                    )

    async def _save_file_to_object_storage(
        self, asset_name: AssetId, filepath: Path, storage_path: Optional[str]
    ) -> str:
        await self._object_storage_service.create_container(container_name=storage_path)
        asset_name = Assets.generate_export_path(asset_name)

        with open(filepath, mode="rb") as zipfile:
            await self._object_storage_service.create_object(
                container_name=storage_path,
                object_name=asset_name,
                data=zipfile,
                override=True,
            )
        return await self._object_storage_service.get_object_url(container_name=storage_path, object_name=asset_name)

    async def _publish_export_finished(self, timestamp: datetime, user_id: UUID, takeout_name: str):
        await self._message_broker_client.publish_topic(
            topic_name=MessageTopics.TOPIC_TAKEOUT_EXPORT_FINISHED.value,
            message_body=TakeoutExportFinishedModel(
                timestamp=timestamp,
                user_uuid=user_id,
                takeout_name=takeout_name,
            ).model_dump_json(),
            message_attributes=create_string_message_attribute(
                ATT_NAME_TAKEOUT_EXPORT_EVENT, MessageTopics.TOPIC_TAKEOUT_EXPORT_FINISHED.value
            ),
        )
