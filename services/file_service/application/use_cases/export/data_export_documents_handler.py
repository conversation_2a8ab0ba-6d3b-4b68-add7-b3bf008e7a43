from collections import defaultdict
from typing import AsyncGenerator, List, Optional, Sequence
from uuid import UUID
from zoneinfo import ZoneInfo

from services.base.application.boundaries.documents import ExtensionResultOutputBoundary
from services.base.application.boundaries.space_time_coordinates import SpaceTimeCoordinates
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.filter_types import (
    TimestampRangeFilter,
)
from services.base.application.database.models.sorts import CommonSorts, Sort, SortOrder
from services.base.application.exceptions import NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.extension_labels.extension_labels import ExtensionLabels
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.extension_output import ExtensionRun
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_query_adjustments import CommonQueryAdjustments
from services.base.domain.schemas.query.leaf_query import RangeQuery, ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.file_service.application.enums.exportable_data_type import ExportableType
from services.file_service.application.mappings.data_types import EnvironmentDataTypeToUseCaseMapping
from services.file_service.application.use_cases.export.aggregate_location_use_case import AggregateLocationUseCase


class DataExportDocumentsHandler:
    @staticmethod
    async def stream_batched_user_data(
        search_service: DocumentSearchService,
        data_type: ExportableType,
        user_id: UUID,
        range_filter: TimestampRangeFilter | None,
    ) -> AsyncGenerator[Sequence[Document], None]:
        query = Query(
            type_queries=[
                TypeQuery(
                    domain_types=[data_type.to_domain_model()],
                    query=(
                        RangeQuery(
                            field_name=DocumentLabels.TIMESTAMP,
                            lte=range_filter.lte,
                            gte=range_filter.gte,
                        )
                        if range_filter
                        else None
                    ),
                )
            ]
        )
        query = CommonQueryAdjustments.add_user_uuid_to_query(query=query, user_uuid=user_id)

        continuation_token = None
        continue_fetch = True
        while continue_fetch:
            fetched_documents = await search_service.search_documents_by_query(
                continuation_token=continuation_token,
                query=query,
                sorts=[CommonSorts.timestamp(order=SortOrder.DESCENDING)],
                size=1000,
            )
            continuation_token = fetched_documents.continuation_token
            continue_fetch = bool(fetched_documents.documents)
            yield fetched_documents.documents

    @staticmethod
    async def stream_user_data(
        search_service: DocumentSearchService,
        data_type: ExportableType,
        user_id: UUID,
    ) -> AsyncGenerator[Sequence[Document], None]:
        # Construct the main query
        query = Query(type_queries=[TypeQuery(domain_types=[data_type.to_domain_model()], query=None)])
        query = CommonQueryAdjustments.add_user_uuid_to_query(query=query, user_uuid=user_id)

        # Initialize continuation token for streaming
        continuation_token = None
        while True:
            fetched_documents = await search_service.search_documents_by_query(
                continuation_token=continuation_token,
                query=query,
                sorts=CommonSorts.created_at_and_internal_id(),
                size=1000,
            )
            continuation_token = fetched_documents.continuation_token

            if not fetched_documents.documents:
                break
            assert fetched_documents.documents
            yield fetched_documents.documents

    @staticmethod
    async def get_extension_outputs(
        user_id: UUID,
        range_filter: TimestampRangeFilter,
        extension_id: UUID,
        extension_run_repo: ExtensionRunRepository,
        extension_result_repo: ExtensionResultRepository,
    ) -> AsyncGenerator[tuple[ExtensionRun, Sequence[ExtensionResultOutputBoundary]], None]:
        query = (
            BooleanQueryBuilder()
            .add_queries(
                queries=[
                    ValuesQuery(
                        field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}", values=[str(user_id)]
                    ),
                    ValuesQuery(
                        field_name=f"{DocumentLabels.METADATA}.{ExtensionLabels.EXTENSION_ID}",
                        values=[str(extension_id)],
                    ),
                    (
                        RangeQuery(field_name=DocumentLabels.TIMESTAMP, gte=range_filter.gte, lte=range_filter.lte)
                        if range_filter
                        else None
                    ),
                ]
            )
            .build_and_query()
        )
        query = Query(type_queries=[TypeQuery(domain_types=[ExtensionRun], query=query)])
        extension_runs = await extension_run_repo.search_by_query(
            query=query, sorts=[Sort(name=DocumentLabels.TIMESTAMP, order=SortOrder.ASCENDING)]
        )
        for extension_run in extension_runs.results:
            extension_run_id = extension_run.document.id
            extension_results = await extension_result_repo.get_all_children(parent_id=extension_run_id)

            yield extension_run.document, extension_results

    @staticmethod
    async def get_extension_output_range(
        extension_run_repo: ExtensionRunRepository, user_uuid: UUID
    ) -> TimestampRangeFilter:
        query = (
            BooleanQueryBuilder()
            .add_queries(
                queries=[
                    ValuesQuery(
                        field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}", values=[str(user_uuid)]
                    ),
                ]
            )
            .build_and_query()
        )
        query = Query(type_queries=[TypeQuery(domain_types=[ExtensionRun], query=query)])
        latest_run = await extension_run_repo.search_by_query(
            query=query, size=1, sorts=[Sort(name=DocumentLabels.TIMESTAMP, order=SortOrder.DESCENDING)]
        )
        if not latest_run.results:
            raise NoContentException("No Extension Output Found")
        latest_run = latest_run.results[0].document

        earliest_run = await extension_run_repo.search_by_query(
            query=query, size=1, sorts=[Sort(name=DocumentLabels.TIMESTAMP, order=SortOrder.ASCENDING)]
        )
        earliest_run = earliest_run.results[0].document

        return TimestampRangeFilter(gte=earliest_run.timestamp, lte=latest_run.timestamp)

    @staticmethod
    async def stream_monthly_environment_data(
        user_id: UUID,
        timezone: ZoneInfo,
        data_type: ExportableType,
        space_time_input: List[SpaceTimeCoordinates],
        aggregation_interval: str,
    ) -> AsyncGenerator[Sequence[Document]]:
        if not space_time_input:
            return
        use_case = EnvironmentDataTypeToUseCaseMapping.get(data_type)
        if not use_case:
            raise ShouldNotReachHereException(f"Did not find usecase for environmental data type: {data_type}")

        monthly_space_time_inputs: dict[str, list[SpaceTimeCoordinates]] = defaultdict(list)
        for location in space_time_input:
            month_year = location.timestamp.strftime("%Y-%m")
            monthly_space_time_inputs[month_year].append(location)

        step = 100
        for _, location_monthly_collection in monthly_space_time_inputs.items():
            for space_time_chunk in [
                location_monthly_collection[i : i + step] for i in range(0, len(location_monthly_collection), step)
            ]:
                fetched_documents = await use_case.execute_async(
                    aggregation_interval=aggregation_interval,
                    user_id=user_id,
                    timezone=timezone,
                    space_time_input=space_time_chunk,
                )
                yield fetched_documents.results

    @staticmethod
    async def get_space_time_input(
        user_id: UUID,
        user_timezone: ZoneInfo,
        range_filter: Optional[TimestampRangeFilter],
        aggregation_interval: str,
    ) -> List[SpaceTimeCoordinates]:
        locations = await AggregateLocationUseCase().execute_async(
            aggregation_interval=aggregation_interval,
            user_id=user_id,
            timezone=user_timezone,
            time_lte=range_filter.lte if range_filter else None,
            time_gte=range_filter.gte if range_filter else None,
        )
        space_time_coordinates = [
            SpaceTimeCoordinates(
                latitude=location.coordinates.latitude,
                longitude=location.coordinates.longitude,
                timestamp=location.timestamp,
                end_time=location.end_time,
                duration=location.duration,
            )
            for location in locations.results
        ]
        return space_time_coordinates

    @staticmethod
    async def _peek_async_generator(async_gen: AsyncGenerator):
        """Peek at the first item of an async generator without consuming it."""
        try:
            first_item = await async_gen.__anext__()
            return first_item, async_gen
        except StopAsyncIteration:
            return None, async_gen

    @staticmethod
    async def first_batch_stream(first_batch, async_gen):
        """
        An async generator that yields the first batch and then the rest of the stream.
        """
        if first_batch:
            yield first_batch
        async for item in async_gen:
            yield item
