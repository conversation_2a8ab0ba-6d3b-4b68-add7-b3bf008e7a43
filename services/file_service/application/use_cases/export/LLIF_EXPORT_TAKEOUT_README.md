# LLIF Data Takeout Readme

## Purpose

This file is meant to give an overview of the LLIF data takeout. This includes all of your data, submitted through the app, or synced data from external providers like Apple Health, Fitbit, and others. It also includes environmental data, which matches with your submitted location. The data is available either in a JSON or CSV.

### How to read your export

- **Where the data lives**: The `json_documents/` folder contains one JSON file per data type (e.g., activity, diary events, sleep).
- **Schema next to each JSON**: For every `*.json` file there is a sibling `*.md` file with the same name that documents the schema with fields names and descriptions.
- **Example layout**:

```text
json_documents/
  Activity.json
  Activity.md
  DiaryEvents.json
  DiaryEvents.md
  Sleep.json
  Sleep.md
  ...
```

- **How to use it**: Open the matching `.md` to see a concise list of attributes for that type. Use it while browsing the JSON to understand what each field means. These schema files are generated from LLIF’s source models and are kept in sync.
- **CSV vs JSON**: The `csv_documents/` folder provides flattened tabular views. For complete structure and field definitions, refer to the corresponding JSON file and its adjacent `.md` schema.

## Privacy Statement

These files are provided only to you as the account owner, in accordance with our privacy policy and terms of use. Visit our website [llif.org](https://llif.org/) for more information.
