from datetime import datetime
from typing import Op<PERSON>, <PERSON><PERSON>, Union

from services.file_service.application.utils import InvalidE7Geo


def datetime_to_iso_converter(time: str, date: str, time_format: str = "%Y-%m-%d %H:%M:%S.%f%z") -> Optional[str]:
    """Returns parsed full datetime formatted to string using isoformat method"""
    try:
        return datetime.strptime("{0} {1}".format(date, time), time_format).isoformat()
    except ValueError:
        return None


def timestamp_to_iso_converter(timestamp: str) -> str:
    """Convert from timestamp in SECONDS to datetime iso, timezone aware (utc+0)"""
    return datetime.fromtimestamp(int(timestamp)).isoformat()


def timestampMs_to_iso_converter(timestampMs: str) -> str:
    """Convert from timestamp in MILLISECONDS to datetime iso, timezone aware (utc+0)"""
    timestamp = int(timestampMs) / 1000  # get timestamp in seconds
    return datetime.fromtimestamp(timestamp).isoformat()


def timestamp_microseconds_to_iso_converter(timestamp_microseconds: str) -> str:
    """Convert from timestamp in MICROSECONDS to datetime iso, timezone aware (utc+0)"""
    timestamp = int(timestamp_microseconds) / 1000000  # get timestamp in seconds
    return datetime.fromtimestamp(timestamp).isoformat()


def timestampMs_to_timestampS(timestampMs: Union[str, int, float]) -> int:
    """Convert from timestamp in MILLISECONDS to timestamp in SECONDS"""
    in_seconds = round(float(timestampMs) / 1000)  # get timestamp in seconds
    return in_seconds


def timestampS_to_timestampMS(timestampMs: Union[str, int, float]) -> int:
    """Convert from timestamp in SECONDS to timestamp in MILIseconds"""
    in_seconds = round(float(timestampMs) * 1000)  # get timestamp in seconds
    return in_seconds


def partial_timestamp_parser(input_timestamp: str) -> str | None:
    """This method processes timestamp from files,
    where year is only represented by the last two digits (e.g. 18 instead of 2018)

    Paramters:
    input_timestamp: partial time stamp in the MM/DD/YY HH:MM:SS format.
    """
    date = input_timestamp[0:6] + datetime.today().strftime("%Y")[:2] + input_timestamp[6:8]
    time = input_timestamp[9:17]
    return datetime_to_iso_converter(time, date, "%m/%d/%Y %H:%M:%S")


def coordinates_to_string(latitude: float, longitude: float) -> str:
    """Convert location coordinates to single string with values separated by comma."""
    return f"{latitude}, {longitude}"


def e7_to_coordinates(latitudeE7: int, longitudeE7: int) -> Tuple[float, float]:
    """Convert location from E7 format to float numbers returned as tuple"""
    e7_divisor = 10**7
    latitude = latitudeE7 / e7_divisor
    longitude = longitudeE7 / e7_divisor
    if not -90 < latitude < 90:
        raise InvalidE7Geo(f"Latitude is not sane! {latitude}")
    if not -180 < longitude < 180:
        raise InvalidE7Geo(f"Longitude is not sane! {longitude}")
    return latitude, longitude
