from typing import Any, Generator, Optional


class InvalidE7Geo(Exception):
    pass


def find_keys_in_dict(node, key: str) -> Generator[Any, None, None]:
    """Finds all nodes with the specified key"""
    if isinstance(node, list):
        for i in node:
            for x in find_keys_in_dict(i, key):
                yield x
    elif isinstance(node, dict):
        if key in node:
            yield node[key]
        for j in node.values():
            for x in find_keys_in_dict(j, key):
                yield x


def peek_generator(iterable: Generator[Any, None, None]) -> Optional[dict]:
    """
    Tries whether the generator object is exhausted
    """
    try:
        return next(iterable)
    except StopIteration:
        return None


def get_value_from_dict(input_dictionary: dict) -> Optional[int]:
    """
    Returns "Value" from input dictionary
    """
    if input_dictionary is None:
        return input_dictionary
    return input_dictionary.get("Value", input_dictionary)
