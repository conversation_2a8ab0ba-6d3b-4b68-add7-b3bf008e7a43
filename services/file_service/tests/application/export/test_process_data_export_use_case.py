import asyncio
import json
import random
import zipfile
from datetime import datetime, timedelta, timezone
from io import BytesIO, <PERSON><PERSON>
from pathlib import Path
from typing import Any, AsyncGenerator, Awaitable, Callable, List, Type
from uuid import UUID, uuid4
from zipfile import ZipFile
from zoneinfo import ZoneInfo

import pandas as pd
import pytest

from services.base.application.assets import Assets
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.models.filter_types import <PERSON>tamp<PERSON>ange<PERSON><PERSON>er, UserUUIDTermsFilter
from services.base.application.database.models.filters import Filters
from services.base.application.event_models.takeout_export_scheduled_event_model import TakeoutExportScheduledModel
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.message_broker_client import MessageBrokerClient
from services.base.application.object_storage_service import ObjectStorageService
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.task_status import TaskStatus
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.repository.environment_repository import EnvironmentRepository
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.export_task_repository import ExportTaskRepository
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.repository.use_case_repository import UseCaseRepository
from services.base.domain.schemas.environment import Environment
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.feeling.stress import Stress, StressFields
from services.base.domain.schemas.export_task import ExportTask
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.metadata import DeprIdentifiableMetadataModel
from services.base.domain.schemas.resting_heart_rate import RestingHeartRate
from services.base.domain.schemas.sleep import Sleep
from services.base.tests.domain.builders.air_quality_input_builder import AirQualityInputBuilder
from services.base.tests.domain.builders.contact_builder import ContactBuilder
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.base.tests.domain.builders.extension_output_builder import ExtensionResultBuilder, ExtensionRunBuilder
from services.base.tests.domain.builders.heart_rate_builder import HeartRateBuilder
from services.base.tests.domain.builders.location_builder import LocationBuilder
from services.base.tests.domain.builders.plan_builder import PlanBuilder
from services.base.tests.domain.builders.pollen_input_builder import PollenInputBuilder
from services.base.tests.domain.builders.resting_hear_rate_builder import RestingHeartRateBuilder
from services.base.tests.domain.builders.sleep_builder import SleepBuilder
from services.base.tests.domain.builders.steps_builder import StepsBuilder
from services.base.tests.domain.builders.stress_builder import StressBuilder
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.base.tests.domain.builders.template.group_template_builder import GroupTemplateBuilder
from services.base.tests.domain.builders.use_case_builder import UseCaseBuilder
from services.base.tests.domain.builders.weather_input_builder import WeatherInputBuilder
from services.file_service.api.request_models.export_data_request_models import EnvironmentAggregationInterval
from services.file_service.application.enums.exportable_data_type import ExportableType
from services.file_service.application.use_cases.export.process_data_export_use_case import ProcessDataExportUseCase
from settings.extension_constants import SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID, TREND_INSIGHTS_EXTENSION_ID


async def _delete_user_documents(
    user_uuid: UUID, data_schemas: list[type[DeprIdentifiableMetadataModel]], depr_event_repo: DeprEventRepository
) -> None:
    filters = Filters()
    filters.must_filters.with_filters([UserUUIDTermsFilter(value=[str(user_uuid)])])
    await _call_event_delete(
        event_repo=depr_event_repo, user_uuid=user_uuid, data_schemas=data_schemas, filters=filters
    )


async def _call_event_delete(
    event_repo: DeprEventRepository,
    user_uuid: UUID,
    data_schemas: List[Type[DeprIdentifiableMetadataModel]],
    filters: Filters,
):
    [
        await event_repo.delete_by_query(data_schema=data_schema, user_uuid=user_uuid, filters=filters)
        for data_schema in data_schemas
    ]


async def read_all_from_json_files(zip_ref: ZipFile) -> List[dict[str, Any]]:
    read_models = []

    file_list = zip_ref.namelist()

    for file in file_list:
        if file.endswith(".json"):
            try:
                with zip_ref.open(file) as json_file:
                    models = json.load(json_file)
                    if isinstance(models, list):
                        read_models.extend(models)
                    else:
                        print(f"Warning: File {file} does not contain a list of models.")

            except json.JSONDecodeError as e:
                print(f"Error decoding JSON in file {file}: {e}")
            except Exception as e:
                raise IOError(f"Failed to read from {file}: {str(e)}")

    return read_models


@pytest.fixture
async def user_with_data(
    event_repository: EventRepository,
    depr_event_repository: DeprEventRepository,
    user_factory: Callable[[], Awaitable[MemberUser]],
) -> tuple[MemberUser, list[Stress]]:
    user: MemberUser = await user_factory()
    stress = StressBuilder().with_owner_id(user.user_uuid).build_n(100)
    events = await event_repository.insert(stress, force_strong_consistency=True)

    sorted_stress = sorted(stress, key=lambda x: x.timestamp)

    yield user, sorted_stress

    # Teardown
    await event_repository.delete_by_id(ids=[e.id for e in events])


@pytest.fixture
async def user_with_small_sample_all_data(
    event_repository: EventRepository,
    contact_repository: ContactRepository,
    depr_event_repository: DeprEventRepository,
    environment_repository: EnvironmentRepository,
    template_repository: TemplateRepository,
    plan_repository: PlanRepository,
    use_case_repository: UseCaseRepository,
    user_factory: Callable[[], Awaitable[MemberUser]],
) -> AsyncGenerator[tuple[MemberUser, list[Document]], None]:
    user: MemberUser = await user_factory()
    # V2 Data
    v2_data = []
    v2_data += await depr_event_repository.insert(
        models=[HeartRateBuilder().with_user_uuid(user_uuid=user.user_uuid).build() for _ in range(5)],
        force_strong_consistency=True,
    )
    v2_data += await depr_event_repository.insert(
        models=[RestingHeartRateBuilder().with_user_uuid(user_uuid=user.user_uuid).build() for _ in range(5)],
        force_strong_consistency=True,
    )
    v2_data += await depr_event_repository.insert(
        models=[SleepBuilder().with_user_uuid(user_uuid=user.user_uuid).build() for _ in range(5)],
        force_strong_consistency=True,
    )
    v2_data += await depr_event_repository.insert(
        models=[StepsBuilder().with_user_uuid(user_uuid=user.user_uuid).build() for _ in range(5)],
        force_strong_consistency=True,
    )

    # V3 Data
    events = EventBuilder().with_owner_id(owner_id=user.user_uuid).build_all()
    plans = PlanBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
    event_templates = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_all()
    group_templates = GroupTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
    templates = list(event_templates) + list(group_templates)
    use_cases = UseCaseBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
    events_saved = await event_repository.insert(events=events, force_strong_consistency=True)
    templates = await template_repository.insert(templates=templates, force_strong_consistency=True)
    plans = await plan_repository.insert(plans=plans, force_strong_consistency=True)
    use_cases = await use_case_repository.insert(use_cases=use_cases, force_strong_consistency=True)
    contacts = ContactBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
    contacts = await contact_repository.insert(contacts=contacts, force_strong_consistency=True)

    assert len(events) == len(events_saved)
    events = events_saved

    yield user, list(events) + list(plans) + list(templates) + list(use_cases) + list(v2_data) + list(contacts)

    # Teardown
    await event_repository.delete_by_id(ids=[e.id for e in events])
    await template_repository.delete_by_id(ids=[t.id for t in templates])
    await plan_repository.delete_by_id(ids=[p.id for p in plans])
    await use_case_repository.delete_by_id(ids=[ut.id for ut in use_cases])

    from services.base.domain.schemas.steps import Steps

    await _delete_user_documents(
        user_uuid=user.user_uuid,
        data_schemas=[
            HeartRate,
            RestingHeartRate,
            Sleep,
            Steps,
        ],
        depr_event_repo=depr_event_repository,
    )


@pytest.fixture
async def user_with_location_and_environment_data(
    environment_repository: EnvironmentRepository,
    depr_event_repository: DeprEventRepository,
    user_factory: Callable[[], Awaitable[MemberUser]],
) -> tuple[MemberUser, list[Location], list[Environment]]:
    user: MemberUser = await user_factory()
    locations = []
    for i in range(20):
        timestamp = PrimitiveTypesGenerator.generate_random_aware_datetime().astimezone(ZoneInfo("UTC"))
        locations.append(LocationBuilder().with_timestamp(timestamp).with_user_uuid(user.user_uuid).build())
    inserted_locations = await depr_event_repository.insert(models=locations, force_strong_consistency=True)
    aq_inputs = []
    pollen_inputs = []
    weather_inputs = []
    created_once = False
    for inserted_location in inserted_locations:
        if random.choice([True, False]) or not created_once:
            aq_inputs.append(
                AirQualityInputBuilder()
                .with_timestamp(inserted_location.timestamp)
                .with_coordinates(coordinates=inserted_location.start_coordinates)
                .build()
            )
            pollen_inputs.append(
                PollenInputBuilder()
                .with_timestamp(inserted_location.timestamp)
                .with_coordinates(coordinates=inserted_location.start_coordinates)
                .build()
            )
            weather_inputs.append(
                WeatherInputBuilder()
                .with_timestamp(inserted_location.timestamp)
                .with_coordinates(coordinates=inserted_location.start_coordinates)
                .build()
            )
            created_once = True

    inserted_environment = await environment_repository.insert(
        environment_documents=aq_inputs + pollen_inputs + weather_inputs, force_strong_consistency=True
    )
    await asyncio.sleep(1)

    yield user, locations, inserted_environment

    # Teardown
    await _delete_user_documents(
        user_uuid=user.user_uuid, data_schemas=[Location], depr_event_repo=depr_event_repository
    )
    await environment_repository.delete_by_id(ids_and_types=[(doc.id, type(doc)) for doc in inserted_environment])


@pytest.fixture
async def user_with_hourly_location_and_environment_data(
    environment_repository: EnvironmentRepository,
    depr_event_repository: DeprEventRepository,
    user_factory: Callable[[], Awaitable[MemberUser]],
) -> tuple[MemberUser, list[Location], list[Environment]]:
    user: MemberUser = await user_factory()
    locations = []

    # Choose a random base day and generate 24 location entries for each hour
    for day in range(2):
        base_timestamp = (
            PrimitiveTypesGenerator.generate_random_aware_datetime()
            .replace(hour=0, minute=0, second=0, microsecond=0)
            .astimezone(ZoneInfo("CET"))
        )
        for hour in range(24):
            timestamp = base_timestamp + timedelta(hours=hour)
            locations.append(LocationBuilder().with_timestamp(timestamp).with_user_uuid(user.user_uuid).build())

    inserted_locations = await depr_event_repository.insert(models=locations, force_strong_consistency=True)
    aq_inputs = []
    pollen_inputs = []
    weather_inputs = []

    for inserted_location in inserted_locations:
        aq_inputs.append(
            AirQualityInputBuilder()
            .with_timestamp(inserted_location.timestamp)
            .with_coordinates(coordinates=inserted_location.start_coordinates)
            .build()
        )
        pollen_inputs.append(
            PollenInputBuilder()
            .with_timestamp(inserted_location.timestamp)
            .with_coordinates(coordinates=inserted_location.start_coordinates)
            .build()
        )
        weather_inputs.append(
            WeatherInputBuilder()
            .with_timestamp(inserted_location.timestamp)
            .with_coordinates(coordinates=inserted_location.start_coordinates)
            .build()
        )

    inserted_environment = await environment_repository.insert(
        environment_documents=aq_inputs + pollen_inputs + weather_inputs, force_strong_consistency=True
    )

    await asyncio.sleep(1)
    yield user, locations, inserted_environment

    # Teardown
    await _delete_user_documents(
        user_uuid=user.user_uuid, data_schemas=[Location], depr_event_repo=depr_event_repository
    )
    await environment_repository.delete_by_id(ids_and_types=[(doc.id, type(doc)) for doc in inserted_environment])


@pytest.fixture
async def user_with_extension_output_data(
    user_factory: Callable[[], Awaitable[MemberUser]],
    extension_run_repo: ExtensionRunRepository,
    extension_result_repo: ExtensionResultRepository,
) -> tuple[MemberUser, list[UUID]]:
    user: MemberUser = await user_factory()
    timestamp = PrimitiveTypesGenerator.generate_random_aware_datetime()
    trend_run = (
        ExtensionRunBuilder()
        .with_result_status(True)
        .with_user_uuid(user_uuid=user.user_uuid)
        .with_extension_id(extension_id=TREND_INSIGHTS_EXTENSION_ID)
        .with_timestamp(timestamp)
        .build()
    )
    correlation_run = (
        ExtensionRunBuilder()
        .with_result_status(True)
        .with_user_uuid(user_uuid=user.user_uuid)
        .with_extension_id(extension_id=SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID)
        .with_timestamp(timestamp)
        .build()
    )
    folder_path = Path.joinpath(Path(__file__).resolve(strict=True).parent, "fixtures")
    with open(folder_path / "trend_output.json", "r") as file:
        trend_output = file.read()
    trend_result = (
        ExtensionResultBuilder()
        .with_result_status(True)
        .with_user_uuid(user_uuid=user.user_uuid)
        .with_extension_id(extension_id=TREND_INSIGHTS_EXTENSION_ID)
        .with_timestamp(timestamp)
        .with_output(trend_output)
    ).build()
    with open(folder_path / "corr_output.json", "r") as file:
        corr_output = file.read()
    corr_result = (
        ExtensionResultBuilder()
        .with_result_status(True)
        .with_user_uuid(user_uuid=user.user_uuid)
        .with_extension_id(extension_id=SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID)
        .with_timestamp(timestamp)
        .with_output(corr_output)
    ).build()
    inserted_runs = await extension_run_repo.insert(
        extension_runs=[trend_run, correlation_run], force_strong_consistency=True
    )
    inserted_trend_result = await extension_result_repo.insert(
        extension_results=[trend_result], parent_id=trend_run.id, force_strong_consistency=True
    )
    inserted_corr_result = await extension_result_repo.insert(
        extension_results=[corr_result], parent_id=correlation_run.id, force_strong_consistency=True
    )
    expected_ids = [run.id for run in inserted_runs] + [inserted_trend_result[0].id] + [inserted_corr_result[0].id]

    yield user, expected_ids

    # Teardown
    _ = await extension_run_repo.delete_by_id(ids=[run.id for run in inserted_runs])
    _ = await extension_result_repo.delete_by_id(
        ids=[result.id for result in list(inserted_trend_result) + list(inserted_corr_result)]
    )


async def test_data_export_use_case_hourly_location_and_environment_test(
    message_broker_client: MessageBrokerClient,
    object_storage_service: ObjectStorageService,
    depr_event_repository: DeprEventRepository,
    member_user_settings: MemberUserSettingsRepository,
    export_task_repository: ExportTaskRepository,
    data_export_use_case: ProcessDataExportUseCase,
    user_with_hourly_location_and_environment_data: tuple[MemberUser, list[Location], list[Environment]],
):
    user, location, environment = user_with_hourly_location_and_environment_data
    now = datetime.now(timezone.utc)
    takeout_name = Assets.generate_asset_id(name="export.zip")
    export_task = await export_task_repository.insert_or_update(
        task=ExportTask(id=uuid4(), user_id=user.user_uuid, status=TaskStatus.SCHEDULED, takeout_name=takeout_name)
    )

    export_name = await data_export_use_case.execute_async(
        input_object=TakeoutExportScheduledModel(
            user_uuid=user.user_uuid,
            data_types=[
                ExportableType.Location,
                ExportableType.AirQuality,
                ExportableType.Weather,
                ExportableType.Pollen,
            ],
            takeout_name=takeout_name,
            timestamp=now,
            user_timezone="CET",
            task_id=export_task.id,
            export_csv=True,
            environment_aggregation_interval=EnvironmentAggregationInterval.ONE_HOUR,
            system_run=False,
            storage_path=Assets.generate_user_storage_container_name(user_uuid=user.user_uuid),
        ),
    )

    object_name = Assets.generate_export_path(asset_id=export_name)
    container_name = Assets.generate_user_storage_container_name(user_uuid=user.user_uuid)
    file = await object_storage_service.get_object(container_name=container_name, object_name=object_name)

    with zipfile.ZipFile(BytesIO(file), "r") as zip_ref:
        file_list = zip_ref.namelist()
        for expected_file in [
            "json_documents/Location.json",
            "json_documents/Pollen.json",
            "json_documents/Weather.json",
            "json_documents/AirQuality.json",
        ]:
            assert expected_file in file_list
        exported_model_list = await read_all_from_json_files(zip_ref=zip_ref)
    assert len(exported_model_list) == 192  # 4 datatypes, 2 days, 24hours, 4*2*24 = 192
    export_task = await export_task_repository.get_by_id(task_id=export_task.id)
    assert export_task.status == TaskStatus.COMPLETED
    # cleanup
    await object_storage_service.delete_object(container_name=container_name, object_name=object_name)


async def test_data_export_use_case_location_and_environment_test(
    message_broker_client: MessageBrokerClient,
    object_storage_service: ObjectStorageService,
    depr_event_repository: DeprEventRepository,
    member_user_settings: MemberUserSettingsRepository,
    export_task_repository: ExportTaskRepository,
    data_export_use_case: ProcessDataExportUseCase,
    user_with_location_and_environment_data: tuple[MemberUser, list[Location], list[Environment]],
):
    user, location, environment = user_with_location_and_environment_data
    now = datetime.now(timezone.utc)
    takeout_name = Assets.generate_asset_id(name="export.zip")
    export_task = await export_task_repository.insert_or_update(
        task=ExportTask(id=uuid4(), user_id=user.user_uuid, status=TaskStatus.SCHEDULED, takeout_name=takeout_name)
    )

    export_name = await data_export_use_case.execute_async(
        input_object=TakeoutExportScheduledModel(
            user_uuid=user.user_uuid,
            data_types=[
                ExportableType.Location,
                ExportableType.AirQuality,
                ExportableType.Weather,
                ExportableType.Pollen,
            ],
            takeout_name=takeout_name,
            timestamp=now,
            user_timezone="CET",
            task_id=export_task.id,
            export_csv=True,
            environment_aggregation_interval=EnvironmentAggregationInterval.ONE_DAY,
            system_run=False,
            storage_path=Assets.generate_user_storage_container_name(user_uuid=user.user_uuid),
        ),
    )

    object_name = Assets.generate_export_path(asset_id=export_name)
    container_name = Assets.generate_user_storage_container_name(user_uuid=user.user_uuid)
    file = await object_storage_service.get_object(container_name=container_name, object_name=object_name)

    with zipfile.ZipFile(BytesIO(file), "r") as zip_ref:
        file_list = zip_ref.namelist()
        for expected_file in [
            "json_documents/Location.json",
            "json_documents/Pollen.json",
            "json_documents/Weather.json",
            "json_documents/AirQuality.json",
        ]:
            assert expected_file in file_list
        _ = await read_all_from_json_files(zip_ref=zip_ref)

    export_task = await export_task_repository.get_by_id(task_id=export_task.id)
    assert export_task.status == TaskStatus.COMPLETED
    # cleanup
    await object_storage_service.delete_object(container_name=container_name, object_name=object_name)


async def test_data_export_use_case_random_documents(
    message_broker_client: MessageBrokerClient,
    object_storage_service: ObjectStorageService,
    depr_event_repository: DeprEventRepository,
    member_user_settings: MemberUserSettingsRepository,
    export_task_repository: ExportTaskRepository,
    data_export_use_case: ProcessDataExportUseCase,
    user_with_data: tuple[MemberUser, list[Stress]],
):
    user, expected_stress = user_with_data
    now = datetime.now(timezone.utc)
    takeout_name = Assets.generate_asset_id(name="export.zip")
    export_task = await export_task_repository.insert_or_update(
        task=ExportTask(user_id=user.user_uuid, status=TaskStatus.SCHEDULED, takeout_name=takeout_name, id=uuid4())
    )

    export_name = await data_export_use_case.execute_async(
        input_object=TakeoutExportScheduledModel(
            user_uuid=user.user_uuid,
            data_types=[ExportableType.Stress],
            takeout_name=takeout_name,
            timestamp=now,
            user_timezone="UTC",
            task_id=export_task.id,
            export_csv=True,
            system_run=False,
            storage_path=Assets.generate_user_storage_container_name(user_uuid=user.user_uuid),
        ),
    )

    assert export_name
    assert datetime.fromtimestamp(float(export_name.split("-")[0]) / 1000)

    object_name = Assets.generate_export_path(asset_id=export_name)
    container_name = Assets.generate_user_storage_container_name(user_uuid=user.user_uuid)
    file = await object_storage_service.get_object(container_name=container_name, object_name=object_name)

    with zipfile.ZipFile(BytesIO(file), "r") as zip_ref:
        file_list = zip_ref.namelist()
        exported_model_list = await read_all_from_json_files(zip_ref=zip_ref)
        for file in file_list:
            if file.endswith(".csv"):
                with zip_ref.open(file) as csv_file:
                    csv_data = StringIO(csv_file.read().decode("utf-8"))
                    stress_csv = pd.read_csv(csv_data)

    exported_model_list = sorted(exported_model_list, key=lambda x: x.get(DocumentLabels.TIMESTAMP))
    assert len(exported_model_list) == len(expected_stress)
    for input_model, export_model in zip(expected_stress, exported_model_list):
        input_model: Stress
        assert input_model.timestamp == datetime.fromisoformat(export_model.get(DocumentLabels.TIMESTAMP))
        assert input_model.rating == export_model.get(StressFields.RATING)

    expected_timestamps = [stress.timestamp for stress in expected_stress]

    for value in stress_csv["timestamp"].values:
        # Parse and check against expected timestamps
        datetime_value = datetime.fromisoformat(value)
        assert datetime_value in expected_timestamps

    export_task = await export_task_repository.get_by_id(task_id=export_task.id)
    assert export_task
    assert export_task.status == TaskStatus.COMPLETED
    # cleanup
    await object_storage_service.delete_object(container_name=container_name, object_name=object_name)


async def test_data_export_use_case_range_filter(
    user_with_data: tuple[MemberUser, list[Stress]],
    object_storage_service: ObjectStorageService,
    message_broker_client: MessageBrokerClient,
    export_task_repository: ExportTaskRepository,
    member_user_settings: MemberUserSettingsRepository,
    depr_event_repository: DeprEventRepository,
    data_export_use_case: ProcessDataExportUseCase,
    gte: datetime = datetime(2021, 1, 1, tzinfo=ZoneInfo("UTC")),
    lte: datetime = datetime(2024, 1, 1, tzinfo=ZoneInfo("UTC")),
):
    range_filter: TimestampRangeFilter = TimestampRangeFilter(gte=gte, lte=lte)
    user, expected_stress = user_with_data
    expected_stress = [model for model in expected_stress if gte <= model.timestamp <= lte]

    now = datetime.now(timezone.utc)
    takeout_name = Assets.generate_asset_id(name="export.zip")
    export_task = await export_task_repository.insert_or_update(
        task=ExportTask(
            user_id=user.user_uuid,
            status=TaskStatus.SCHEDULED,
            takeout_name=takeout_name,
            id=uuid4(),
            completed_at=None,
        )
    )

    export_name = await data_export_use_case.execute_async(
        input_object=TakeoutExportScheduledModel(
            user_uuid=user.user_uuid,
            data_types=[ExportableType.Stress],
            takeout_name=takeout_name,
            timestamp=now,
            user_timezone="UTC",
            range_filter=range_filter,
            task_id=export_task.id,
            export_csv=False,
            system_run=False,
            storage_path=Assets.generate_user_storage_container_name(user_uuid=user.user_uuid),
        )
    )

    assert export_name
    assert datetime.fromtimestamp(float(export_name.split("-")[0]) / 1000)

    object_name = Assets.generate_export_path(asset_id=export_name)
    container_name = Assets.generate_user_storage_container_name(user_uuid=user.user_uuid)
    file = await object_storage_service.get_object(container_name=container_name, object_name=object_name)

    with zipfile.ZipFile(BytesIO(file), "r") as zip_ref:
        exported_model_list = await read_all_from_json_files(zip_ref=zip_ref)

    exported_model_list = sorted(exported_model_list, key=lambda x: x[0])
    assert len(exported_model_list) == len(expected_stress)
    for input_model, export_model in zip(expected_stress, exported_model_list):
        input_model: Stress
        assert input_model.timestamp == datetime.fromisoformat(export_model.get(DocumentLabels.TIMESTAMP))
        assert input_model.rating == export_model.get(StressFields.RATING)

    export_task = await export_task_repository.get_by_id(task_id=export_task.id)
    assert export_task
    assert export_task.status == TaskStatus.COMPLETED
    # cleanup
    await object_storage_service.delete_object(container_name=container_name, object_name=object_name)


async def test_data_export_use_case_small_sample_all_valid_data(
    object_storage_service: ObjectStorageService,
    export_task_repository: ExportTaskRepository,
    data_export_use_case: ProcessDataExportUseCase,
    user_with_small_sample_all_data,
):
    # Does not include environment data as that is covered in a different test
    user, documents = user_with_small_sample_all_data

    now = datetime.now(timezone.utc)
    takeout_name = Assets.generate_asset_id(name="export.zip")
    export_task = await export_task_repository.insert_or_update(
        task=ExportTask(
            user_id=user.user_uuid,
            status=TaskStatus.SCHEDULED,
            takeout_name=takeout_name,
            id=uuid4(),
            completed_at=None,
        )
    )

    export_name = await data_export_use_case.execute_async(
        input_object=TakeoutExportScheduledModel(
            user_uuid=user.user_uuid,
            data_types=[exp_type for exp_type in ExportableType],
            takeout_name=takeout_name,
            timestamp=now,
            user_timezone="UTC",
            task_id=export_task.id,
            export_csv=True,
            system_run=False,
            storage_path=Assets.generate_user_storage_container_name(user_uuid=user.user_uuid),
        ),
    )

    assert export_name
    assert datetime.fromtimestamp(float(export_name.split("-")[0]) / 1000)

    object_name = Assets.generate_export_path(asset_id=export_name)
    container_name = Assets.generate_user_storage_container_name(user_uuid=user.user_uuid)
    file = await object_storage_service.get_object(container_name=container_name, object_name=object_name)

    with zipfile.ZipFile(BytesIO(file), "r") as zip_ref:
        exported_model_list = await read_all_from_json_files(zip_ref=zip_ref)

    expected_count = len(documents)
    exported_count = len(exported_model_list)
    assert expected_count == exported_count

    export_task = await export_task_repository.get_by_id(task_id=export_task.id)
    assert export_task
    assert export_task.status == TaskStatus.COMPLETED
    # cleanup
    await object_storage_service.delete_object(container_name=container_name, object_name=object_name)
    await export_task_repository.delete(task=export_task)


async def test_data_export_with_extension_output_data(
    user_with_extension_output_data: tuple[MemberUser, list[UUID]],
    object_storage_service: ObjectStorageService,
    message_broker_client: MessageBrokerClient,
    export_task_repository: ExportTaskRepository,
    member_user_settings: MemberUserSettingsRepository,
    data_export_use_case: ProcessDataExportUseCase,
):
    user, expected_ids = user_with_extension_output_data

    now = datetime.now(timezone.utc)
    takeout_name = Assets.generate_asset_id(name="export.zip")
    export_task = await export_task_repository.insert_or_update(
        task=ExportTask(user_id=user.user_uuid, status=TaskStatus.SCHEDULED, takeout_name=takeout_name, id=uuid4())
    )
    storage_path = "system-storage"

    export_name = await data_export_use_case.execute_async(
        input_object=TakeoutExportScheduledModel(
            user_uuid=user.user_uuid,
            data_types=[ExportableType.ExtensionRun],
            takeout_name=takeout_name,
            timestamp=now,
            user_timezone="UTC",
            task_id=export_task.id,
            export_csv=True,
            system_run=True,
            storage_path=storage_path,
        ),
    )

    assert export_name
    assert datetime.fromtimestamp(float(export_name.split("-")[0]) / 1000)

    object_name = Assets.generate_export_path(asset_id=export_name)
    container_name = storage_path
    file = await object_storage_service.get_object(container_name=container_name, object_name=object_name)

    with zipfile.ZipFile(BytesIO(file), "r") as zip_ref:
        exported_model_list = await read_all_from_json_files(zip_ref=zip_ref)

    found_ids = [found_model.get(DocumentLabels.ID) for found_model in exported_model_list]
    assert len(found_ids) == len(expected_ids)
    for found_id in found_ids:
        assert UUID(found_id) in expected_ids
        expected_ids.remove(UUID(found_id))

    # Does not test the conversion to CSV is valid

    assert not expected_ids
    export_task = await export_task_repository.get_by_id(task_id=export_task.id)
    assert export_task.status == TaskStatus.COMPLETED
    # cleanup
    await object_storage_service.delete_object(container_name=container_name, object_name=object_name)
    await export_task_repository.delete(task=export_task)
