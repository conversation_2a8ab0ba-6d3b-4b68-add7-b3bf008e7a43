import asyncio
import random
import zipfile
from io import BytesIO
from typing import Awaitable, Callable, List, Type
from urllib.parse import parse_qs
from uuid import UUID

import pytest
from httpx import ASGITransport, AsyncClient
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.assets import Assets
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.models.filter_types import UserUUIDTermsFilter
from services.base.application.database.models.filters import Filters
from services.base.application.object_storage_service import ObjectStorageService
from services.base.application.retry import retry_until_value
from services.base.application.utils.urls import join_as_url
from services.base.domain.enums.task_status import TaskStatus
from services.base.domain.repository.environment_repository import EnvironmentRepository
from services.base.domain.repository.export_task_repository import ExportTaskRepository
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.metadata import DeprIdentifiableMetadataModel
from services.base.tests.domain.builders.air_quality_input_builder import AirQualityInputBuilder
from services.base.tests.domain.builders.location_builder import LocationBuilder
from services.base.tests.domain.builders.pollen_input_builder import PollenInputBuilder
from services.base.tests.domain.builders.weather_input_builder import WeatherInputBuilder
from services.file_service.api.response_models.fetch_export_url_response import FetchExportUrlResponse
from services.file_service.api.urls import DataExportEndpointUrls
from services.file_service.main import app


async def _delete_user_documents(
    user_uuid: UUID, data_schema: Type[DeprIdentifiableMetadataModel], event_repo: DeprEventRepository
) -> None:
    filters = Filters()
    filters.must_filters.with_filters([UserUUIDTermsFilter(value=[str(user_uuid)])])
    await _call_event_delete(event_repo=event_repo, user_uuid=user_uuid, data_schemas=[data_schema], filters=filters)


async def _call_event_delete(
    event_repo: DeprEventRepository,
    user_uuid: UUID,
    data_schemas: List[Type[DeprIdentifiableMetadataModel]],
    filters: Filters,
):
    [
        await event_repo.delete_by_query(data_schema=data_schema, user_uuid=user_uuid, filters=filters)
        for data_schema in data_schemas
    ]


@pytest.fixture
async def user_with_location_and_environment_data(
    environment_repository: EnvironmentRepository,
    depr_event_repository: DeprEventRepository,
    user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
) -> tuple[MemberUser, dict]:
    user, headers = await user_headers_factory()
    locations = [LocationBuilder().with_user_uuid(user_uuid=user.user_uuid).build() for _ in range(20)]
    inserted_locations = await depr_event_repository.insert(models=locations, force_strong_consistency=True)
    aq_inputs = []
    pollen_inputs = []
    weather_inputs = []
    created_once = False
    for inserted_location in inserted_locations:
        if random.choice([True, False]) or not created_once:
            aq_inputs.append(
                AirQualityInputBuilder()
                .with_timestamp(inserted_location.timestamp)
                .with_coordinates(coordinates=inserted_location.start_coordinates)
                .build()
            )
            pollen_inputs.append(
                PollenInputBuilder()
                .with_timestamp(inserted_location.timestamp)
                .with_coordinates(coordinates=inserted_location.start_coordinates)
                .build()
            )
            weather_inputs.append(
                WeatherInputBuilder()
                .with_timestamp(inserted_location.timestamp)
                .with_coordinates(coordinates=inserted_location.start_coordinates)
                .build()
            )
            created_once = True

    inserted_environment = await environment_repository.insert(
        environment_documents=aq_inputs + pollen_inputs + weather_inputs, force_strong_consistency=True
    )

    await asyncio.sleep(1)
    yield user, headers

    # Teardown
    await _delete_user_documents(user_uuid=user.user_uuid, data_schema=Location, event_repo=depr_event_repository)
    await environment_repository.delete_by_id(ids_and_types=[(doc.id, type(doc)) for doc in inserted_environment])


async def test_schedule_data_export_endpoint(
    user_factory,
    object_storage_service: ObjectStorageService,
    export_task_repository: ExportTaskRepository,
):
    # Arrange
    user = await user_factory()
    headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
    request_url = join_as_url(DataExportEndpointUrls.SCHEDULE, {})

    # Act
    client = AsyncClient(transport=ASGITransport(app=app), base_url="http://test")

    response = await client.post(request_url, headers=headers)

    # Assert
    assert response.status_code == status.HTTP_200_OK
    output = response.json()
    task_id = output["message"]

    # Wait for object to be downloadable
    container_name = Assets.generate_user_storage_container_name(user_uuid=user.user_uuid)

    @retry_until_value(value_validator=lambda x: x.status == TaskStatus.COMPLETED, delay=5, max_times=10)
    async def await_export_completed():
        return await export_task_repository.get_by_id(task_id=task_id)

    task = await await_export_completed()

    request_url = join_as_url(DataExportEndpointUrls.URL, {"export_ids": [task.takeout_name]})

    # Act
    response = await client.get(request_url, headers=headers)
    assert response.status_code == status.HTTP_200_OK
    output = FetchExportUrlResponse(**response.json())
    export_url = output.exports[task.takeout_name]

    credentials = await object_storage_service.generate_container_access_credentials(container_name=container_name)
    response = await AsyncClient().get(
        url=join_as_url(
            base_url=export_url.unicode_string(),
            query_params={k: v[0] if v else None for k, v in parse_qs(credentials).items()},
        )
    )

    assert response.status_code == status.HTTP_200_OK


async def test_schedule_data_export_random_environment_user_data_endpoint(
    user_with_location_and_environment_data,
    object_storage_service: ObjectStorageService,
    export_task_repository: ExportTaskRepository,
):
    # Arrange
    user, headers = user_with_location_and_environment_data
    user_uuid = user.user_uuid
    request_url = join_as_url(DataExportEndpointUrls.SCHEDULE, {"environment_aggregation_interval": "1d"})
    client = AsyncClient(transport=ASGITransport(app=app), base_url="http://test")
    # Act
    response = await client.post(request_url, headers=headers)

    # Assert
    assert response.status_code == status.HTTP_200_OK
    output = response.json()
    task_id = output["message"]

    # Wait for object to be downloadable
    container_name = Assets.generate_user_storage_container_name(user_uuid=user_uuid)

    @retry_until_value(value_validator=lambda x: x.status == TaskStatus.COMPLETED, delay=5, max_times=10)
    async def await_export_completed():
        return await export_task_repository.get_by_id(task_id=task_id)

    task = await await_export_completed()

    request_url = join_as_url(DataExportEndpointUrls.URL, {"export_ids": [task.takeout_name]})

    # Act
    response = await client.get(request_url, headers=headers)
    assert response.status_code == status.HTTP_200_OK
    output = FetchExportUrlResponse(**response.json())
    export_url = output.exports[task.takeout_name]

    credentials = await object_storage_service.generate_container_access_credentials(container_name=container_name)
    async with AsyncClient() as async_client:
        response = await async_client.get(
            url=join_as_url(
                base_url=export_url.unicode_string(),
                query_params={k: v[0] if v else None for k, v in parse_qs(credentials).items()},
            )
        )

    assert response.status_code == status.HTTP_200_OK

    object_name = Assets.generate_export_path(asset_id=task.takeout_name)
    container_name = Assets.generate_user_storage_container_name(user_uuid=user.user_uuid)
    file = await object_storage_service.get_object(container_name=container_name, object_name=object_name)

    with zipfile.ZipFile(BytesIO(file), "r") as zip_ref:
        file_list = zip_ref.namelist()
        for expected_folder in [
            "json_documents/Location.json",
            "json_documents/Pollen.json",
            "json_documents/Weather.json",
            "json_documents/AirQuality.json",
        ]:
            assert expected_folder in file_list

    export_task = await export_task_repository.get_by_id(task_id=task.id)
    assert export_task.status == TaskStatus.COMPLETED
    # cleanup
    await object_storage_service.delete_object(container_name=container_name, object_name=object_name)
